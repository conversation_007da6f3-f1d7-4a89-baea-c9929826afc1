package com.anytech.anytxn.parameter.card.service;


import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardBinControlMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardBinControlSelfMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardBinDefinitionMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardBinDefinitionSelfMapper;
import com.anytech.anytxn.parameter.card.mapper.unicast.BinCardNumberUsedSelfMapper;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardBinControlReqDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardBinControlResDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardBinDefinitionReqDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardBinDefinitionResDTO;
import com.anytech.anytxn.parameter.base.card.domain.model.BinCardNumberUsed;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardBinControl;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardBinDefinition;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * CardBinDefinitionServiceTest test class
 * 
 * <AUTHOR> Engineer
 * @date 2025-01-23
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class CardBinDefinitionServiceTest {

    @Mock
    private ParmCardBinControlMapper cardBinControlMapper;
    
    @Mock
    private ParmCardBinControlSelfMapper cardBinControlSelfMapper;
    
    @Mock
    private BinCardNumberUsedSelfMapper binCardNumberUsedSelfMapper;
    
    @Mock
    private ParmCardBinDefinitionMapper cardBinDefinitionMapper;
    
    @Mock
    private ParmCardBinDefinitionSelfMapper cardBinDefinitionSelfMapper;
    
    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private CardBinDefinitionServiceImpl cardBinDefinitionService;

    private ParmCardBinDefinition parmCardBinDefinition;
    private CardBinDefinitionReqDTO cardBinDefinitionReqDTO;
    private CardBinDefinitionResDTO cardBinDefinitionResDTO;
    private ParmCardBinControl parmCardBinControl;
    private CardBinControlReqDTO cardBinControlReqDTO;
    private CardBinControlResDTO cardBinControlResDTO;
    private BinCardNumberUsed binCardNumberUsed;

    @BeforeEach
    void setUp() {
        // Mock OrgNumberUtils static method to avoid NPE in BaseParam constructor
        try (MockedStatic<OrgNumberUtils> mockOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockOrgNumberUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("1001");
            
            // 创建测试实体对象
            parmCardBinDefinition = new ParmCardBinDefinition();
            parmCardBinDefinition.setId("1");
            parmCardBinDefinition.setOrganizationNumber("1001");
            parmCardBinDefinition.setTableId("BIN001");
            parmCardBinDefinition.setDescription("Test BIN Definition");
            parmCardBinDefinition.setStatus("1");
            parmCardBinDefinition.setEndNumberAlarm(100);
            parmCardBinDefinition.setCreateTime(LocalDateTime.now());
            parmCardBinDefinition.setUpdateTime(LocalDateTime.now());
            parmCardBinDefinition.setVersionNumber(1L);

            // 创建测试请求DTO对象
            cardBinDefinitionReqDTO = new CardBinDefinitionReqDTO();
            cardBinDefinitionReqDTO.setId("1");
            cardBinDefinitionReqDTO.setOrganizationNumber("1001");
            cardBinDefinitionReqDTO.setTableId("BIN001");
            cardBinDefinitionReqDTO.setDescription("Test BIN Definition");
            cardBinDefinitionReqDTO.setStatus("1");
            cardBinDefinitionReqDTO.setEndNumberAlarm(100);
            cardBinDefinitionReqDTO.setUpdateTime(LocalDateTime.now());
            cardBinDefinitionReqDTO.setUpdateBy("testUser");
            cardBinDefinitionReqDTO.setVersionNumber(1L);

            // 创建CardBinControl测试对象
            cardBinControlReqDTO = new CardBinControlReqDTO();
            cardBinControlReqDTO.setId(1L);
            cardBinControlReqDTO.setOrganizationNumber("1001");
            cardBinControlReqDTO.setTableId("BIN001");
            cardBinControlReqDTO.setBinSequence("001");
            cardBinControlReqDTO.setBinNumber("123456");
            cardBinControlReqDTO.setBinSource("C");
            cardBinControlReqDTO.setStartCardNumber("1234560000000000");
            cardBinControlReqDTO.setEndCardNumber("1234569999999999");
            cardBinControlReqDTO.setEmergencyStartCard("1234560000000000");
            cardBinControlReqDTO.setEmergencyEndCard("1234560000009999");
            cardBinControlReqDTO.setStatus("1");
            cardBinControlReqDTO.setUpdateTime(LocalDateTime.now());
            cardBinControlReqDTO.setUpdateBy("testUser");
            cardBinControlReqDTO.setLastUsedNumber("1234560000000001");

            cardBinDefinitionReqDTO.setCardBinControlList(Arrays.asList(cardBinControlReqDTO));

            // 创建测试响应DTO对象
            cardBinDefinitionResDTO = new CardBinDefinitionResDTO();
            cardBinDefinitionResDTO.setId("1");
            cardBinDefinitionResDTO.setOrganizationNumber("1001");
            cardBinDefinitionResDTO.setTableId("BIN001");
            cardBinDefinitionResDTO.setDescription("Test BIN Definition");
            cardBinDefinitionResDTO.setStatus("1");
            cardBinDefinitionResDTO.setEndNumberAlarm(100);
            cardBinDefinitionResDTO.setCreateTime(LocalDateTime.now());
            cardBinDefinitionResDTO.setUpdateTime(LocalDateTime.now());
            cardBinDefinitionResDTO.setUpdateBy("testUser");
            cardBinDefinitionResDTO.setVersionNumber(1L);

            // 创建CardBinControlResDTO测试对象
            cardBinControlResDTO = new CardBinControlResDTO();
            cardBinControlResDTO.setId(1L);
            cardBinControlResDTO.setOrganizationNumber("1001");
            cardBinControlResDTO.setTableId("BIN001");
            cardBinControlResDTO.setBinSequence("001");
            cardBinControlResDTO.setBinNumber("123456");
            cardBinControlResDTO.setBinSource("C");
            cardBinControlResDTO.setStartCardNumber("1234560000000000");
            cardBinControlResDTO.setEndCardNumber("1234569999999999");
            cardBinControlResDTO.setEmergencyStartCard("1234560000000000");
            cardBinControlResDTO.setEmergencyEndCard("1234560000009999");
            cardBinControlResDTO.setStatus("1");
            cardBinControlResDTO.setUpdateTime(LocalDateTime.now());
            cardBinControlResDTO.setUpdateBy("testUser");
            cardBinControlResDTO.setLastUsedNumber("1234560000000001");

            cardBinDefinitionResDTO.setCardBinControlList(Arrays.asList(cardBinControlResDTO));

            // 创建ParmCardBinControl测试对象
            parmCardBinControl = new ParmCardBinControl();
            parmCardBinControl.setId(1L);
            parmCardBinControl.setOrganizationNumber("1001");
            parmCardBinControl.setTableId("BIN001");
            parmCardBinControl.setBinSequence("001");
            parmCardBinControl.setBinNumber("123456");
            parmCardBinControl.setBinSource("C");
            parmCardBinControl.setStartCardNumber("1234560000000000");
            parmCardBinControl.setEndCardNumber("1234569999999999");
            parmCardBinControl.setEmergencyStartCard("1234560000000000");
            parmCardBinControl.setEmergencyEndCard("1234560000009999");
            parmCardBinControl.setStatus("1");
            parmCardBinControl.setCreateTime(LocalDateTime.now());
            parmCardBinControl.setUpdateTime(LocalDateTime.now());
            parmCardBinControl.setUpdateBy("testUser");
            parmCardBinControl.setVersionNumber(1);

            // 创建BinCardNumberUsed测试对象
            binCardNumberUsed = new BinCardNumberUsed();
            binCardNumberUsed.setOrganizationNumber("1001");
            binCardNumberUsed.setTableId("BIN001");
            binCardNumberUsed.setBinSequence("001");
            binCardNumberUsed.setLastUsedNumber("1234560000000001");
        }
    }

    @Test
    void testAdd_Success() {
        // Arrange
        Mockito.lenient().when(cardBinDefinitionSelfMapper.isExists("1001", "BIN001")).thenReturn(false);
        Mockito.lenient().when(numberIdGenerator.generateId(anyString())).thenReturn(123456789L);
        
        try (MockedStatic<OrgNumberUtils> mockOrgNumberUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class)) {
            
            mockOrgNumberUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("1001");
            mockTenantUtils.when(() -> TenantUtils.getTenantId()).thenReturn("tenant1");

            // Act
            ParameterCompare result = cardBinDefinitionService.add(cardBinDefinitionReqDTO);

            // Assert
            assertNotNull(result);
            assertEquals("BIN001", result.getMainParmId());
            verify(cardBinDefinitionSelfMapper).isExists("1001", "BIN001");
            verify(numberIdGenerator).generateId("tenant1");
        }
    }

    @Test
    void testAdd_AlreadyExists() {
        // Arrange
        Mockito.lenient().when(cardBinDefinitionSelfMapper.isExists("1001", "BIN001")).thenReturn(true);
        
        try (MockedStatic<OrgNumberUtils> mockOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockOrgNumberUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("1001");

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
                () -> cardBinDefinitionService.add(cardBinDefinitionReqDTO));
            
            assertEquals(AnyTxnParameterRespCodeEnum.P_EXIST_BLOCK_CODE_AND_ID_FAULT.getCode(), exception.getErrCode());
            verify(cardBinDefinitionSelfMapper).isExists("1001", "BIN001");
        }
    }
 
    @Test
    void testModify_Success() {
        // Arrange
        cardBinDefinitionReqDTO.setId("1");
        Mockito.lenient().when(cardBinDefinitionMapper.selectByPrimaryKey("1")).thenReturn(parmCardBinDefinition);
        Mockito.lenient().when(cardBinControlSelfMapper.selectAll("1001", "BIN001")).thenReturn(Arrays.asList(parmCardBinControl));
        
        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {
            mockBeanMapping.when(() -> BeanMapping.copy(parmCardBinDefinition, CardBinDefinitionResDTO.class))
                          .thenReturn(cardBinDefinitionResDTO);
            mockBeanMapping.when(() -> BeanMapping.copyList(Arrays.asList(parmCardBinControl), CardBinControlResDTO.class))
                          .thenReturn(Arrays.asList(cardBinControlResDTO));
            mockBeanMapping.when(() -> BeanMapping.copy(cardBinDefinitionResDTO, CardBinDefinitionReqDTO.class))
                          .thenReturn(cardBinDefinitionReqDTO);

            // Act
            ParameterCompare result = cardBinDefinitionService.modify(cardBinDefinitionReqDTO);

            // Assert
            assertNotNull(result);
            assertEquals("BIN001", result.getMainParmId());
            verify(cardBinDefinitionMapper).selectByPrimaryKey("1");
        }
    }

    @Test
    void testModify_EmptyId() {
        // Arrange
        cardBinDefinitionReqDTO.setId(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> cardBinDefinitionService.modify(cardBinDefinitionReqDTO));
        
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT.getCode(), exception.getErrCode());
        verify(cardBinDefinitionMapper, never()).selectByPrimaryKey(any());
    }

    @Test
    void testModify_NotFound() {
        // Arrange
        cardBinDefinitionReqDTO.setId("999");
        Mockito.lenient().when(cardBinDefinitionMapper.selectByPrimaryKey("999")).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> cardBinDefinitionService.modify(cardBinDefinitionReqDTO));
        
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
        verify(cardBinDefinitionMapper).selectByPrimaryKey("999");
    }

    @Test
    void testFindPage_Success() {
        // Arrange
        List<ParmCardBinDefinition> dataList = Arrays.asList(parmCardBinDefinition);
        Mockito.lenient().when(cardBinDefinitionSelfMapper.selectPageByCondition("1001", "BIN001", "Test")).thenReturn(dataList);
        
        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class);
             MockedStatic<OrgNumberUtils> mockOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockOrgNumberUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("1001");
            mockBeanMapping.when(() -> BeanMapping.copyList(dataList, CardBinDefinitionResDTO.class))
                          .thenReturn(Arrays.asList(cardBinDefinitionResDTO));

            // Act
            PageResultDTO<CardBinDefinitionResDTO> result = cardBinDefinitionService.findPage(1, 10, "1001", "BIN001", "Test");

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getPage());
            assertEquals(10, result.getRows());
            assertEquals(1, result.getData().size());
            assertEquals("1", result.getData().get(0).getId());
            verify(cardBinDefinitionSelfMapper).selectPageByCondition("1001", "BIN001", "Test");
        }
    }

    @Test
    void testFindPage_EmptyOrgNum() {
        // Arrange
        List<ParmCardBinDefinition> dataList = Arrays.asList(parmCardBinDefinition);
        Mockito.lenient().when(cardBinDefinitionSelfMapper.selectPageByCondition("1001", null, null)).thenReturn(dataList);
        
        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class);
             MockedStatic<OrgNumberUtils> mockOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockOrgNumberUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("1001");
            mockBeanMapping.when(() -> BeanMapping.copyList(dataList, CardBinDefinitionResDTO.class))
                          .thenReturn(Arrays.asList(cardBinDefinitionResDTO));

            // Act
            PageResultDTO<CardBinDefinitionResDTO> result = cardBinDefinitionService.findPage(1, 10, null, null, null);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getData().size());
            verify(cardBinDefinitionSelfMapper).selectPageByCondition("1001", null, null);
        }
    }

    @Test
    void testFind_Success() {
        // Arrange
        Mockito.lenient().when(cardBinDefinitionMapper.selectByPrimaryKey("1")).thenReturn(parmCardBinDefinition);
        Mockito.lenient().when(cardBinControlSelfMapper.selectAll("1001", "BIN001")).thenReturn(Arrays.asList(parmCardBinControl));
        
        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class);
             MockedStatic<OrgNumberUtils> mockOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockOrgNumberUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("1001");
            mockBeanMapping.when(() -> BeanMapping.copy(parmCardBinDefinition, CardBinDefinitionResDTO.class))
                          .thenReturn(cardBinDefinitionResDTO);
            mockBeanMapping.when(() -> BeanMapping.copyList(Arrays.asList(parmCardBinControl), CardBinControlResDTO.class))
                          .thenReturn(Arrays.asList(cardBinControlResDTO));

            // Act
            CardBinDefinitionResDTO result = cardBinDefinitionService.find("1");

            // Assert
            assertNotNull(result);
            assertEquals("1", result.getId());
            assertEquals("BIN001", result.getTableId());
            assertEquals(1, result.getCardBinControlList().size());
            verify(cardBinDefinitionMapper).selectByPrimaryKey("1");
            verify(cardBinControlSelfMapper).selectAll("1001", "BIN001");
        }
    }

    @Test
    void testFind_EmptyId() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> cardBinDefinitionService.find(null));
        
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
        verify(cardBinDefinitionMapper, never()).selectByPrimaryKey(any());
    }

    @Test
    void testFind_NotFound() {
        // Arrange
        Mockito.lenient().when(cardBinDefinitionMapper.selectByPrimaryKey("999")).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> cardBinDefinitionService.find("999"));
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_GET_CARD_BIN_DEFINITION_BY_ID_FAULT.getCode(), exception.getErrCode());
        verify(cardBinDefinitionMapper).selectByPrimaryKey("999");
    }

    @Test
    void testFind_WithEmptyCardBinControlList() {
        // Arrange
        Mockito.lenient().when(cardBinDefinitionMapper.selectByPrimaryKey("1")).thenReturn(parmCardBinDefinition);
        Mockito.lenient().when(cardBinControlSelfMapper.selectAll("1001", "BIN001")).thenReturn(Collections.emptyList());
        
        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {
            mockBeanMapping.when(() -> BeanMapping.copy(parmCardBinDefinition, CardBinDefinitionResDTO.class))
                          .thenReturn(cardBinDefinitionResDTO);

            // Act
            CardBinDefinitionResDTO result = cardBinDefinitionService.find("1");

            // Assert
            assertNotNull(result);
            assertEquals("1", result.getId());
            verify(cardBinDefinitionMapper).selectByPrimaryKey("1");
            verify(cardBinControlSelfMapper).selectAll("1001", "BIN001");
        }
    }

    @Test
    void testFind_WithBinCardNumberUsedData() {
        // Arrange
        Mockito.lenient().when(cardBinDefinitionMapper.selectByPrimaryKey("1")).thenReturn(parmCardBinDefinition);
        Mockito.lenient().when(cardBinControlSelfMapper.selectAll("1001", "BIN001")).thenReturn(Arrays.asList(parmCardBinControl));
        Mockito.lenient().when(binCardNumberUsedSelfMapper.selectLastUsedNumberByIndex("1001", "BIN001", "001")).thenReturn(binCardNumberUsed);
        
        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {
            mockBeanMapping.when(() -> BeanMapping.copy(parmCardBinDefinition, CardBinDefinitionResDTO.class))
                          .thenReturn(cardBinDefinitionResDTO);
            mockBeanMapping.when(() -> BeanMapping.copyList(Arrays.asList(parmCardBinControl), CardBinControlResDTO.class))
                          .thenReturn(Arrays.asList(cardBinControlResDTO));

            // Act
            CardBinDefinitionResDTO result = cardBinDefinitionService.find("1");

            // Assert
            assertNotNull(result);
            assertEquals("1", result.getId());
            assertEquals("BIN001", result.getTableId());
            assertEquals(1, result.getCardBinControlList().size());
            assertEquals("1234560000000001", result.getCardBinControlList().get(0).getLastUsedNumber());
            verify(cardBinDefinitionMapper).selectByPrimaryKey("1");
            verify(cardBinControlSelfMapper).selectAll("1001", "BIN001");
            verify(binCardNumberUsedSelfMapper).selectLastUsedNumberByIndex("1001", "BIN001", "001");
        }
    }

    @Test
    void testFind_WithNullBinCardNumberUsedData() {
        // Arrange
        Mockito.lenient().when(cardBinDefinitionMapper.selectByPrimaryKey("1")).thenReturn(parmCardBinDefinition);
        Mockito.lenient().when(cardBinControlSelfMapper.selectAll("1001", "BIN001")).thenReturn(Arrays.asList(parmCardBinControl));
        Mockito.lenient().when(binCardNumberUsedSelfMapper.selectLastUsedNumberByIndex("1001", "BIN001", "001")).thenReturn(null);
        
        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {
            mockBeanMapping.when(() -> BeanMapping.copy(parmCardBinDefinition, CardBinDefinitionResDTO.class))
                          .thenReturn(cardBinDefinitionResDTO);
            mockBeanMapping.when(() -> BeanMapping.copyList(Arrays.asList(parmCardBinControl), CardBinControlResDTO.class))
                          .thenReturn(Arrays.asList(cardBinControlResDTO));

            // Act
            CardBinDefinitionResDTO result = cardBinDefinitionService.find("1");

            // Assert
            assertNotNull(result);
            assertEquals("1", result.getId());
            assertEquals("BIN001", result.getTableId());
            assertEquals(1, result.getCardBinControlList().size());
            assertEquals("0", result.getCardBinControlList().get(0).getLastUsedNumber()); // Default value is "0"
            verify(cardBinDefinitionMapper).selectByPrimaryKey("1");
            verify(cardBinControlSelfMapper).selectAll("1001", "BIN001");
            verify(binCardNumberUsedSelfMapper).selectLastUsedNumberByIndex("1001", "BIN001", "001");
        }
    }

    @Test
    void testFindByOrgAndTableId_Success() {
        // Arrange
        Mockito.lenient().when(cardBinDefinitionSelfMapper.selectByOrgAndTableId("1001", "BIN001")).thenReturn(parmCardBinDefinition);
        Mockito.lenient().when(cardBinControlSelfMapper.selectAll("1001", "BIN001")).thenReturn(Arrays.asList(parmCardBinControl));
        
        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {
            mockBeanMapping.when(() -> BeanMapping.copy(parmCardBinDefinition, CardBinDefinitionResDTO.class))
                          .thenReturn(cardBinDefinitionResDTO);
            mockBeanMapping.when(() -> BeanMapping.copyList(Arrays.asList(parmCardBinControl), CardBinControlResDTO.class))
                          .thenReturn(Arrays.asList(cardBinControlResDTO));

            // Act
            CardBinDefinitionResDTO result = cardBinDefinitionService.findByOrgAndTableId("1001", "BIN001");

            // Assert
            assertNotNull(result);
            assertEquals("1", result.getId());
            assertEquals("BIN001", result.getTableId());
            assertEquals(1, result.getCardBinControlList().size());
            verify(cardBinDefinitionSelfMapper).selectByOrgAndTableId("1001", "BIN001");
            verify(cardBinControlSelfMapper).selectAll("1001", "BIN001");
        }
    }

    @Test
    void testFindByOrgAndTableId_EmptyOrganizationNumber() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> cardBinDefinitionService.findByOrgAndTableId("", "BIN001"));
        
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
        verify(cardBinDefinitionSelfMapper, never()).selectByOrgAndTableId(any(), any());
    }

    @Test
    void testFindByOrgAndTableId_EmptyTableId() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> cardBinDefinitionService.findByOrgAndTableId("1001", ""));
        
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
        verify(cardBinDefinitionSelfMapper, never()).selectByOrgAndTableId(any(), any());
    }

    @Test
    void testFindByOrgAndTableId_NotFound() {
        // Arrange
        Mockito.lenient().when(cardBinDefinitionSelfMapper.selectByOrgAndTableId("1001", "BIN999")).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> cardBinDefinitionService.findByOrgAndTableId("1001", "BIN999"));
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_GET_CARD_BIN_DEFINITION_BY_ID_FAULT.getCode(), exception.getErrCode());
        verify(cardBinDefinitionSelfMapper).selectByOrgAndTableId("1001", "BIN999");
    }

    @Test
    void testRemove_Success() {
        // Arrange
        Mockito.lenient().when(cardBinDefinitionMapper.selectByPrimaryKey("1")).thenReturn(parmCardBinDefinition);

        // Act
        ParameterCompare result = cardBinDefinitionService.remove("1");

        // Assert
        assertNotNull(result);
        assertEquals("BIN001", result.getMainParmId());
        verify(cardBinDefinitionMapper).selectByPrimaryKey("1");
    }

    @Test
    void testRemove_EmptyId() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> cardBinDefinitionService.remove(null));
        
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
        verify(cardBinDefinitionMapper, never()).selectByPrimaryKey(any());
    }

    @Test
    void testRemove_NotFound() {
        // Arrange
        Mockito.lenient().when(cardBinDefinitionMapper.selectByPrimaryKey("999")).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> cardBinDefinitionService.remove("999"));
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_DELETE_CARD_BIN_DEFINITION_BY_ID_FAULT.getCode(), exception.getErrCode());
        verify(cardBinDefinitionMapper).selectByPrimaryKey("999");
    }

    @Test
    void testRemoveByOrgAndTableId_Success() {
        // Arrange
        Mockito.lenient().when(cardBinDefinitionSelfMapper.selectByOrgAndTableId("1001", "BIN001")).thenReturn(parmCardBinDefinition);
        Mockito.lenient().when(cardBinDefinitionMapper.deleteByPrimaryKey("1")).thenReturn(1);
        Mockito.lenient().when(cardBinControlSelfMapper.delectByTableIdAndOrgNum("BIN001", "1001")).thenReturn(1);

        // Act
        Boolean result = cardBinDefinitionService.removeByOrgAndTableId("1001", "BIN001");

        // Assert
        assertTrue(result);
        verify(cardBinDefinitionSelfMapper).selectByOrgAndTableId("1001", "BIN001");
        verify(cardBinControlSelfMapper).delectByTableIdAndOrgNum("BIN001", "1001");
        verify(cardBinDefinitionMapper).deleteByPrimaryKey("1");
    }

    @Test
    void testRemoveByOrgAndTableId_EmptyOrganizationNumber() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> cardBinDefinitionService.removeByOrgAndTableId("", "BIN001"));
        
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
        verify(cardBinDefinitionSelfMapper, never()).selectByOrgAndTableId(any(), any());
    }

    @Test
    void testRemoveByOrgAndTableId_EmptyTableId() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> cardBinDefinitionService.removeByOrgAndTableId("1001", ""));
        
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
        verify(cardBinDefinitionSelfMapper, never()).selectByOrgAndTableId(any(), any());
    }

    @Test
    void testRemoveByOrgAndTableId_NotFound() {
        // Arrange
        Mockito.lenient().when(cardBinDefinitionSelfMapper.selectByOrgAndTableId("1001", "BIN999")).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> cardBinDefinitionService.removeByOrgAndTableId("1001", "BIN999"));
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_DELETE_CARD_BIN_DEFINITION_BY_ID_FAULT.getCode(), exception.getErrCode());
        verify(cardBinDefinitionSelfMapper).selectByOrgAndTableId("1001", "BIN999");
    }

    @Test
    void testRemoveByOrgAndTableId_DeleteException() {
        // Arrange
        Mockito.lenient().when(cardBinDefinitionSelfMapper.selectByOrgAndTableId("1001", "BIN001")).thenReturn(parmCardBinDefinition);
        Mockito.lenient().doThrow(new RuntimeException("Database error")).when(cardBinControlSelfMapper).delectByTableIdAndOrgNum("BIN001", "1001");

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> cardBinDefinitionService.removeByOrgAndTableId("1001", "BIN001"));
        
        assertEquals(AnyTxnParameterRespCodeEnum.S_DELETE_CARD_BIN_DEFINITION_FAULT.getCode(), exception.getErrCode());
        verify(cardBinDefinitionSelfMapper).selectByOrgAndTableId("1001", "BIN001");
        verify(cardBinControlSelfMapper).delectByTableIdAndOrgNum("BIN001", "1001");
    }

    @Test
    void testRemoveByOrgAndTableId_DeleteReturnZero() {
        // Arrange
        Mockito.lenient().when(cardBinDefinitionSelfMapper.selectByOrgAndTableId("1001", "BIN001")).thenReturn(parmCardBinDefinition);
        Mockito.lenient().when(cardBinDefinitionMapper.deleteByPrimaryKey("1")).thenReturn(0);
        Mockito.lenient().when(cardBinControlSelfMapper.delectByTableIdAndOrgNum("BIN001", "1001")).thenReturn(1);

        // Act
        Boolean result = cardBinDefinitionService.removeByOrgAndTableId("1001", "BIN001");

        // Assert
        assertFalse(result);
        verify(cardBinDefinitionSelfMapper).selectByOrgAndTableId("1001", "BIN001");
        verify(cardBinControlSelfMapper).delectByTableIdAndOrgNum("BIN001", "1001");
        verify(cardBinDefinitionMapper).deleteByPrimaryKey("1");
    }
}
