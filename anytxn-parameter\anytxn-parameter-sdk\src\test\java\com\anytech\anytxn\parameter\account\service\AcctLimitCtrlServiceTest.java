package com.anytech.anytxn.parameter.account.service;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.account.mapper.ParmAcctLimitCtrlMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmAcctLimitCtrlSelfMapper;
import com.anytech.anytxn.parameter.account.service.AcctLimitCtrlServiceImpl;
import com.anytech.anytxn.parameter.base.account.domain.dto.AcctLimitCtrlDefDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.AcctLimitCtrlReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.AcctLimitCtrlResDTO;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmAcctLimitCtrl;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AcctLimitCtrlServiceImpl 单元测试类
 * 
 * 测试 AcctLimitCtrlServiceImpl 的7个核心方法：
 * 1. findAll - 分页查询账户额度控制参数
 * 2. addAcctLimitCtrl - 添加账户额度控制参数
 * 3. modifyAcctLimitCtrl - 修改账户额度控制参数
 * 4. removeAcctLimitCtrl - 删除账户额度控制参数
 * 5. findById - 根据ID查询账户额度控制参数
 * 6. findByOrgNumAndAcctLimitCtrlId - 根据机构号和账户额度控制ID查询
 * 7. removeAcctLimitCtrlByOrgAndAcctId - 根据机构号和账户额度控制ID删除
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class AcctLimitCtrlServiceTest {

    @Mock
    private ParmAcctLimitCtrlMapper parmAcctLimitCtrlMapper;

    @Mock
    private ParmAcctLimitCtrlSelfMapper parmAcctLimitCtrlSelfMapper;

    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private AcctLimitCtrlServiceImpl acctLimitCtrlService;

    private ParmAcctLimitCtrl testEntity;

    @BeforeEach
    void setUp() {
        // 设置测试数据 - 不继承BaseParam的Entity对象
        testEntity = new ParmAcctLimitCtrl();
        testEntity.setId("1");
        testEntity.setOrganizationNumber("0001");
        testEntity.setAcctLimitCtrlId("ALCTRL001");
        testEntity.setDescription("测试账户额度控制");
        testEntity.setLimitTypeCode("LT001");
        testEntity.setMaxLimitAmount(new BigDecimal("100000.00"));
        testEntity.setCreditFormula("FORMULA001");
        testEntity.setOverlimitPercent(new BigDecimal("10.00"));
        testEntity.setOverlimitCountPerCycle(3);
        testEntity.setCreateTime(LocalDateTime.now());
        testEntity.setUpdateTime(LocalDateTime.now());
        testEntity.setVersionNumber(1L);

        // 不在setUp中创建继承BaseParam的DTO对象，避免OrgNumberUtils.getOrg()调用失败
    }

    /**
     * 创建测试用的DTO对象（继承BaseParam）
     * 需要在Mock环境中创建，避免OrgNumberUtils.getOrg()调用失败
     */
    private AcctLimitCtrlReqDTO createTestReqDTO() {
        AcctLimitCtrlReqDTO reqDTO = new AcctLimitCtrlReqDTO();
        reqDTO.setId("1");
        reqDTO.setAcctLimitCtrlId("ALCTRL001");
        reqDTO.setDescription("测试账户额度控制");
        reqDTO.setVersionNumber(1L);
        
        // 创建额度控制详情列表
        List<AcctLimitCtrlDefDTO> defDTOList = new ArrayList<>();
        AcctLimitCtrlDefDTO defDTO = new AcctLimitCtrlDefDTO();
        defDTO.setLimitTypeCode("LT001");
        defDTO.setMaxLimitAmount(new BigDecimal("100000.00"));
        defDTO.setCreditFormula("FORMULA001");
        defDTO.setOverlimitPercent(new BigDecimal("10.00"));
        defDTO.setOverlimitCountPerCycle(3);
        defDTOList.add(defDTO);
        reqDTO.setAcctLimitCtrlDefDTOList(defDTOList);
        
        return reqDTO;
    }

    /**
     * 创建测试用的ResDTO对象（继承BaseParam）
     * 需要在Mock环境中创建，避免OrgNumberUtils.getOrg()调用失败
     */
    private AcctLimitCtrlResDTO createTestResDTO() {
        AcctLimitCtrlResDTO resDTO = new AcctLimitCtrlResDTO();
        resDTO.setId("1");
        resDTO.setAcctLimitCtrlId("ALCTRL001");
        resDTO.setDescription("测试账户额度控制");
        resDTO.setVersionNumber(1L);
        return resDTO;
    }

    @Test
    void testFindAll_Success() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMock = mockStatic(BeanMapping.class)) {
            
            orgMock.when(OrgNumberUtils::getOrg).thenReturn("0001");
            
            AcctLimitCtrlReqDTO reqDTO = createTestReqDTO();
            AcctLimitCtrlResDTO testResDTO = createTestResDTO();
            List<ParmAcctLimitCtrl> entityList = Arrays.asList(testEntity);
            List<AcctLimitCtrlResDTO> resDTOList = Arrays.asList(testResDTO);
            
            when(parmAcctLimitCtrlSelfMapper.selectByCondition(any(AcctLimitCtrlReqDTO.class)))
                .thenReturn(entityList);
            beanMock.when(() -> BeanMapping.copyList(entityList, AcctLimitCtrlResDTO.class))
                .thenReturn(resDTOList);

            // Act
            PageResultDTO<AcctLimitCtrlResDTO> result = acctLimitCtrlService.findAll(1, 10, reqDTO);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getPage());
            assertEquals(10, result.getRows());
            assertEquals(1, result.getData().size());
            assertEquals("ALCTRL001", result.getData().get(0).getAcctLimitCtrlId());
        }
    }

    @Test
    void testAddAcctLimitCtrl_Success() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<TenantUtils> tenantMock = mockStatic(TenantUtils.class)) {
            
            orgMock.when(OrgNumberUtils::getOrg).thenReturn("0001");
            tenantMock.when(TenantUtils::getTenantId).thenReturn("tenant1");
            
            AcctLimitCtrlReqDTO reqDTO = createTestReqDTO();
            
            when(parmAcctLimitCtrlSelfMapper.selectByOrgAndAcctId(anyString(), anyString()))
                .thenReturn(Collections.emptyList());
            when(numberIdGenerator.generateId(anyString())).thenReturn(123456L);

            // Act
            ParameterCompare result = acctLimitCtrlService.addAcctLimitCtrl(reqDTO);

            // Assert
            assertNotNull(result);
            assertEquals("ALCTRL001", result.getMainParmId());
            verify(parmAcctLimitCtrlSelfMapper).selectByOrgAndAcctId("0001", "ALCTRL001");
        }
    }

    @Test
    void testAddAcctLimitCtrl_AcctLimitCtrlIdNull_ThrowsException() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgMock = mockStatic(OrgNumberUtils.class)) {
            orgMock.when(OrgNumberUtils::getOrg).thenReturn("0001");
            
            AcctLimitCtrlReqDTO reqDTO = createTestReqDTO();
            reqDTO.setAcctLimitCtrlId(null);

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
                () -> acctLimitCtrlService.addAcctLimitCtrl(reqDTO));
            assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_NOT_EMPTY_FAULT.getCode(), 
                exception.getErrCode());
        }
    }

    @Test
    void testAddAcctLimitCtrl_AlreadyExists_ThrowsException() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgMock = mockStatic(OrgNumberUtils.class)) {
            orgMock.when(OrgNumberUtils::getOrg).thenReturn("0001");
            
            AcctLimitCtrlReqDTO reqDTO = createTestReqDTO();
            List<ParmAcctLimitCtrl> existingList = Arrays.asList(testEntity);
            
            when(parmAcctLimitCtrlSelfMapper.selectByOrgAndAcctId(anyString(), anyString()))
                .thenReturn(existingList);

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
                () -> acctLimitCtrlService.addAcctLimitCtrl(reqDTO));
            assertEquals(AnyTxnParameterRespCodeEnum.D_DATA_ALREADY_EXISTS_FAULT.getCode(), 
                exception.getErrCode());
        }
    }

    @Test
    void testModifyAcctLimitCtrl_Success() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMock = mockStatic(BeanMapping.class)) {
            
            orgMock.when(OrgNumberUtils::getOrg).thenReturn("0001");
            
            AcctLimitCtrlReqDTO reqDTO = createTestReqDTO();
            AcctLimitCtrlResDTO testResDTO = createTestResDTO();
            
            when(parmAcctLimitCtrlMapper.selectByPrimaryKey("1")).thenReturn(testEntity);
            beanMock.when(() -> BeanMapping.copy(testEntity, AcctLimitCtrlResDTO.class))
                .thenReturn(testResDTO);

            // Act
            ParameterCompare result = acctLimitCtrlService.modifyAcctLimitCtrl(reqDTO);

            // Assert
            assertNotNull(result);
            assertEquals("ALCTRL001", result.getMainParmId());
            verify(parmAcctLimitCtrlMapper).selectByPrimaryKey("1");
        }
    }

    @Test
    void testModifyAcctLimitCtrl_EmptyDefList_ThrowsException() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgMock = mockStatic(OrgNumberUtils.class)) {
            orgMock.when(OrgNumberUtils::getOrg).thenReturn("0001");
            
            AcctLimitCtrlReqDTO reqDTO = createTestReqDTO();
            reqDTO.setAcctLimitCtrlDefDTOList(Collections.emptyList());

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
                () -> acctLimitCtrlService.modifyAcctLimitCtrl(reqDTO));
            assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), 
                exception.getErrCode());
        }
    }

    @Test
    void testRemoveAcctLimitCtrl_Success() {
        // Arrange
        when(parmAcctLimitCtrlMapper.selectByPrimaryKey("1")).thenReturn(testEntity);
        when(parmAcctLimitCtrlMapper.deleteByPrimaryKey("1")).thenReturn(1);

        // Act
        Boolean result = acctLimitCtrlService.removeAcctLimitCtrl("1");

        // Assert
        assertTrue(result);
        verify(parmAcctLimitCtrlMapper).deleteByPrimaryKey("1");
    }

    @Test
    void testRemoveAcctLimitCtrl_DataNotExist_ThrowsException() {
        // Arrange
        when(parmAcctLimitCtrlMapper.selectByPrimaryKey("1")).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> acctLimitCtrlService.removeAcctLimitCtrl("1"));
        assertEquals(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT.getCode(), 
            exception.getErrCode());
    }

    @Test
    void testFindById_Success() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMock = mockStatic(BeanMapping.class)) {
            
            orgMock.when(OrgNumberUtils::getOrg).thenReturn("0001");
            AcctLimitCtrlResDTO testResDTO = createTestResDTO();
            
            when(parmAcctLimitCtrlMapper.selectByPrimaryKey("1")).thenReturn(testEntity);
            beanMock.when(() -> BeanMapping.copy(testEntity, AcctLimitCtrlResDTO.class))
                .thenReturn(testResDTO);

            // Act
            AcctLimitCtrlResDTO result = acctLimitCtrlService.findById("1");

            // Assert
            assertNotNull(result);
            assertEquals("ALCTRL001", result.getAcctLimitCtrlId());
            verify(parmAcctLimitCtrlMapper).selectByPrimaryKey("1");
        }
    }

    @Test
    void testFindById_NullId_ThrowsException() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> acctLimitCtrlService.findById(null));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT.getCode(), 
            exception.getErrCode());
    }

    @Test
    void testFindByOrgNumAndAcctLimitCtrlId_Success() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMock = mockStatic(BeanMapping.class)) {
            
            orgMock.when(OrgNumberUtils::getOrg).thenReturn("0001");
            AcctLimitCtrlResDTO testResDTO = createTestResDTO();
            
            List<ParmAcctLimitCtrl> entityList = Arrays.asList(testEntity);
            
            when(parmAcctLimitCtrlSelfMapper.selectByOrgAndAcctId("0001", "ALCTRL001"))
                .thenReturn(entityList);
            beanMock.when(() -> BeanMapping.copy(testEntity, AcctLimitCtrlResDTO.class))
                .thenReturn(testResDTO);

            // Act
            AcctLimitCtrlResDTO result = acctLimitCtrlService.findByOrgNumAndAcctLimitCtrlId("0001", "ALCTRL001");

            // Assert
            assertNotNull(result);
            assertEquals("ALCTRL001", result.getAcctLimitCtrlId());
            assertNotNull(result.getAcctLimitCtrlDefDTOList());
            assertEquals(1, result.getAcctLimitCtrlDefDTOList().size());
            verify(parmAcctLimitCtrlSelfMapper).selectByOrgAndAcctId("0001", "ALCTRL001");
        }
    }

    @Test
    void testFindByOrgNumAndAcctLimitCtrlId_EmptyOrgNum_ThrowsException() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> acctLimitCtrlService.findByOrgNumAndAcctLimitCtrlId("", "ALCTRL001"));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), 
            exception.getErrCode());
    }

    @Test
    void testRemoveAcctLimitCtrlByOrgAndAcctId_Success() {
        // Arrange
        List<ParmAcctLimitCtrl> entityList = Arrays.asList(testEntity);
        
        when(parmAcctLimitCtrlSelfMapper.selectByOrgAndAcctId("0001", "ALCTRL001"))
            .thenReturn(entityList);

        // Act
        ParameterCompare result = acctLimitCtrlService.removeAcctLimitCtrlByOrgAndAcctId("0001", "ALCTRL001");

        // Assert
        assertNotNull(result);
        assertEquals("ALCTRL001", result.getMainParmId());
        verify(parmAcctLimitCtrlSelfMapper).selectByOrgAndAcctId("0001", "ALCTRL001");
    }

    @Test
    void testRemoveAcctLimitCtrlByOrgAndAcctId_DataNotExist_ThrowsException() {
        // Arrange
        when(parmAcctLimitCtrlSelfMapper.selectByOrgAndAcctId("0001", "ALCTRL001"))
            .thenReturn(Collections.emptyList());

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> acctLimitCtrlService.removeAcctLimitCtrlByOrgAndAcctId("0001", "ALCTRL001"));
        assertEquals(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT.getCode(), 
            exception.getErrCode());
    }
} 
