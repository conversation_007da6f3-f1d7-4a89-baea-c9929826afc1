package com.anytech.anytxn.parameter.authorization.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.authorization.service.AuthCheckDefinitionServiceImpl;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.AuthCheckControlReqDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.AuthCheckDefinitionReqDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.AuthCheckDefinitionResDTO;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.authorization.mapper.ParmAuthCheckControlMapper;
import com.anytech.anytxn.parameter.authorization.mapper.ParmAuthCheckControlSelfMapper;
import com.anytech.anytxn.parameter.authorization.mapper.ParmAuthCheckDefinitionMapper;
import com.anytech.anytxn.parameter.authorization.mapper.ParmAuthCheckDefinitionSelfMapper;
import com.anytech.anytxn.parameter.base.authorization.domain.model.ParmAuthCheckControl;
import com.anytech.anytxn.parameter.base.authorization.domain.model.ParmAuthCheckDefinition;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AuthCheckDefinitionServiceImpl 单元测试类
 * 
 * 测试授权检查定义服务的核心业务逻辑
 * 
 * <AUTHOR> Generator
 * @date 2025-06-25
 */
@ExtendWith(MockitoExtension.class)
class AuthCheckDefinitionServiceTest {

    @Mock
    private ParmAuthCheckDefinitionMapper parmAuthCheckDefinitionMapper;

    @Mock
    private ParmAuthCheckDefinitionSelfMapper parmAuthCheckDefinitionSelfMapper;

    @Mock
    private ParmAuthCheckControlMapper parmAuthCheckControlMapper;

    @Mock
    private ParmAuthCheckControlSelfMapper parmAuthCheckControlSelfMapper;

    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private AuthCheckDefinitionServiceImpl authCheckDefinitionService;

    private String testOrganizationNumber;
    private String testTableId;
    private String testId;
    private ParmAuthCheckDefinition testParmAuthCheckDefinition;
    private AuthCheckDefinitionReqDTO testAuthCheckDefinitionReqDTO;
    private AuthCheckDefinitionResDTO testAuthCheckDefinitionResDTO;
    private List<AuthCheckControlReqDTO> testAuthCheckControlReqList;

    @BeforeEach
    void setUp() throws Exception {
        // 初始化OrgNumberUtils静态实例
        try {
            Field orgNumberUtilField = OrgNumberUtils.class.getDeclaredField("orgNumberUtil");
            orgNumberUtilField.setAccessible(true);
            OrgNumberUtils mockOrgNumberUtil = mock(OrgNumberUtils.class);
            orgNumberUtilField.set(null, mockOrgNumberUtil);
            when(mockOrgNumberUtil.getBatchOrg()).thenReturn("001");
        } catch (Exception e) {
            // 如果反射失败，可以忽略
        }

        // 准备测试数据
        testOrganizationNumber = "001";
        testTableId = "TBL001";
        testId = "123456789";
        
        // 创建测试的AuthCheckControlReqDTO对象
        AuthCheckControlReqDTO authCheckControlReq1 = new AuthCheckControlReqDTO();
        authCheckControlReq1.setCheckItem("0");
        authCheckControlReq1.setDescription("卡激活检查");
        authCheckControlReq1.setCheckPriority(1);
        authCheckControlReq1.setResponsePriority(1);
        authCheckControlReq1.setOrganizationNumber(testOrganizationNumber);
        
        AuthCheckControlReqDTO authCheckControlReq2 = new AuthCheckControlReqDTO();
        authCheckControlReq2.setCheckItem("1");
        authCheckControlReq2.setDescription("状态检查");
        authCheckControlReq2.setCheckPriority(2);
        authCheckControlReq2.setResponsePriority(2);
        authCheckControlReq2.setOrganizationNumber(testOrganizationNumber);
        
        testAuthCheckControlReqList = Arrays.asList(authCheckControlReq1, authCheckControlReq2);
        
        // 创建测试的AuthCheckDefinitionReqDTO对象
        testAuthCheckDefinitionReqDTO = new AuthCheckDefinitionReqDTO();
        testAuthCheckDefinitionReqDTO.setId(testId);
        testAuthCheckDefinitionReqDTO.setTableId(testTableId);
        testAuthCheckDefinitionReqDTO.setDescription("测试授权检查定义");
        testAuthCheckDefinitionReqDTO.setCheckMethod("0");
        testAuthCheckDefinitionReqDTO.setStatus("1");
        testAuthCheckDefinitionReqDTO.setOrganizationNumber(testOrganizationNumber);
        testAuthCheckDefinitionReqDTO.setAuthCheckControlReqList(testAuthCheckControlReqList);
        
        // 创建测试的ParmAuthCheckDefinition对象
        testParmAuthCheckDefinition = new ParmAuthCheckDefinition();
        testParmAuthCheckDefinition.setId(testId);
        testParmAuthCheckDefinition.setTableId(testTableId);
        testParmAuthCheckDefinition.setDescription("测试授权检查定义");
        testParmAuthCheckDefinition.setCheckMethod("0");
        testParmAuthCheckDefinition.setStatus("1");
        testParmAuthCheckDefinition.setOrganizationNumber(testOrganizationNumber);
        testParmAuthCheckDefinition.setCreateTime(LocalDateTime.now());
        testParmAuthCheckDefinition.setUpdateTime(LocalDateTime.now());
        testParmAuthCheckDefinition.setUpdateBy("testUser");
        testParmAuthCheckDefinition.setVersionNumber(1L);

        // 创建测试的AuthCheckDefinitionResDTO对象
        testAuthCheckDefinitionResDTO = new AuthCheckDefinitionResDTO();
        testAuthCheckDefinitionResDTO.setId(testId);
        testAuthCheckDefinitionResDTO.setTableId(testTableId);
        testAuthCheckDefinitionResDTO.setDescription("测试授权检查定义");
        testAuthCheckDefinitionResDTO.setCheckMethod("0");
        testAuthCheckDefinitionResDTO.setStatus("1");
        testAuthCheckDefinitionResDTO.setOrganizationNumber(testOrganizationNumber);
    }

    /**
     * 测试添加授权检查定义 - 成功场景
     */
    @Test
    void testAdd_Success() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn(testOrganizationNumber);
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("tenant001");
            when(numberIdGenerator.generateId(anyString())).thenReturn(Long.valueOf(testId));
            
            // Mock不存在记录
            when(parmAuthCheckDefinitionSelfMapper.findByOrgAndTableId(testOrganizationNumber, testTableId))
                .thenReturn(null);
            
            // Act
            ParameterCompare result = authCheckDefinitionService.add(testAuthCheckDefinitionReqDTO);

            // Assert
            assertNotNull(result);
            assertEquals(testTableId, result.getMainParmId());
            verify(parmAuthCheckDefinitionSelfMapper).findByOrgAndTableId(testOrganizationNumber, testTableId);
            verify(numberIdGenerator).generateId("tenant001");
        }
    }

    /**
     * 测试添加授权检查定义 - 记录已存在
     */
    @Test
    void testAdd_RecordExists() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn(testOrganizationNumber);
            
            // Mock记录已存在
            when(parmAuthCheckDefinitionSelfMapper.findByOrgAndTableId(testOrganizationNumber, testTableId))
                .thenReturn(testParmAuthCheckDefinition);
            
            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
                authCheckDefinitionService.add(testAuthCheckDefinitionReqDTO);
            });
            
            assertEquals(AnyTxnParameterRespCodeEnum.D_EXIST_PARM_PAYMENT_ALLOCATED_BY_ID_FAULT.getCode(), exception.getErrCode());
            verify(parmAuthCheckDefinitionSelfMapper).findByOrgAndTableId(testOrganizationNumber, testTableId);
        }
    }

    /**
     * 测试添加授权检查定义 - 检查项重复
     */
    @Test
    void testAdd_DuplicateCheckItem() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn(testOrganizationNumber);
            
            // Mock不存在记录
            when(parmAuthCheckDefinitionSelfMapper.findByOrgAndTableId(testOrganizationNumber, testTableId))
                .thenReturn(null);
            
            // 创建重复检查项的请求
            AuthCheckControlReqDTO duplicateReq1 = new AuthCheckControlReqDTO();
            duplicateReq1.setCheckItem("0");
            duplicateReq1.setDescription("卡激活检查1");
            
            AuthCheckControlReqDTO duplicateReq2 = new AuthCheckControlReqDTO();
            duplicateReq2.setCheckItem("0"); // 重复的检查项
            duplicateReq2.setDescription("卡激活检查2");
            
            testAuthCheckDefinitionReqDTO.setAuthCheckControlReqList(Arrays.asList(duplicateReq1, duplicateReq2));
            
            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
                authCheckDefinitionService.add(testAuthCheckDefinitionReqDTO);
            });
            
            assertEquals(AnyTxnParameterRespCodeEnum.D_REPEAT_AUTH_CHECK_DEFINITION_FAULT.getCode(), exception.getErrCode());
        }
    }

    /**
     * 测试修改授权检查定义 - 成功场景
     */
    @Test
    void testModify_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(any(AuthCheckDefinitionResDTO.class), eq(AuthCheckDefinitionReqDTO.class)))
                .thenReturn(testAuthCheckDefinitionReqDTO);
            
            // Mock存在记录
            when(parmAuthCheckDefinitionMapper.selectByPrimaryKey(testId))
                .thenReturn(testParmAuthCheckDefinition);
            when(parmAuthCheckControlSelfMapper.findByOrgAndTableId(testOrganizationNumber, testTableId))
                .thenReturn(Collections.emptyList());
            
            // Act
            ParameterCompare result = authCheckDefinitionService.modify(testAuthCheckDefinitionReqDTO);

            // Assert
            assertNotNull(result);
            assertEquals(testTableId, result.getMainParmId());
            verify(parmAuthCheckDefinitionMapper).selectByPrimaryKey(testId);
        }
    }

    /**
     * 测试修改授权检查定义 - ID为空
     */
    @Test
    void testModify_EmptyId() {
        // Arrange
        testAuthCheckDefinitionReqDTO.setId(null);
        
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            authCheckDefinitionService.modify(testAuthCheckDefinitionReqDTO);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试分页查询授权检查定义 - 成功场景
     */
    @Test
    void testFindAllByPage_Success() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn(testOrganizationNumber);
            
            Page<ParmAuthCheckDefinition> mockPage = new Page<>(1, 10);
            mockPage.setTotal(1);
            mockPage.setPages(1);
            mockPage.add(testParmAuthCheckDefinition);
            
            List<AuthCheckDefinitionResDTO> mockList = Collections.singletonList(testAuthCheckDefinitionResDTO);
            beanMappingMock.when(() -> BeanMapping.copyList(anyList(), eq(AuthCheckDefinitionResDTO.class)))
                .thenReturn(mockList);
            
            when(parmAuthCheckDefinitionSelfMapper.selectByCondition(any(AuthCheckDefinitionReqDTO.class)))
                .thenReturn(Collections.singletonList(testParmAuthCheckDefinition));
            
            // Act
            PageResultDTO<AuthCheckDefinitionResDTO> result = authCheckDefinitionService.findAllByPage(1, 10, testAuthCheckDefinitionReqDTO);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getPage());
            assertEquals(10, result.getRows());
            assertEquals(1, result.getData().size());
            verify(parmAuthCheckDefinitionSelfMapper).selectByCondition(any(AuthCheckDefinitionReqDTO.class));
        }
    }

    /**
     * 测试通过ID查询授权检查定义 - 成功场景
     */
    @Test
    void testFindById_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(any(ParmAuthCheckDefinition.class), any(AuthCheckDefinitionResDTO.class)))
                .thenAnswer(invocation -> {
                    AuthCheckDefinitionResDTO target = invocation.getArgument(1);
                    target.setId(testId);
                    target.setOrganizationNumber(testOrganizationNumber);
                    target.setTableId(testTableId);
                    return null;
                });
            beanMappingMock.when(() -> BeanMapping.copyList(anyList(), eq(Class.class)))
                .thenReturn(Collections.emptyList());
            
            when(parmAuthCheckDefinitionMapper.selectByPrimaryKey(testId))
                .thenReturn(testParmAuthCheckDefinition);
            when(parmAuthCheckControlSelfMapper.findByOrgAndTableId(testOrganizationNumber, testTableId))
                .thenReturn(Collections.emptyList());
            
            // Act
            AuthCheckDefinitionResDTO result = authCheckDefinitionService.findById(testId);

            // Assert
            assertNotNull(result);
            verify(parmAuthCheckDefinitionMapper).selectByPrimaryKey(testId);
            verify(parmAuthCheckControlSelfMapper).findByOrgAndTableId(testOrganizationNumber, testTableId);
        }
    }

    /**
     * 测试通过ID查询授权检查定义 - ID为空
     */
    @Test
    void testFindById_EmptyId() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            authCheckDefinitionService.findById(null);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试通过ID查询授权检查定义 - 记录不存在
     */
    @Test
    void testFindById_RecordNotFound() {
        // Arrange
        when(parmAuthCheckDefinitionMapper.selectByPrimaryKey(testId))
            .thenReturn(null);
        
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            authCheckDefinitionService.findById(testId);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_AUTH_CHECK_DEFINITION_BY_ID_FAULT.getCode(), exception.getErrCode());
        verify(parmAuthCheckDefinitionMapper).selectByPrimaryKey(testId);
    }

    /**
     * 测试删除授权检查定义 - 成功场景
     */
    @Test
    void testRemove_Success() {
        // Arrange
        when(parmAuthCheckDefinitionMapper.selectByPrimaryKey(testId))
            .thenReturn(testParmAuthCheckDefinition);
        
        // Act
        ParameterCompare result = authCheckDefinitionService.remove(testId);

        // Assert
        assertNotNull(result);
        assertEquals(testTableId, result.getMainParmId());
        verify(parmAuthCheckDefinitionMapper).selectByPrimaryKey(testId);
    }

    /**
     * 测试删除授权检查定义 - ID为空
     */
    @Test
    void testRemove_EmptyId() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            authCheckDefinitionService.remove(null);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试删除授权检查定义 - 记录不存在
     */
    @Test
    void testRemove_RecordNotFound() {
        // Arrange
        when(parmAuthCheckDefinitionMapper.selectByPrimaryKey(testId))
            .thenReturn(null);
        
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            authCheckDefinitionService.remove(testId);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_AUTH_CHECK_DEFINITION_BY_ID_FAULT.getCode(), exception.getErrCode());
        verify(parmAuthCheckDefinitionMapper).selectByPrimaryKey(testId);
    }

    /**
     * 测试根据机构号和参数表ID查询授权检查定义 - 成功场景
     */
    @Test
    void testFindByOrgAndTableId_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(any(ParmAuthCheckDefinition.class), eq(AuthCheckDefinitionResDTO.class)))
                .thenReturn(testAuthCheckDefinitionResDTO);
            
            when(parmAuthCheckDefinitionSelfMapper.findByOrgAndTableId(testOrganizationNumber, testTableId))
                .thenReturn(testParmAuthCheckDefinition);
            
            // Act
            AuthCheckDefinitionResDTO result = authCheckDefinitionService.findByOrgAndTableId(testOrganizationNumber, testTableId);

            // Assert
            assertNotNull(result);
            assertEquals(testAuthCheckDefinitionResDTO, result);
            verify(parmAuthCheckDefinitionSelfMapper).findByOrgAndTableId(testOrganizationNumber, testTableId);
        }
    }

    /**
     * 测试根据机构号和参数表ID查询授权检查定义 - 记录不存在
     */
    @Test
    void testFindByOrgAndTableId_RecordNotFound() {
        // Arrange
        when(parmAuthCheckDefinitionSelfMapper.findByOrgAndTableId(testOrganizationNumber, testTableId))
            .thenReturn(null);
        
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            authCheckDefinitionService.findByOrgAndTableId(testOrganizationNumber, testTableId);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_AUTH_CHECK_DEFINITION_FAULT.getCode(), exception.getErrCode());
        verify(parmAuthCheckDefinitionSelfMapper).findByOrgAndTableId(testOrganizationNumber, testTableId);
    }
} 
