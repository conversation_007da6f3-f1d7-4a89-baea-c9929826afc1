package com.anytech.anytxn.parameter.authorization.service;

import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.authorization.service.AuthCheckControlServiceImpl;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.AuthCheckControlResDTO;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.authorization.mapper.ParmAuthCheckControlMapper;
import com.anytech.anytxn.parameter.authorization.mapper.ParmAuthCheckControlSelfMapper;
import com.anytech.anytxn.parameter.base.authorization.domain.model.ParmAuthCheckControl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * AuthCheckControlServiceImpl 单元测试类
 * 
 * 测试授权检查控制服务的核心业务逻辑
 * 
 * <AUTHOR> Generator
 * @date 2025-06-25
 */
@ExtendWith(MockitoExtension.class)
class AuthCheckControlServiceTest {

    @Mock
    private ParmAuthCheckControlMapper parmAuthCheckControlMapper;

    @Mock
    private ParmAuthCheckControlSelfMapper parmAuthCheckControlSelfMapper;

    @InjectMocks
    private AuthCheckControlServiceImpl authCheckControlService;

    private String testOrganizationNumber;
    private String testTableId;
    private ParmAuthCheckControl testParmAuthCheckControl;
    private AuthCheckControlResDTO testAuthCheckControlResDTO;
    private List<ParmAuthCheckControl> testParmAuthCheckControlList;
    private List<AuthCheckControlResDTO> testAuthCheckControlResDTOList;

    @BeforeEach
    void setUp() throws Exception {
        // 初始化OrgNumberUtils静态实例
        try {
            Field orgNumberUtilField = OrgNumberUtils.class.getDeclaredField("orgNumberUtil");
            orgNumberUtilField.setAccessible(true);
            OrgNumberUtils mockOrgNumberUtil = mock(OrgNumberUtils.class);
            orgNumberUtilField.set(null, mockOrgNumberUtil);
            when(mockOrgNumberUtil.getBatchOrg()).thenReturn("001");
        } catch (Exception e) {
            // 如果反射失败，可以忽略
        }

        // 准备测试数据
        testOrganizationNumber = "001";
        testTableId = "TBL001";
        
        // 创建测试的ParmAuthCheckControl对象
        testParmAuthCheckControl = new ParmAuthCheckControl();
        testParmAuthCheckControl.setId("123456789");
        testParmAuthCheckControl.setTableId(testTableId);
        testParmAuthCheckControl.setCheckItem("0");
        testParmAuthCheckControl.setDescription("卡激活检查");
        testParmAuthCheckControl.setCheckPriority(1);
        testParmAuthCheckControl.setResponsePriority(1);
        testParmAuthCheckControl.setStatus("1");
        testParmAuthCheckControl.setOrganizationNumber(testOrganizationNumber);
        testParmAuthCheckControl.setCreateTime(LocalDateTime.now());
        testParmAuthCheckControl.setUpdateTime(LocalDateTime.now());
        testParmAuthCheckControl.setUpdateBy("testUser");
        testParmAuthCheckControl.setVersionNumber(1L);

        // 创建测试的AuthCheckControlResDTO对象
        testAuthCheckControlResDTO = new AuthCheckControlResDTO();
        testAuthCheckControlResDTO.setId("123456789");
        testAuthCheckControlResDTO.setTableId(testTableId);
        testAuthCheckControlResDTO.setCheckItem("0");
        testAuthCheckControlResDTO.setDescription("卡激活检查");
        testAuthCheckControlResDTO.setCheckPriority(1);
        testAuthCheckControlResDTO.setResponsePriority(1);
        testAuthCheckControlResDTO.setOrganizationNumber(testOrganizationNumber);
        testAuthCheckControlResDTO.setCreateTime(LocalDateTime.now());
        testAuthCheckControlResDTO.setUpdateTime(LocalDateTime.now());
        testAuthCheckControlResDTO.setUpdateBy("testUser");
        testAuthCheckControlResDTO.setVersionNumber(1L);

        // 创建测试列表
        testParmAuthCheckControlList = Arrays.asList(testParmAuthCheckControl);
        testAuthCheckControlResDTOList = Arrays.asList(testAuthCheckControlResDTO);
    }

    /**
     * 测试 findByOrgAndTableId - 成功找到数据
     */
    @Test
    void testFindByOrgAndTableId_Success() {
        // Arrange
        when(parmAuthCheckControlSelfMapper.findByOrgAndTableId(testOrganizationNumber, testTableId))
                .thenReturn(testParmAuthCheckControlList);

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copyList(testParmAuthCheckControlList, AuthCheckControlResDTO.class))
                    .thenReturn(testAuthCheckControlResDTOList);

            // Act
            List<AuthCheckControlResDTO> result = authCheckControlService.findByOrgAndTableId(testOrganizationNumber, testTableId);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(testAuthCheckControlResDTO.getId(), result.get(0).getId());
            assertEquals(testAuthCheckControlResDTO.getTableId(), result.get(0).getTableId());
            assertEquals(testAuthCheckControlResDTO.getCheckItem(), result.get(0).getCheckItem());
            assertEquals(testAuthCheckControlResDTO.getDescription(), result.get(0).getDescription());
            assertEquals(testAuthCheckControlResDTO.getCheckPriority(), result.get(0).getCheckPriority());
            assertEquals(testAuthCheckControlResDTO.getResponsePriority(), result.get(0).getResponsePriority());
            
            verify(parmAuthCheckControlSelfMapper).findByOrgAndTableId(testOrganizationNumber, testTableId);
            beanMappingMock.verify(() -> BeanMapping.copyList(testParmAuthCheckControlList, AuthCheckControlResDTO.class));
        }
    }

    /**
     * 测试 findByOrgAndTableId - 找到多条数据
     */
    @Test
    void testFindByOrgAndTableId_Success_MultipleRecords() {
        // Arrange
        ParmAuthCheckControl secondControl = new ParmAuthCheckControl();
        secondControl.setId("987654321");
        secondControl.setTableId(testTableId);
        secondControl.setCheckItem("1");
        secondControl.setDescription("状态检查");
        secondControl.setCheckPriority(2);
        secondControl.setResponsePriority(2);
        secondControl.setStatus("1");
        secondControl.setOrganizationNumber(testOrganizationNumber);

        AuthCheckControlResDTO secondControlDTO = new AuthCheckControlResDTO();
        secondControlDTO.setId("987654321");
        secondControlDTO.setTableId(testTableId);
        secondControlDTO.setCheckItem("1");
        secondControlDTO.setDescription("状态检查");
        secondControlDTO.setCheckPriority(2);
        secondControlDTO.setResponsePriority(2);
        secondControlDTO.setOrganizationNumber(testOrganizationNumber);

        List<ParmAuthCheckControl> multipleRecords = Arrays.asList(testParmAuthCheckControl, secondControl);
        List<AuthCheckControlResDTO> multipleDTOs = Arrays.asList(testAuthCheckControlResDTO, secondControlDTO);

        when(parmAuthCheckControlSelfMapper.findByOrgAndTableId(testOrganizationNumber, testTableId))
                .thenReturn(multipleRecords);

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copyList(multipleRecords, AuthCheckControlResDTO.class))
                    .thenReturn(multipleDTOs);

            // Act
            List<AuthCheckControlResDTO> result = authCheckControlService.findByOrgAndTableId(testOrganizationNumber, testTableId);

            // Assert
            assertNotNull(result);
            assertEquals(2, result.size());
            assertEquals("123456789", result.get(0).getId());
            assertEquals("987654321", result.get(1).getId());
            assertEquals("0", result.get(0).getCheckItem());
            assertEquals("1", result.get(1).getCheckItem());
            
            verify(parmAuthCheckControlSelfMapper).findByOrgAndTableId(testOrganizationNumber, testTableId);
            beanMappingMock.verify(() -> BeanMapping.copyList(multipleRecords, AuthCheckControlResDTO.class));
        }
    }

    /**
     * 测试 findByOrgAndTableId - 没有找到数据
     */
    @Test
    void testFindByOrgAndTableId_NoDataFound() {
        // Arrange
        when(parmAuthCheckControlSelfMapper.findByOrgAndTableId(testOrganizationNumber, testTableId))
                .thenReturn(Collections.emptyList());

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () ->
                authCheckControlService.findByOrgAndTableId(testOrganizationNumber, testTableId));

        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_AUTH_CHECK_CONTROL_FAULT.getCode(), exception.getErrCode());
        verify(parmAuthCheckControlSelfMapper).findByOrgAndTableId(testOrganizationNumber, testTableId);
    }

    /**
     * 测试 findByOrgAndTableId - 机构号为null
     */
    @Test
    void testFindByOrgAndTableId_NullOrganizationNumber() {
        // Arrange
        when(parmAuthCheckControlSelfMapper.findByOrgAndTableId(null, testTableId))
                .thenReturn(Collections.emptyList());

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () ->
                authCheckControlService.findByOrgAndTableId(null, testTableId));

        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_AUTH_CHECK_CONTROL_FAULT.getCode(), exception.getErrCode());
        verify(parmAuthCheckControlSelfMapper).findByOrgAndTableId(null, testTableId);
    }

    /**
     * 测试 findByOrgAndTableId - 参数表ID为null
     */
    @Test
    void testFindByOrgAndTableId_NullTableId() {
        // Arrange
        when(parmAuthCheckControlSelfMapper.findByOrgAndTableId(testOrganizationNumber, null))
                .thenReturn(Collections.emptyList());

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () ->
                authCheckControlService.findByOrgAndTableId(testOrganizationNumber, null));

        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_AUTH_CHECK_CONTROL_FAULT.getCode(), exception.getErrCode());
        verify(parmAuthCheckControlSelfMapper).findByOrgAndTableId(testOrganizationNumber, null);
    }

    /**
     * 测试 findByOrgAndTableId - 机构号为空字符串
     */
    @Test
    void testFindByOrgAndTableId_EmptyOrganizationNumber() {
        // Arrange
        String emptyOrgNumber = "";
        when(parmAuthCheckControlSelfMapper.findByOrgAndTableId(emptyOrgNumber, testTableId))
                .thenReturn(Collections.emptyList());

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () ->
                authCheckControlService.findByOrgAndTableId(emptyOrgNumber, testTableId));

        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_AUTH_CHECK_CONTROL_FAULT.getCode(), exception.getErrCode());
        verify(parmAuthCheckControlSelfMapper).findByOrgAndTableId(emptyOrgNumber, testTableId);
    }

    /**
     * 测试 findByOrgAndTableId - 参数表ID为空字符串
     */
    @Test
    void testFindByOrgAndTableId_EmptyTableId() {
        // Arrange
        String emptyTableId = "";
        when(parmAuthCheckControlSelfMapper.findByOrgAndTableId(testOrganizationNumber, emptyTableId))
                .thenReturn(Collections.emptyList());

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () ->
                authCheckControlService.findByOrgAndTableId(testOrganizationNumber, emptyTableId));

        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_AUTH_CHECK_CONTROL_FAULT.getCode(), exception.getErrCode());
        verify(parmAuthCheckControlSelfMapper).findByOrgAndTableId(testOrganizationNumber, emptyTableId);
    }

    /**
     * 测试 findByOrgAndTableId - 数据库异常
     */
    @Test
    void testFindByOrgAndTableId_DatabaseException() {
        // Arrange
        when(parmAuthCheckControlSelfMapper.findByOrgAndTableId(testOrganizationNumber, testTableId))
                .thenThrow(new RuntimeException("Database connection error"));

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () ->
                authCheckControlService.findByOrgAndTableId(testOrganizationNumber, testTableId));

        assertEquals("Database connection error", exception.getMessage());
        verify(parmAuthCheckControlSelfMapper).findByOrgAndTableId(testOrganizationNumber, testTableId);
    }

    /**
     * 测试 findByOrgAndTableId - BeanMapping转换异常
     */
    @Test
    void testFindByOrgAndTableId_BeanMappingException() {
        // Arrange
        when(parmAuthCheckControlSelfMapper.findByOrgAndTableId(testOrganizationNumber, testTableId))
                .thenReturn(testParmAuthCheckControlList);

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copyList(testParmAuthCheckControlList, AuthCheckControlResDTO.class))
                    .thenThrow(new RuntimeException("Bean mapping error"));

            // Act & Assert
            RuntimeException exception = assertThrows(RuntimeException.class, () ->
                    authCheckControlService.findByOrgAndTableId(testOrganizationNumber, testTableId));

            assertEquals("Bean mapping error", exception.getMessage());
            verify(parmAuthCheckControlSelfMapper).findByOrgAndTableId(testOrganizationNumber, testTableId);
            beanMappingMock.verify(() -> BeanMapping.copyList(testParmAuthCheckControlList, AuthCheckControlResDTO.class));
        }
    }
} 
