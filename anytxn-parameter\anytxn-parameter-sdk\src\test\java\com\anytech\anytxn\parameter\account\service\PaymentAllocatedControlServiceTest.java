package com.anytech.anytxn.parameter.account.service;

import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.account.mapper.ParmPaymentAllocatedControlMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmPaymentAllocatedControlSelfMapper;
import com.anytech.anytxn.parameter.account.service.PaymentAllocatedControlServiceImpl;
import com.anytech.anytxn.parameter.base.account.domain.dto.PaymentAllocatedControlReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.PaymentAllocatedControlResDTO;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmPaymentAllocatedControl;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * PaymentAllocatedControlService单元测试类
 * 
 * <AUTHOR>
 * @date 2025-06-30
 */
@ExtendWith(MockitoExtension.class)
class PaymentAllocatedControlServiceTest {

    @Mock
    private ParmPaymentAllocatedControlMapper parmPaymentAllocatedControlMapper;

    @Mock
    private ParmPaymentAllocatedControlSelfMapper parmPaymentAllocatedControlSelfMapper;

    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private PaymentAllocatedControlServiceImpl paymentAllocatedControlService;

    private PaymentAllocatedControlReqDTO mockReqDTO;
    private PaymentAllocatedControlResDTO mockResDTO;
    private ParmPaymentAllocatedControl mockControl;

    @BeforeEach
    void setUp() throws Exception {
        // 初始化OrgNumberUtils的静态实例以避免NullPointerException
        Field orgNumberUtilField = OrgNumberUtils.class.getDeclaredField("orgNumberUtil");
        orgNumberUtilField.setAccessible(true);
        OrgNumberUtils orgNumberUtilInstance = new OrgNumberUtils();
        orgNumberUtilField.set(null, orgNumberUtilInstance);

        // 构建测试用的PaymentAllocatedControlReqDTO
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMocked = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMocked.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            mockReqDTO = new PaymentAllocatedControlReqDTO();
            mockReqDTO.setOrganizationNumber("ORG001");
            mockReqDTO.setTableId("PAY001");
            mockReqDTO.setTransactionTypeCode("TXN001");
            mockReqDTO.setStatementFlag("1");
            mockReqDTO.setPriority(1);
            mockReqDTO.setDescription("测试还款分配控制");
        }

        // 构建测试用的ParmPaymentAllocatedControl
        mockControl = new ParmPaymentAllocatedControl();
        mockControl.setId(1001L);
        mockControl.setOrganizationNumber("ORG001");
        mockControl.setTableId("PAY001");
        mockControl.setTransactionTypeCode("TXN001");
        mockControl.setStatementFlag("1");
        mockControl.setPriority(1);
        mockControl.setDescription("测试还款分配控制");
        mockControl.setStatus("1");
        mockControl.setLargeGraceFlag("0");
        mockControl.setAbsStatusFlag("N");
        mockControl.setVersionNumber(1L);
        mockControl.setCreateTime(LocalDateTime.now());
        mockControl.setUpdateTime(LocalDateTime.now());

        // 构建测试用的PaymentAllocatedControlResDTO
        mockResDTO = new PaymentAllocatedControlResDTO();
        mockResDTO.setId(1001L);
        mockResDTO.setOrganizationNumber("ORG001");
        mockResDTO.setTableId("PAY001");
        mockResDTO.setTransactionTypeCode("TXN001");
        mockResDTO.setStatementFlag("1");
        mockResDTO.setPriority(1);
        mockResDTO.setDescription("测试还款分配控制");
    }

    /**
     * 测试 addPaymentAllocatedControl 方法 - 成功路径
     */
    @Test
    void testAddPaymentAllocatedControl_Success() {
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class);
             MockedStatic<TenantUtils> tenantUtils = mockStatic(TenantUtils.class)) {
            
            // Given
            orgUtils.when(() -> OrgNumberUtils.getOrg(mockReqDTO.getOrganizationNumber())).thenReturn("ORG001");
            tenantUtils.when(TenantUtils::getTenantId).thenReturn("TENANT001");
            
            when(parmPaymentAllocatedControlSelfMapper.selectByMultipleConditions(
                    "ORG001", mockReqDTO.getTableId(), mockReqDTO.getStatementFlag(), mockReqDTO.getTransactionTypeCode()))
                    .thenReturn(null);
            when(numberIdGenerator.generateId("TENANT001")).thenReturn(1001L);
            
            beanMapping.when(() -> BeanMapping.copy(mockReqDTO, ParmPaymentAllocatedControl.class))
                    .thenReturn(mockControl);
            beanMapping.when(() -> BeanMapping.copy(mockControl, PaymentAllocatedControlResDTO.class))
                    .thenReturn(mockResDTO);
            
            when(parmPaymentAllocatedControlMapper.insert(any(ParmPaymentAllocatedControl.class))).thenReturn(1);

            // When
            PaymentAllocatedControlResDTO result = paymentAllocatedControlService.addPaymentAllocatedControl(mockReqDTO);

            // Then
            assertNotNull(result);
            assertEquals(mockResDTO.getId(), result.getId());
            verify(parmPaymentAllocatedControlSelfMapper).selectByMultipleConditions(
                    "ORG001", mockReqDTO.getTableId(), mockReqDTO.getStatementFlag(), mockReqDTO.getTransactionTypeCode());
            verify(parmPaymentAllocatedControlMapper).insert(any(ParmPaymentAllocatedControl.class));
        }
    }

    /**
     * 测试 addPaymentAllocatedControl 方法 - 数据已存在
     */
    @Test
    void testAddPaymentAllocatedControl_AlreadyExists() {
        try (MockedStatic<OrgNumberUtils> orgUtils = mockStatic(OrgNumberUtils.class)) {
            // Given
            orgUtils.when(() -> OrgNumberUtils.getOrg(mockReqDTO.getOrganizationNumber())).thenReturn("ORG001");
            
            when(parmPaymentAllocatedControlSelfMapper.selectByMultipleConditions(
                    "ORG001", mockReqDTO.getTableId(), mockReqDTO.getStatementFlag(), mockReqDTO.getTransactionTypeCode()))
                    .thenReturn(mockControl);

            // When & Then
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                    () -> paymentAllocatedControlService.addPaymentAllocatedControl(mockReqDTO));
            
            assertEquals(AnyTxnParameterRespCodeEnum.D_EXIST_PARM_PAYMENT_ALLOCATED_CONTROL_FAULT.getCode(),
                    exception.getErrCode());
            verify(parmPaymentAllocatedControlSelfMapper).selectByMultipleConditions(
                    "ORG001", mockReqDTO.getTableId(), mockReqDTO.getStatementFlag(), mockReqDTO.getTransactionTypeCode());
            verifyNoInteractions(parmPaymentAllocatedControlMapper);
        }
    }

    /**
     * 测试 removePaymentAllocatedControl 方法 - 成功路径
     */
    @Test
    void testRemovePaymentAllocatedControl_Success() {
        // Given
        Long id = 1001L;
        when(parmPaymentAllocatedControlMapper.selectByPrimaryKey(id)).thenReturn(mockControl);
        when(parmPaymentAllocatedControlMapper.deleteByPrimaryKey(id)).thenReturn(1);

        // When
        Boolean result = paymentAllocatedControlService.removePaymentAllocatedControl(id);

        // Then
        assertTrue(result);
        verify(parmPaymentAllocatedControlMapper).selectByPrimaryKey(id);
        verify(parmPaymentAllocatedControlMapper).deleteByPrimaryKey(id);
    }

    /**
     * 测试 removePaymentAllocatedControl 方法 - 数据不存在
     */
    @Test
    void testRemovePaymentAllocatedControl_NotFound() {
        // Given
        Long id = 9999L;
        when(parmPaymentAllocatedControlMapper.selectByPrimaryKey(id)).thenReturn(null);

        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> paymentAllocatedControlService.removePaymentAllocatedControl(id));
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_PAYMENT_ALLOCATED_CONTROL_FAULT.getCode(),
                exception.getErrCode());
        verify(parmPaymentAllocatedControlMapper).selectByPrimaryKey(id);
        verify(parmPaymentAllocatedControlMapper, never()).deleteByPrimaryKey(any());
    }

    /**
     * 测试 removePaymentAllocatedControl 方法 - 删除失败
     */
    @Test
    void testRemovePaymentAllocatedControl_DeleteFailed() {
        // Given
        Long id = 1001L;
        when(parmPaymentAllocatedControlMapper.selectByPrimaryKey(id)).thenReturn(mockControl);
        when(parmPaymentAllocatedControlMapper.deleteByPrimaryKey(id)).thenReturn(0);

        // When
        Boolean result = paymentAllocatedControlService.removePaymentAllocatedControl(id);

        // Then
        assertFalse(result);
        verify(parmPaymentAllocatedControlMapper).selectByPrimaryKey(id);
        verify(parmPaymentAllocatedControlMapper).deleteByPrimaryKey(id);
    }

    /**
     * 测试 removePaymentAllocatedControl 方法 - null参数
     */
    @Test
    void testRemovePaymentAllocatedControl_NullId() {
        // Given
        when(parmPaymentAllocatedControlMapper.deleteByPrimaryKey(null)).thenReturn(0);

        // When
        Boolean result = paymentAllocatedControlService.removePaymentAllocatedControl(null);

        // Then
        assertFalse(result);
        verify(parmPaymentAllocatedControlMapper, never()).selectByPrimaryKey(any());
        verify(parmPaymentAllocatedControlMapper).deleteByPrimaryKey(null);
    }

    /**
     * 测试 findByOrgAndTableId 方法 - 成功路径
     */
    @Test
    void testFindByOrgAndTableId_Success() {
        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            // Given
            String orgNum = "ORG001";
            String tableId = "PAY001";
            List<ParmPaymentAllocatedControl> controlList = Arrays.asList(mockControl);
            List<PaymentAllocatedControlResDTO> resList = Arrays.asList(mockResDTO);
            
            when(parmPaymentAllocatedControlSelfMapper.seletByOrgAndTableId(orgNum, tableId))
                    .thenReturn(controlList);
            beanMapping.when(() -> BeanMapping.copyList(controlList, PaymentAllocatedControlResDTO.class))
                    .thenReturn(resList);

            // When
            List<PaymentAllocatedControlResDTO> result = paymentAllocatedControlService.findByOrgAndTableId(orgNum, tableId);

            // Then
            assertNotNull(result);
            assertFalse(result.isEmpty());
            assertEquals(1, result.size());
            assertEquals(mockResDTO.getId(), result.get(0).getId());
            verify(parmPaymentAllocatedControlSelfMapper).seletByOrgAndTableId(orgNum, tableId);
        }
    }

    /**
     * 测试 findByOrgAndTableId 方法 - 数据为空
     */
    @Test
    void testFindByOrgAndTableId_EmptyList() {
        // Given
        String orgNum = "ORG001";
        String tableId = "NONEXISTENT";
        when(parmPaymentAllocatedControlSelfMapper.seletByOrgAndTableId(orgNum, tableId))
                .thenReturn(Collections.emptyList());

        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> paymentAllocatedControlService.findByOrgAndTableId(orgNum, tableId));
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_PAYMENT_ALLOCATED_CONTROL_BY_ID_ORG_FAULT.getCode(),
                exception.getErrCode());
        verify(parmPaymentAllocatedControlSelfMapper).seletByOrgAndTableId(orgNum, tableId);
    }

    /**
     * 测试 findByMultipleConditions 方法 - 成功路径
     */
    @Test
    void testFindByMultipleConditions_Success() {
        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            // Given
            String orgNumber = "ORG001";
            String tableId = "PAY001";
            String statementFlag = "1";
            String transactionTypeCode = "TXN001";
            String largeGraceFlag = "0";
            String absStatus = "N";
            
            when(parmPaymentAllocatedControlSelfMapper.selectByOrgTidSflagTcodeLgflagAndAbs(
                    orgNumber, tableId, statementFlag, transactionTypeCode, largeGraceFlag, absStatus))
                    .thenReturn(mockControl);
            beanMapping.when(() -> BeanMapping.copy(mockControl, PaymentAllocatedControlResDTO.class))
                    .thenReturn(mockResDTO);

            // When
            PaymentAllocatedControlResDTO result = paymentAllocatedControlService.findByMultipleConditions(
                    orgNumber, tableId, statementFlag, transactionTypeCode, largeGraceFlag, absStatus);

            // Then
            assertNotNull(result);
            assertEquals(mockResDTO.getId(), result.getId());
            verify(parmPaymentAllocatedControlSelfMapper).selectByOrgTidSflagTcodeLgflagAndAbs(
                    orgNumber, tableId, statementFlag, transactionTypeCode, largeGraceFlag, absStatus);
        }
    }

    /**
     * 测试 findByMultipleConditions 方法 - 数据不存在
     */
    @Test
    void testFindByMultipleConditions_NotFound() {
        // Given
        String orgNumber = "ORG001";
        String tableId = "NONEXISTENT";
        String statementFlag = "1";
        String transactionTypeCode = "TXN001";
        String largeGraceFlag = "0";
        String absStatus = "N";
        
        when(parmPaymentAllocatedControlSelfMapper.selectByOrgTidSflagTcodeLgflagAndAbs(
                orgNumber, tableId, statementFlag, transactionTypeCode, largeGraceFlag, absStatus))
                .thenReturn(null);

        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> paymentAllocatedControlService.findByMultipleConditions(
                        orgNumber, tableId, statementFlag, transactionTypeCode, largeGraceFlag, absStatus));
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT.getCode(),
                exception.getErrCode());
        verify(parmPaymentAllocatedControlSelfMapper).selectByOrgTidSflagTcodeLgflagAndAbs(
                orgNumber, tableId, statementFlag, transactionTypeCode, largeGraceFlag, absStatus);
    }

    /**
     * 测试 findByMultipleConditions 方法 - 边界条件：空字符串参数
     */
    @Test
    void testFindByMultipleConditions_EmptyStringParams() {
        try (MockedStatic<BeanMapping> beanMapping = mockStatic(BeanMapping.class)) {
            // Given
            String orgNumber = "";
            String tableId = "";
            String statementFlag = "";
            String transactionTypeCode = "";
            String largeGraceFlag = "";
            String absStatus = "";
            
            when(parmPaymentAllocatedControlSelfMapper.selectByOrgTidSflagTcodeLgflagAndAbs(
                    orgNumber, tableId, statementFlag, transactionTypeCode, largeGraceFlag, absStatus))
                    .thenReturn(mockControl);
            beanMapping.when(() -> BeanMapping.copy(mockControl, PaymentAllocatedControlResDTO.class))
                    .thenReturn(mockResDTO);

            // When
            PaymentAllocatedControlResDTO result = paymentAllocatedControlService.findByMultipleConditions(
                    orgNumber, tableId, statementFlag, transactionTypeCode, largeGraceFlag, absStatus);

            // Then
            assertNotNull(result);
            verify(parmPaymentAllocatedControlSelfMapper).selectByOrgTidSflagTcodeLgflagAndAbs(
                    orgNumber, tableId, statementFlag, transactionTypeCode, largeGraceFlag, absStatus);
        }
    }

    /**
     * 测试 findByMultipleConditions 方法 - 边界条件：null参数
     */
    @Test
    void testFindByMultipleConditions_NullParams() {
        // Given
        when(parmPaymentAllocatedControlSelfMapper.selectByOrgTidSflagTcodeLgflagAndAbs(
                null, null, null, null, null, null))
                .thenReturn(null);

        // When & Then
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> paymentAllocatedControlService.findByMultipleConditions(
                        null, null, null, null, null, null));
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT.getCode(),
                exception.getErrCode());
        verify(parmPaymentAllocatedControlSelfMapper).selectByOrgTidSflagTcodeLgflagAndAbs(
                null, null, null, null, null, null);
    }
} 
