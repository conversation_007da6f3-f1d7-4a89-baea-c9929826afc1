package com.anytech.anytxn.parameter.accounting.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.accounting.mapper.TPmsGlamsControlMapper;
import com.anytech.anytxn.parameter.accounting.mapper.TPmsGlamsControlSelfMapper;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.TPmsGlamsControlDTO;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.TPmsGlamsDefinitionDTO;
import com.anytech.anytxn.parameter.base.accounting.domain.model.TPmsGlamsControl;
import com.anytech.anytxn.parameter.base.accounting.service.ITPmsGlamsDefinitionService;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.github.pagehelper.PageHelper;
import org.apache.commons.lang3.ObjectUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cglib.beans.BeanCopier;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * TPmsGlamsControlService Unit Test Class
 *
 * <AUTHOR>
 * @date 2019-10-09
 */
@ExtendWith(MockitoExtension.class)
class TPmsGlamsControlServiceTest {

    @Mock
    private TPmsGlamsControlSelfMapper tPmsGlamsControlSelfMapper;
    
    @Mock
    private TPmsGlamsControlMapper tPmsGlamsControlMapper;
    
    @Mock
    private Number16IdGen numberIdGenerator;
    
    @Mock
    private ITPmsGlamsDefinitionService pmsGlamsDefinitionService;

    @InjectMocks
    private TPmsGlamsControlServiceImpl tPmsGlamsControlService;

    private MockedStatic<OrgNumberUtils> orgNumberUtilsMockedStatic;
    private MockedStatic<TenantUtils> tenantUtilsMockedStatic;
    private MockedStatic<PageHelper> pageHelperMockedStatic;
    private MockedStatic<BeanCopier> beanCopierMockedStatic;
    private MockedStatic<ObjectUtils> objectUtilsMockedStatic;
    private MockedStatic<JSON> jsonMockedStatic;

    @BeforeEach
    void setUp() {
        orgNumberUtilsMockedStatic = mockStatic(OrgNumberUtils.class);
        tenantUtilsMockedStatic = mockStatic(TenantUtils.class);
        pageHelperMockedStatic = mockStatic(PageHelper.class);
        beanCopierMockedStatic = mockStatic(BeanCopier.class);
        objectUtilsMockedStatic = mockStatic(ObjectUtils.class);
        jsonMockedStatic = mockStatic(JSON.class);

        // Set default return values for static methods
        orgNumberUtilsMockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
        tenantUtilsMockedStatic.when(TenantUtils::getTenantId).thenReturn("tenant001");
    }

    @Test
    void testFindByTableId_Success() {
        // Arrange
        String tableId = "TEST_TABLE_001";
        String orgNumber = "001";
        
        TPmsGlamsControl control1 = createMockTPmsGlamsControl("1", "001", tableId);
        TPmsGlamsControl control2 = createMockTPmsGlamsControl("2", "001", tableId);
        List<TPmsGlamsControl> controlList = List.of(control1, control2);
        
        BeanCopier mockBeanCopier = mock(BeanCopier.class);
        
        Mockito.lenient().when(tPmsGlamsControlSelfMapper.selectTableId(tableId, orgNumber))
                .thenReturn(controlList);
        beanCopierMockedStatic.when(() -> BeanCopier.create(TPmsGlamsControl.class, TPmsGlamsControlDTO.class, false))
                .thenReturn(mockBeanCopier);
        
        // Act
        List<TPmsGlamsControlDTO> result = tPmsGlamsControlService.findByTableId(tableId);
        
        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        verify(tPmsGlamsControlSelfMapper).selectTableId(tableId, orgNumber);
    }

    @Test
    void testFindByTableId_EmptyResult() {
        // Arrange
        String tableId = "EMPTY_TABLE";
        String orgNumber = "001";
        
        Mockito.lenient().when(tPmsGlamsControlSelfMapper.selectTableId(tableId, orgNumber))
                .thenReturn(null);
        
        // Act
        List<TPmsGlamsControlDTO> result = tPmsGlamsControlService.findByTableId(tableId);
        
        // Assert
        assertNull(result);
        verify(tPmsGlamsControlSelfMapper).selectTableId(tableId, orgNumber);
    }

    @Test
    void testFindByDefine_Success() {
        // Arrange
        String organizationNumber = "001";
        String branchid = "001";
        String tableId = "TEST_TABLE_001";
        
        TPmsGlamsControl control = createMockTPmsGlamsControl("1", branchid, tableId);
        List<TPmsGlamsControl> controlList = List.of(control);
        
        BeanCopier mockBeanCopier = mock(BeanCopier.class);
        
        Mockito.lenient().when(tPmsGlamsControlSelfMapper.selectByDefine(organizationNumber, branchid, tableId))
                .thenReturn(controlList);
        beanCopierMockedStatic.when(() -> BeanCopier.create(TPmsGlamsControl.class, TPmsGlamsControlDTO.class, false))
                .thenReturn(mockBeanCopier);
        
        // Act
        List<TPmsGlamsControlDTO> result = tPmsGlamsControlService.findByDefine(organizationNumber, branchid, tableId);
        
        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(tPmsGlamsControlSelfMapper).selectByDefine(organizationNumber, branchid, tableId);
    }

    @Test
    void testFindByDefine_NullResult() {
        // Arrange
        String organizationNumber = "001";
        String branchid = "001";
        String tableId = "NULL_TABLE";
        
        Mockito.lenient().when(tPmsGlamsControlSelfMapper.selectByDefine(organizationNumber, branchid, tableId))
                .thenReturn(null);
        
        // Act
        List<TPmsGlamsControlDTO> result = tPmsGlamsControlService.findByDefine(organizationNumber, branchid, tableId);
        
        // Assert
        assertNull(result);
        verify(tPmsGlamsControlSelfMapper).selectByDefine(organizationNumber, branchid, tableId);
    }

    /**
     * 创建模拟的 TPmsGlamsControl 对象
     */
    private TPmsGlamsControl createMockTPmsGlamsControl(String id, String branchid, String tableId) {
        TPmsGlamsControl control = new TPmsGlamsControl();
        control.setId(id);
        control.setBranchid(branchid);
        control.setTableId(tableId);
        control.setDrcr("D");
        control.setGlAcct("1001");
        control.setPriceTaxInd("1");
        control.setGlAcctName("Test Account");
        control.setRbLogo("1");
        control.setCreateTime(LocalDateTime.now());
        control.setUpdateTime(LocalDateTime.now());
        control.setUpdateBy("testUser");
        control.setVersionNumber(1L);
        control.setOrganizationNumber("001");
        return control;
    }

    /**
     * Create mock TPmsGlamsControlDTO object
     */
    private TPmsGlamsControlDTO createMockTPmsGlamsControlDTO(String id, String branchid, String tableId) {
        TPmsGlamsControlDTO dto = new TPmsGlamsControlDTO();
        dto.setId(id);
        dto.setBranchid(branchid);
        dto.setTableId(tableId);
        dto.setDrcr("D");
        dto.setGlAcct("1001");
        dto.setPriceTaxInd("1");
        dto.setGlAcctName("Test Account");
        dto.setRbLogo("1");
        dto.setCreateTime(LocalDateTime.now());
        dto.setUpdateTime(LocalDateTime.now());
        dto.setUpdateBy("testUser");
        dto.setVersionNumber(1L);
        dto.setOrganizationNumber("001");
        return dto;
    }

    @Test
    void testPage_Success() {
        // Arrange
        int page = 1;
        int rows = 10;
        String orgNumber = "001";

        TPmsGlamsControl control = createMockTPmsGlamsControl("1", "001", "TEST_TABLE");
        List<TPmsGlamsControl> controlList = List.of(control);

        BeanCopier mockBeanCopier = mock(BeanCopier.class);

        // Skip PageHelper mock for now - let it use real implementation
        Mockito.lenient().when(tPmsGlamsControlSelfMapper.selectAll(false, orgNumber))
                .thenReturn(controlList);
        beanCopierMockedStatic.when(() -> BeanCopier.create(TPmsGlamsControl.class, TPmsGlamsControlDTO.class, false))
                .thenReturn(mockBeanCopier);

        // Act
        PageResultDTO<TPmsGlamsControlDTO> result = tPmsGlamsControlService.page(page, rows);

        // Assert
        assertNotNull(result);
        assertEquals(page, result.getPage());
        assertEquals(rows, result.getRows());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().size());
        verify(tPmsGlamsControlSelfMapper).selectAll(false, orgNumber);
    }

    @Test
    void testPage_EmptyList() {
        // Arrange
        int page = 1;
        int rows = 10;
        String orgNumber = "001";

        List<TPmsGlamsControl> emptyList = Collections.emptyList();

        Mockito.lenient().when(tPmsGlamsControlSelfMapper.selectAll(false, orgNumber))
                .thenReturn(emptyList);

        // Act
        PageResultDTO<TPmsGlamsControlDTO> result = tPmsGlamsControlService.page(page, rows);

        // Assert
        assertNotNull(result);
        assertEquals(page, result.getPage());
        assertEquals(rows, result.getRows());
        assertNull(result.getData());
        verify(tPmsGlamsControlSelfMapper).selectAll(false, orgNumber);
    }

    @Test
    void testPage_Exception() {
        // Arrange
        int page = 1;
        int rows = 10;
        String orgNumber = "001";

        Mockito.lenient().when(tPmsGlamsControlSelfMapper.selectAll(false, orgNumber))
                .thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> tPmsGlamsControlService.page(page, rows));

        assertEquals(AnyTxnParameterRespCodeEnum.D_PAGE_TPMS_GLAMS_CONTROL_FAULT.getCode(), exception.getErrCode());
        verify(tPmsGlamsControlSelfMapper).selectAll(false, orgNumber);
    }

    @Test
    void testDetail_Success() {
        // Arrange
        String id = "TEST_ID_001";
        TPmsGlamsControl control = createMockTPmsGlamsControl(id, "001", "TEST_TABLE");
        BeanCopier mockBeanCopier = mock(BeanCopier.class);

        Mockito.lenient().when(tPmsGlamsControlMapper.selectByPrimaryKey(id))
                .thenReturn(control);
        beanCopierMockedStatic.when(() -> BeanCopier.create(TPmsGlamsControl.class, TPmsGlamsControlDTO.class, false))
                .thenReturn(mockBeanCopier);

        // Act
        TPmsGlamsControlDTO result = tPmsGlamsControlService.detail(id);

        // Assert
        assertNotNull(result);
        verify(tPmsGlamsControlMapper).selectByPrimaryKey(id);
    }

    @Test
    void testDetail_NotFound() {
        // Arrange
        String id = "NOT_FOUND_ID";

        Mockito.lenient().when(tPmsGlamsControlMapper.selectByPrimaryKey(id))
                .thenReturn(null);

        // Act
        TPmsGlamsControlDTO result = tPmsGlamsControlService.detail(id);

        // Assert
        assertNull(result);
        verify(tPmsGlamsControlMapper).selectByPrimaryKey(id);
    }

    @Test
    void testRemove_Success() {
        // Arrange
        String id = "TEST_ID_001";
        TPmsGlamsControl control = createMockTPmsGlamsControl(id, "001", "TEST_TABLE");

        Mockito.lenient().when(tPmsGlamsControlMapper.selectByPrimaryKey(id))
                .thenReturn(control);
        objectUtilsMockedStatic.when(() -> ObjectUtils.isEmpty(control))
                .thenReturn(false);

        // Act
        ParameterCompare result = tPmsGlamsControlService.remove(id);

        // Assert
        assertNotNull(result);
        verify(tPmsGlamsControlMapper).selectByPrimaryKey(id);
    }

    @Test
    void testRemove_NullId() {
        // Arrange
        String id = null;

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> tPmsGlamsControlService.remove(id));

        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    @Test
    void testRemove_DataNotExists() {
        // Arrange
        String id = "NOT_EXISTS_ID";

        Mockito.lenient().when(tPmsGlamsControlMapper.selectByPrimaryKey(id))
                .thenReturn(null);
        objectUtilsMockedStatic.when(() -> ObjectUtils.isEmpty(null))
                .thenReturn(true);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> tPmsGlamsControlService.remove(id));

        assertEquals(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_TEMPLATE_FAULT.getCode(), exception.getErrCode());
        verify(tPmsGlamsControlMapper).selectByPrimaryKey(id);
    }

    @Test
    void testAdd_Success() {
        // Arrange
        TPmsGlamsControlDTO dto = createMockTPmsGlamsControlDTO("1", "001", "TEST_TABLE");
        Long generatedId = 123456789L;
        String tenantId = "tenant001";

        BeanCopier mockBeanCopier = mock(BeanCopier.class);

        objectUtilsMockedStatic.when(() -> ObjectUtils.isEmpty(dto))
                .thenReturn(false);
        beanCopierMockedStatic.when(() -> BeanCopier.create(TPmsGlamsControlDTO.class, TPmsGlamsControl.class, false))
                .thenReturn(mockBeanCopier);
        Mockito.lenient().when(numberIdGenerator.generateId(tenantId))
                .thenReturn(generatedId);

        // Act
        ParameterCompare result = tPmsGlamsControlService.add(dto);

        // Assert
        assertNotNull(result);
        verify(numberIdGenerator).generateId(tenantId);
    }

    @Test
    void testAdd_EmptyData() {
        // Arrange
        TPmsGlamsControlDTO dto = null;

        objectUtilsMockedStatic.when(() -> ObjectUtils.isEmpty(dto))
                .thenReturn(true);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> tPmsGlamsControlService.add(dto));

        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST.getCode(), exception.getErrCode());
    }

    @Test
    void testUpdate_Success() {
        // Arrange
        TPmsGlamsControlDTO dto = createMockTPmsGlamsControlDTO("1", "001", "TEST_TABLE");
        TPmsGlamsControl existingControl = createMockTPmsGlamsControl("1", "001", "TEST_TABLE");

        BeanCopier mockBeanCopier = mock(BeanCopier.class);

        objectUtilsMockedStatic.when(() -> ObjectUtils.isEmpty(dto))
                .thenReturn(false);
        Mockito.lenient().when(tPmsGlamsControlMapper.selectByPrimaryKey(String.valueOf(dto.getId())))
                .thenReturn(existingControl);
        objectUtilsMockedStatic.when(() -> ObjectUtils.isEmpty(existingControl))
                .thenReturn(false);
        beanCopierMockedStatic.when(() -> BeanCopier.create(TPmsGlamsControlDTO.class, TPmsGlamsControl.class, false))
                .thenReturn(mockBeanCopier);

        // Act
        ParameterCompare result = tPmsGlamsControlService.update(dto);

        // Assert
        assertNotNull(result);
        verify(tPmsGlamsControlMapper).selectByPrimaryKey(String.valueOf(dto.getId()));
    }

    @Test
    void testUpdate_EmptyData() {
        // Arrange
        TPmsGlamsControlDTO dto = null;

        objectUtilsMockedStatic.when(() -> ObjectUtils.isEmpty(dto))
                .thenReturn(true);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> tPmsGlamsControlService.update(dto));

        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    @Test
    void testUpdate_DataNotExists() {
        // Arrange
        TPmsGlamsControlDTO dto = createMockTPmsGlamsControlDTO("1", "001", "TEST_TABLE");

        objectUtilsMockedStatic.when(() -> ObjectUtils.isEmpty(dto))
                .thenReturn(false);
        Mockito.lenient().when(tPmsGlamsControlMapper.selectByPrimaryKey(String.valueOf(dto.getId())))
                .thenReturn(null);
        objectUtilsMockedStatic.when(() -> ObjectUtils.isEmpty(null))
                .thenReturn(true);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> tPmsGlamsControlService.update(dto));

        assertEquals(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_TEMPLATE_FAULT.getCode(), exception.getErrCode());
        verify(tPmsGlamsControlMapper).selectByPrimaryKey(String.valueOf(dto.getId()));
    }

    @Test
    void testUpdateDb_Success() throws Exception {
        // Arrange
        ParmModificationRecord record = new ParmModificationRecord();
        TPmsGlamsControl control = createMockTPmsGlamsControl("1", "001", "TEST_TABLE");
        String jsonBody = "{\"id\":\"1\",\"branchid\":\"001\",\"tableId\":\"TEST_TABLE\"}";

        record.setParmBody(jsonBody);

        jsonMockedStatic.when(() -> JSON.parseObject(jsonBody, TPmsGlamsControl.class))
                .thenReturn(control);
        Mockito.lenient().when(tPmsGlamsControlMapper.updateByPrimaryKeySelective(any(TPmsGlamsControl.class)))
                .thenReturn(1);

        // Act
        boolean result = tPmsGlamsControlService.updateDb(record);

        // Assert
        assertTrue(result);
        verify(tPmsGlamsControlMapper).updateByPrimaryKeySelective(any(TPmsGlamsControl.class));
    }

    @Test
    void testUpdateDb_Exception() {
        // Arrange
        ParmModificationRecord record = new ParmModificationRecord();
        TPmsGlamsControl control = createMockTPmsGlamsControl("1", "001", "TEST_TABLE");
        String jsonBody = "{\"id\":\"1\",\"branchid\":\"001\",\"tableId\":\"TEST_TABLE\"}";

        record.setParmBody(jsonBody);

        jsonMockedStatic.when(() -> JSON.parseObject(jsonBody, TPmsGlamsControl.class))
                .thenReturn(control);
        Mockito.lenient().when(tPmsGlamsControlMapper.updateByPrimaryKeySelective(any(TPmsGlamsControl.class)))
                .thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> tPmsGlamsControlService.updateDb(record));

        assertEquals(AnyTxnParameterRespCodeEnum.D_INSERT_TPMS_GLAMS_CONTROL_FAULT.getCode(), exception.getErrCode());
        verify(tPmsGlamsControlMapper).updateByPrimaryKeySelective(any(TPmsGlamsControl.class));
    }

    @Test
    void testInsertDb_Success() throws Exception {
        // Arrange
        ParmModificationRecord record = new ParmModificationRecord();
        TPmsGlamsControl control = createMockTPmsGlamsControl("1", "001", "TEST_TABLE");
        String jsonBody = "{\"id\":\"1\",\"branchid\":\"001\",\"tableId\":\"TEST_TABLE\"}";

        record.setParmBody(jsonBody);

        jsonMockedStatic.when(() -> JSON.parseObject(jsonBody, TPmsGlamsControl.class))
                .thenReturn(control);
        Mockito.lenient().when(tPmsGlamsControlMapper.insert(any(TPmsGlamsControl.class)))
                .thenReturn(1);

        // Act
        boolean result = tPmsGlamsControlService.insertDb(record);

        // Assert
        assertTrue(result);
        verify(tPmsGlamsControlMapper).insert(any(TPmsGlamsControl.class));
    }

    @Test
    void testInsertDb_Exception() {
        // Arrange
        ParmModificationRecord record = new ParmModificationRecord();
        TPmsGlamsControl control = createMockTPmsGlamsControl("1", "001", "TEST_TABLE");
        String jsonBody = "{\"id\":\"1\",\"branchid\":\"001\",\"tableId\":\"TEST_TABLE\"}";

        record.setParmBody(jsonBody);

        jsonMockedStatic.when(() -> JSON.parseObject(jsonBody, TPmsGlamsControl.class))
                .thenReturn(control);
        Mockito.lenient().when(tPmsGlamsControlMapper.insert(any(TPmsGlamsControl.class)))
                .thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> tPmsGlamsControlService.insertDb(record));

        assertEquals(AnyTxnParameterRespCodeEnum.D_INSERT_TPMS_GLAMS_CONTROL_FAULT.getCode(), exception.getErrCode());
        verify(tPmsGlamsControlMapper).insert(any(TPmsGlamsControl.class));
    }

    @Test
    void testDeleteDb_Success() throws Exception {
        // Arrange
        ParmModificationRecord record = new ParmModificationRecord();
        TPmsGlamsControl control = createMockTPmsGlamsControl("1", "001", "TEST_TABLE");
        TPmsGlamsDefinitionDTO definitionDTO = createMockTPmsGlamsDefinitionDTO("1", "001", "001", "TEST_TABLE");
        String jsonBody = "{\"id\":\"1\",\"branchid\":\"001\",\"tableId\":\"TEST_TABLE\"}";

        record.setParmBody(jsonBody);

        jsonMockedStatic.when(() -> JSON.parseObject(jsonBody, TPmsGlamsControl.class))
                .thenReturn(control);
        Mockito.lenient().when(tPmsGlamsControlMapper.deleteByPrimaryKey(control.getId()))
                .thenReturn(1);
        Mockito.lenient().when(pmsGlamsDefinitionService.detail(control.getId()))
                .thenReturn(definitionDTO);
        Mockito.lenient().when(tPmsGlamsControlSelfMapper.deleteByDefine(
                definitionDTO.getOrganizationNumber(),
                definitionDTO.getBranchid(),
                definitionDTO.getTableId()))
                .thenReturn(1);

        // Act
        boolean result = tPmsGlamsControlService.deleteDb(record);

        // Assert
        assertTrue(result);
        verify(tPmsGlamsControlMapper).deleteByPrimaryKey(control.getId());
        verify(pmsGlamsDefinitionService).detail(control.getId());
        verify(tPmsGlamsControlSelfMapper).deleteByDefine(
                definitionDTO.getOrganizationNumber(),
                definitionDTO.getBranchid(),
                definitionDTO.getTableId());
    }

    @Test
    void testDeleteDb_PartialFailure() throws Exception {
        // Arrange
        ParmModificationRecord record = new ParmModificationRecord();
        TPmsGlamsControl control = createMockTPmsGlamsControl("1", "001", "TEST_TABLE");
        TPmsGlamsDefinitionDTO definitionDTO = createMockTPmsGlamsDefinitionDTO("1", "001", "001", "TEST_TABLE");
        String jsonBody = "{\"id\":\"1\",\"branchid\":\"001\",\"tableId\":\"TEST_TABLE\"}";

        record.setParmBody(jsonBody);

        jsonMockedStatic.when(() -> JSON.parseObject(jsonBody, TPmsGlamsControl.class))
                .thenReturn(control);
        Mockito.lenient().when(tPmsGlamsControlMapper.deleteByPrimaryKey(control.getId()))
                .thenReturn(1);
        Mockito.lenient().when(pmsGlamsDefinitionService.detail(control.getId()))
                .thenReturn(definitionDTO);
        Mockito.lenient().when(tPmsGlamsControlSelfMapper.deleteByDefine(
                definitionDTO.getOrganizationNumber(),
                definitionDTO.getBranchid(),
                definitionDTO.getTableId()))
                .thenReturn(0); // Simulate delete failure

        // Act
        boolean result = tPmsGlamsControlService.deleteDb(record);

        // Assert
        assertFalse(result);
        verify(tPmsGlamsControlMapper).deleteByPrimaryKey(control.getId());
        verify(pmsGlamsDefinitionService).detail(control.getId());
        verify(tPmsGlamsControlSelfMapper).deleteByDefine(
                definitionDTO.getOrganizationNumber(),
                definitionDTO.getBranchid(),
                definitionDTO.getTableId());
    }

    /**
     * Create mock TPmsGlamsDefinitionDTO object
     */
    private TPmsGlamsDefinitionDTO createMockTPmsGlamsDefinitionDTO(String id, String organizationNumber, String branchid, String tableId) {
        TPmsGlamsDefinitionDTO dto = new TPmsGlamsDefinitionDTO();
        dto.setId(id);
        dto.setOrganizationNumber(organizationNumber);
        dto.setBranchid(branchid);
        dto.setTableId(tableId);
        dto.setDescription("Test Description");
        dto.setCreateTime(LocalDateTime.now());
        dto.setUpdateTime(LocalDateTime.now());
        dto.setUpdateBy("testUser");
        dto.setVersionNumber(1L);
        return dto;
    }

    @org.junit.jupiter.api.AfterEach
    void tearDown() {
        if (orgNumberUtilsMockedStatic != null) {
            orgNumberUtilsMockedStatic.close();
        }
        if (tenantUtilsMockedStatic != null) {
            tenantUtilsMockedStatic.close();
        }
        if (pageHelperMockedStatic != null) {
            pageHelperMockedStatic.close();
        }
        if (beanCopierMockedStatic != null) {
            beanCopierMockedStatic.close();
        }
        if (objectUtilsMockedStatic != null) {
            objectUtilsMockedStatic.close();
        }
        if (jsonMockedStatic != null) {
            jsonMockedStatic.close();
        }
    }
}
