package com.anytech.anytxn.parameter.card.service;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.core.enums.MessageLanguageEnum;
import com.anytech.anytxn.common.core.utils.MessageSourceUtils;
import com.anytech.anytxn.parameter.base.card.domain.dto.BinCardNumberUsedDTO;
import com.anytech.anytxn.parameter.base.card.domain.model.BinCardNumberUsed;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.card.mapper.unicast.BinCardNumberUsedMapper;
import com.anytech.anytxn.parameter.card.mapper.unicast.BinCardNumberUsedSelfMapper;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.util.StringUtils;
import java.util.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;

@ExtendWith(MockitoExtension.class)
class BinCardNumberUsedServiceTest {
    @InjectMocks
    private BinCardNumberUsedServiceImpl service;
    @Mock
    private BinCardNumberUsedSelfMapper binCardNumberUsedSelfMapper;
    @Mock
    private BinCardNumberUsedMapper binCardNumberUsedMapper;
    @Mock
    private Number16IdGen numberIdGenerator;
    private MockedStatic<BeanMapping> beanMappingMockedStatic;
    private MockedStatic<OrgNumberUtils> orgNumberUtilsMockedStatic;
    private MockedStatic<TenantUtils> tenantUtilsMockedStatic;
    private MockedStatic<PageHelper> pageHelperMockedStatic;
    private MockedStatic<StringUtils> stringUtilsMockedStatic;
    private MockedStatic<MessageSourceUtils> messageSourceUtilsMockedStatic;

    @BeforeEach
    void setUp() {
        beanMappingMockedStatic = Mockito.mockStatic(BeanMapping.class);
        orgNumberUtilsMockedStatic = Mockito.mockStatic(OrgNumberUtils.class);
        tenantUtilsMockedStatic = Mockito.mockStatic(TenantUtils.class);
        pageHelperMockedStatic = Mockito.mockStatic(PageHelper.class);
        stringUtilsMockedStatic = Mockito.mockStatic(StringUtils.class);
        messageSourceUtilsMockedStatic = Mockito.mockStatic(MessageSourceUtils.class);
        messageSourceUtilsMockedStatic.when(() -> MessageSourceUtils.requestLanguage()).thenReturn(MessageLanguageEnum.CN);
    }

    @AfterEach
    void tearDown() {
        beanMappingMockedStatic.close();
        orgNumberUtilsMockedStatic.close();
        tenantUtilsMockedStatic.close();
        pageHelperMockedStatic.close();
        stringUtilsMockedStatic.close();
        messageSourceUtilsMockedStatic.close();
    }

    // 1. findAll - happy path
    @Test
    void testFindAll_HappyPath() {
        BinCardNumberUsedDTO dto = new BinCardNumberUsedDTO();
        dto.setOrganizationNumber("org");
        stringUtilsMockedStatic.when(() -> StringUtils.isEmpty("org")).thenReturn(false);
        pageHelperMockedStatic.when(() -> PageHelper.startPage(anyInt(), anyInt())).thenReturn(Mockito.mock(Page.class));
        List<BinCardNumberUsed> list = Collections.singletonList(new BinCardNumberUsed());
        Mockito.lenient().when(binCardNumberUsedSelfMapper.selectByCondition(any())).thenReturn(list);
        List<BinCardNumberUsedDTO> resList = Collections.singletonList(new BinCardNumberUsedDTO());
        beanMappingMockedStatic.when(() -> BeanMapping.copyList(any(), any())).thenReturn(resList);
        PageResultDTO<BinCardNumberUsedDTO> result = service.findAll(1, 10, dto);
        assertNotNull(result);
        assertEquals(resList, result.getData());
    }

    // 2. findAll - binCardNumberUsedDTO为null
    @Test
    void testFindAll_NullDTO() {
        pageHelperMockedStatic.when(() -> PageHelper.startPage(anyInt(), anyInt())).thenReturn(Mockito.mock(Page.class));
        Mockito.lenient().when(binCardNumberUsedSelfMapper.selectByCondition(any())).thenReturn(Collections.emptyList());
        beanMappingMockedStatic.when(() -> BeanMapping.copyList(any(), any())).thenReturn(Collections.emptyList());
        PageResultDTO<BinCardNumberUsedDTO> result = service.findAll(1, 10, null);
        assertNotNull(result);
    }

    // 3. findAll - organizationNumber为空
    @Test
    void testFindAll_OrgNumberNull() {
        BinCardNumberUsedDTO dto = new BinCardNumberUsedDTO();
        stringUtilsMockedStatic.when(() -> StringUtils.isEmpty(null)).thenReturn(true);
        orgNumberUtilsMockedStatic.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
        pageHelperMockedStatic.when(() -> PageHelper.startPage(anyInt(), anyInt())).thenReturn(Mockito.mock(Page.class));
        Mockito.lenient().when(binCardNumberUsedSelfMapper.selectByCondition(any())).thenReturn(Collections.emptyList());
        beanMappingMockedStatic.when(() -> BeanMapping.copyList(any(), any())).thenReturn(Collections.emptyList());
        PageResultDTO<BinCardNumberUsedDTO> result = service.findAll(1, 10, dto);
        assertNotNull(result);
    }

    // 4. findById - happy path
    @Test
    void testFindById_HappyPath() {
        BinCardNumberUsed entity = new BinCardNumberUsed();
        Mockito.lenient().when(binCardNumberUsedMapper.selectByPrimaryKey(any())).thenReturn(entity);
        BinCardNumberUsedDTO dto = new BinCardNumberUsedDTO();
        beanMappingMockedStatic.when(() -> BeanMapping.copy(entity, BinCardNumberUsedDTO.class)).thenReturn(dto);
        BinCardNumberUsedDTO result = service.findById("id");
        assertNotNull(result);
    }

    // 5. findById - id为null
    @Test
    void testFindById_IdNull() {
        assertThrows(AnyTxnParameterException.class, () -> service.findById(null));
    }

    // 6. findById - 查不到
    @Test
    void testFindById_NotFound() {
        Mockito.lenient().when(binCardNumberUsedMapper.selectByPrimaryKey(any())).thenReturn(null);
        assertThrows(AnyTxnParameterException.class, () -> service.findById("id"));
    }

    // 7. add - happy path
    @Test
    void testAdd_HappyPath() {
        BinCardNumberUsedDTO dto = new BinCardNumberUsedDTO();
        dto.setTableId("tid");
        dto.setBinSequence("seq");
        dto.setFinishedIndicator("1");
        dto.setStatus("1");
        Mockito.lenient().when(binCardNumberUsedSelfMapper.selectLastUsedNumberByIndex(any(), any(), any())).thenReturn(null);
        beanMappingMockedStatic.when(() -> BeanMapping.copy(any(), eq(BinCardNumberUsed.class))).thenReturn(new BinCardNumberUsed());
        tenantUtilsMockedStatic.when(TenantUtils::getTenantId).thenReturn("tenant");
        Mockito.lenient().when(numberIdGenerator.generateId(any())).thenReturn(123L);
        ParameterCompare compare = ParameterCompare.getBuilder().withAfter(new BinCardNumberUsed()).build(BinCardNumberUsed.class);
        ParameterCompare result = service.add(dto);
        assertNotNull(result);
    }

    // 8. add - 已存在
    @Test
    void testAdd_Exist() {
        BinCardNumberUsedDTO dto = new BinCardNumberUsedDTO();
        dto.setTableId("tid");
        dto.setBinSequence("seq");
        dto.setFinishedIndicator("1");
        dto.setStatus("1");
        Mockito.lenient().when(binCardNumberUsedSelfMapper.selectLastUsedNumberByIndex(any(), any(), any())).thenReturn(new BinCardNumberUsed());
        assertThrows(AnyTxnParameterException.class, () -> service.add(dto));
    }

    // 9. add - checkRequired各字段为空
    @Test
    void testAdd_RequiredFieldNull() {
        BinCardNumberUsedDTO dto = new BinCardNumberUsedDTO();
        // tableId为空
        dto.setBinSequence("seq");
        dto.setFinishedIndicator("1");
        dto.setStatus("1");
        assertThrows(AnyTxnParameterException.class, () -> service.add(dto));
        // binSequence为空
        dto.setTableId("tid");
        dto.setBinSequence(null);
        assertThrows(AnyTxnParameterException.class, () -> service.add(dto));
        // finishedIndicator为空
        dto.setBinSequence("seq");
        dto.setFinishedIndicator(null);
        assertThrows(AnyTxnParameterException.class, () -> service.add(dto));
        // status为空
        dto.setFinishedIndicator("1");
        dto.setStatus(null);
        assertThrows(AnyTxnParameterException.class, () -> service.add(dto));
    }

    // 10. update - happy path
    @Test
    void testUpdate_HappyPath() {
        BinCardNumberUsedDTO dto = new BinCardNumberUsedDTO();
        dto.setId("id");
        BinCardNumberUsed entity = new BinCardNumberUsed();
        Mockito.lenient().when(binCardNumberUsedMapper.selectByPrimaryKey(any())).thenReturn(entity);
        beanMappingMockedStatic.when(() -> BeanMapping.copy(any(), eq(BinCardNumberUsed.class))).thenReturn(new BinCardNumberUsed());
        ParameterCompare compare = ParameterCompare.getBuilder().withAfter(new BinCardNumberUsed()).withBefore(entity).build(BinCardNumberUsed.class);
        ParameterCompare result = service.update(dto);
        assertNotNull(result);
    }

    // 11. update - binCardNumberUsedDTO为null
    @Test
    void testUpdate_NullDTO() {
        assertThrows(AnyTxnParameterException.class, () -> service.update(null));
    }

    // 12. update - 查不到
    @Test
    void testUpdate_NotFound() {
        BinCardNumberUsedDTO dto = new BinCardNumberUsedDTO();
        dto.setId("id");
        Mockito.lenient().when(binCardNumberUsedMapper.selectByPrimaryKey(any())).thenReturn(null);
        assertThrows(AnyTxnParameterException.class, () -> service.update(dto));
    }

    // 13. delete - happy path
    @Test
    void testDelete_HappyPath() {
        BinCardNumberUsed entity = new BinCardNumberUsed();
        Mockito.lenient().when(binCardNumberUsedMapper.selectByPrimaryKey(any())).thenReturn(entity);
        ParameterCompare compare = ParameterCompare.getBuilder().withBefore(entity).build(BinCardNumberUsed.class);
        ParameterCompare result = service.delete("id");
        assertNotNull(result);
    }

    // 14. delete - id为null
    @Test
    void testDelete_IdNull() {
        assertThrows(AnyTxnParameterException.class, () -> service.delete(null));
    }

    // 15. delete - 查不到
    @Test
    void testDelete_NotFound() {
        Mockito.lenient().when(binCardNumberUsedMapper.selectByPrimaryKey(any())).thenReturn(null);
        assertThrows(AnyTxnParameterException.class, () -> service.delete("id"));
    }
} 