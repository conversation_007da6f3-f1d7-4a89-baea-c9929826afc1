package com.anytech.anytxn.parameter.card.service;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.core.enums.MessageLanguageEnum;
import com.anytech.anytxn.common.core.utils.MessageSourceUtils;
import com.anytech.anytxn.parameter.base.card.domain.dto.BonusCashBackTableSearchDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.ParmBonusCashbackCharReqDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.ParmBonusCashbackCharResDTO;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.base.transaction.domain.model.ParmBonusCashbackCharge;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmBonusCashbackChargeMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmBonusCashbackChargeSelfMapper;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;

@ExtendWith(MockitoExtension.class)
class BonusCashBackTableServiceTest {
    @InjectMocks
    private BonusCashBackTableServiceImpl service;
    @Mock
    private ParmBonusCashbackChargeSelfMapper cashbackChargeSelfMapper;
    @Mock
    private ParmBonusCashbackChargeMapper cashbackChargeMapper;
    @Mock
    private Number16IdGen numberIdGenerator;
    private MockedStatic<BeanMapping> beanMappingMockedStatic;
    private MockedStatic<OrgNumberUtils> orgNumberUtilsMockedStatic;
    private MockedStatic<TenantUtils> tenantUtilsMockedStatic;
    private MockedStatic<PageHelper> pageHelperMockedStatic;
    private MockedStatic<MessageSourceUtils> messageSourceUtilsMockedStatic;

    @BeforeEach
    void setUp() {
        beanMappingMockedStatic = Mockito.mockStatic(BeanMapping.class);
        orgNumberUtilsMockedStatic = Mockito.mockStatic(OrgNumberUtils.class);
        tenantUtilsMockedStatic = Mockito.mockStatic(TenantUtils.class);
        pageHelperMockedStatic = Mockito.mockStatic(PageHelper.class);
        messageSourceUtilsMockedStatic = Mockito.mockStatic(MessageSourceUtils.class);
        messageSourceUtilsMockedStatic.when(() -> MessageSourceUtils.requestLanguage()).thenReturn(MessageLanguageEnum.CN);
    }

    @AfterEach
    void tearDown() {
        beanMappingMockedStatic.close();
        orgNumberUtilsMockedStatic.close();
        tenantUtilsMockedStatic.close();
        pageHelperMockedStatic.close();
        messageSourceUtilsMockedStatic.close();
    }

    // 1. findAll - happy path
    @Test
    void testFindAll_HappyPath() {
        BonusCashBackTableSearchDTO dto = new BonusCashBackTableSearchDTO();
        Page<ParmBonusCashbackCharge> page = Mockito.mock(Page.class);
        pageHelperMockedStatic.when(() -> PageHelper.startPage(anyInt(), anyInt())).thenReturn(page);
        List<ParmBonusCashbackCharge> list = Collections.singletonList(new ParmBonusCashbackCharge());
        Mockito.lenient().when(cashbackChargeSelfMapper.selectByCondition(any())).thenReturn(list);
        // mock具体参数，必须和测试用例中传入的list对象完全一致
        List<ParmBonusCashbackCharResDTO> resList = Collections.singletonList(new ParmBonusCashbackCharResDTO());
        List<ParmBonusCashbackCharge> entityList = new ArrayList<>();
        beanMappingMockedStatic.when(() -> BeanMapping.copyList(entityList, ParmBonusCashbackCharResDTO.class)).thenReturn(resList);
        beanMappingMockedStatic.when(() -> BeanMapping.copyList(new ArrayList<>(), ParmBonusCashbackCharResDTO.class)).thenReturn(Collections.emptyList());
        PageResultDTO<ParmBonusCashbackCharResDTO> result = service.findAll(1, 10, dto);
        assertNotNull(result);
        assertEquals(resList, result.getData());
    }

    // 2. findAll - cashBackTableSearchDTO为null
    @Test
    void testFindAll_NullDTO() {
        pageHelperMockedStatic.when(() -> PageHelper.startPage(anyInt(), anyInt())).thenReturn(Mockito.mock(Page.class));
        Mockito.lenient().when(cashbackChargeSelfMapper.selectByCondition(any())).thenReturn(Collections.emptyList());
        beanMappingMockedStatic.when(() -> BeanMapping.copyList(anyList(), ParmBonusCashbackCharResDTO.class)).thenAnswer(invocation -> Collections.emptyList());
        PageResultDTO<ParmBonusCashbackCharResDTO> result = service.findAll(1, 10, null);
        assertNotNull(result);
    }

    // 3. addParm - happy path
    @Test
    void testAddParm_HappyPath() {
        ParmBonusCashbackCharReqDTO req = new ParmBonusCashbackCharReqDTO();
        req.setOrganizationNumber("org");
        req.setTableId("tid");
        Mockito.lenient().when(cashbackChargeSelfMapper.selectByOrgAndTid(any(), any())).thenReturn(null);
        beanMappingMockedStatic.when(() -> BeanMapping.copy(any(), eq(ParmBonusCashbackCharge.class))).thenReturn(new ParmBonusCashbackCharge());
        tenantUtilsMockedStatic.when(TenantUtils::getTenantId).thenReturn("tenant");
        Mockito.lenient().when(numberIdGenerator.generateId(any())).thenReturn(123L);
        ParameterCompare compare = ParameterCompare.getBuilder().withAfter(new ParmBonusCashbackCharge()).build(ParmBonusCashbackCharge.class);
        ParameterCompare result = service.addParm(req);
        assertNotNull(result);
    }

    // 4. addParm - 已存在
    @Test
    void testAddParm_Exist() {
        ParmBonusCashbackCharReqDTO req = new ParmBonusCashbackCharReqDTO();
        req.setOrganizationNumber("org");
        req.setTableId("tid");
        Mockito.lenient().when(cashbackChargeSelfMapper.selectByOrgAndTid(any(), any())).thenReturn(new ParmBonusCashbackCharge());
        assertThrows(AnyTxnParameterException.class, () -> service.addParm(req));
    }

    // 5. modifyParm - happy path
    @Test
    void testModifyParm_HappyPath() {
        ParmBonusCashbackCharReqDTO req = new ParmBonusCashbackCharReqDTO();
        req.setId("id");
        req.setOrganizationNumber("org");
        req.setTableId("tid");
        ParmBonusCashbackCharge oldEntity = new ParmBonusCashbackCharge();
        oldEntity.setOrganizationNumber("org");
        oldEntity.setTableId("tid");
        Mockito.lenient().when(cashbackChargeMapper.selectByPrimaryKey(any())).thenReturn(oldEntity);
        beanMappingMockedStatic.when(() -> BeanMapping.copy(any(), eq(ParmBonusCashbackCharge.class))).thenReturn(new ParmBonusCashbackCharge());
        ParameterCompare compare = ParameterCompare.getBuilder().withAfter(new ParmBonusCashbackCharge()).withBefore(oldEntity).build(ParmBonusCashbackCharge.class);
        ParameterCompare result = service.modifyParm(req);
        assertNotNull(result);
    }

    // 6. modifyParm - id查不到
    @Test
    void testModifyParm_NotFound() {
        ParmBonusCashbackCharReqDTO req = new ParmBonusCashbackCharReqDTO();
        req.setId("id");
        Mockito.lenient().when(cashbackChargeMapper.selectByPrimaryKey(any())).thenReturn(null);
        assertThrows(AnyTxnParameterException.class, () -> service.modifyParm(req));
    }

    // 7. modifyParm - 唯一性冲突
    @Test
    void testModifyParm_UniqueConflict() {
        ParmBonusCashbackCharReqDTO req = new ParmBonusCashbackCharReqDTO();
        req.setId("id");
        req.setOrganizationNumber("org2");
        req.setTableId("tid2");
        ParmBonusCashbackCharge oldEntity = new ParmBonusCashbackCharge();
        oldEntity.setOrganizationNumber("org1");
        oldEntity.setTableId("tid1");
        Mockito.lenient().when(cashbackChargeMapper.selectByPrimaryKey(any())).thenReturn(oldEntity);
        Mockito.lenient().when(cashbackChargeSelfMapper.selectByOrgAndTid(any(), any())).thenReturn(new ParmBonusCashbackCharge());
        assertThrows(AnyTxnParameterException.class, () -> service.modifyParm(req));
    }

    // 8. removeParm - happy path
    @Test
    void testRemoveParm_HappyPath() {
        ParmBonusCashbackCharge entity = new ParmBonusCashbackCharge();
        Mockito.lenient().when(cashbackChargeMapper.selectByPrimaryKey(any())).thenReturn(entity);
        ParameterCompare compare = ParameterCompare.getBuilder().withBefore(entity).build(ParmBonusCashbackCharge.class);
        ParameterCompare result = service.removeParm("id");
        assertNotNull(result);
    }

    // 9. removeParm - id查不到
    @Test
    void testRemoveParm_NotFound() {
        Mockito.lenient().when(cashbackChargeMapper.selectByPrimaryKey(any())).thenReturn(null);
        assertThrows(AnyTxnParameterException.class, () -> service.removeParm("id"));
    }

    // 10. findById - happy path
    @Test
    void testFindById_HappyPath() {
        ParmBonusCashbackCharge entity = new ParmBonusCashbackCharge();
        Mockito.lenient().when(cashbackChargeMapper.selectByPrimaryKey(any())).thenReturn(entity);
        ParmBonusCashbackCharResDTO dto = new ParmBonusCashbackCharResDTO();
        beanMappingMockedStatic.when(() -> BeanMapping.copy(entity, ParmBonusCashbackCharResDTO.class)).thenReturn(dto);
        ParmBonusCashbackCharResDTO result = service.findById("id");
        assertNotNull(result);
    }

    // 11. findById - id为空
    @Test
    void testFindById_IdNull() {
        assertThrows(AnyTxnParameterException.class, () -> service.findById(null));
    }

    // 12. findByOrgAndTableId - happy path
    @Test
    void testFindByOrgAndTableId_HappyPath() {
        ParmBonusCashbackCharge entity = new ParmBonusCashbackCharge();
        Mockito.lenient().when(cashbackChargeSelfMapper.selectByOrgAndTid(any(), any())).thenReturn(entity);
        ParmBonusCashbackCharResDTO dto = new ParmBonusCashbackCharResDTO();
        beanMappingMockedStatic.when(() -> BeanMapping.copy(entity, ParmBonusCashbackCharResDTO.class)).thenReturn(dto);
        ParmBonusCashbackCharResDTO result = service.findByOrgAndTableId("org", "tid");
        assertNotNull(result);
    }

    // 13. findByOrgAndTableId - 查不到
    @Test
    void testFindByOrgAndTableId_NotFound() {
        Mockito.lenient().when(cashbackChargeSelfMapper.selectByOrgAndTid(any(), any())).thenReturn(null);
        assertThrows(AnyTxnParameterException.class, () -> service.findByOrgAndTableId("org", "tid"));
    }
} 