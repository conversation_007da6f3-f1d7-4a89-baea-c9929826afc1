package com.anytech.anytxn.parameter.service;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.parameter.base.card.domain.dto.*;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardFeeGroupInfo;
import com.anytech.anytxn.parameter.base.card.service.IParmCardFeeDefiniTionService;
import com.anytech.anytxn.parameter.base.card.service.IParmCardFeeGroupService;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardFeeGroupInfoSelfMapper;
import com.anytech.anytxn.parameter.card.service.ParmCardFeeGroupInfoServiceImpl;
import com.anytech.anytxn.parameter.service.utils.SimpleMockUtils;
import com.anytech.anytxn.parameter.service.utils.SimpleTestUtils;
import com.github.pagehelper.Page;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ParmCardFeeGroupService 单元测试类
 * 测试卡片费用组参数管理服务的各项功能
 */
@ExtendWith(MockitoExtension.class)
class ParmCardFeeGroupServiceTest {

    @Mock
    private ParmCardFeeGroupInfoSelfMapper parmCardFeeGroupInfoSelfMapper;

    @Mock
    private IParmCardFeeDefiniTionService parmCardFeeDefiniTionService;

    @Mock
    private com.anytech.anytxn.common.sequence.utils.Number16IdGen numberIdGenerator;

    @InjectMocks
    private ParmCardFeeGroupInfoServiceImpl parmCardFeeGroupService;

    private CardFeeGroupInfoReqDTO mockCardFeeGroupInfoReqDTO;
    private List<ParmCardFeeGroupInfo> mockCardFeeGroupInfoList;
    private List<CardFeeGroupDetailDTO> mockCardFeeGroupDetailList;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        mockCardFeeGroupInfoReqDTO = createMockCardFeeGroupInfoReqDTO();
        mockCardFeeGroupInfoList = createMockCardFeeGroupInfoList();
        mockCardFeeGroupDetailList = createMockCardFeeGroupDetailList();
    }

    /**
     * 测试分页查询卡片费用组参数 - 正常情况
     */
    @Test
    void testFindByPage_Success() {
        SimpleMockUtils.executeWithStaticMocks(() -> {
            // Arrange
            Page<ParmCardFeeGroupInfo> mockPage = new Page<>(1, 10);
            mockPage.setTotal(5);
            mockPage.setPages(1);
            
            when(parmCardFeeGroupInfoSelfMapper.selectByCondition(anyString(), anyString(), anyString()))
                    .thenReturn(mockCardFeeGroupInfoList);

            // Act
            PageResultDTO<CardFeeGroupInfoResDTO> result = parmCardFeeGroupService
                    .findByPage(1, 10, "TEST001", "测试描述", "001");

            // Assert
            assertNotNull(result, "分页查询结果不应为null");
            assertTrue(result.getTotal() >= 0, "总数应该大于等于0");
            assertNotNull(result.getData(), "数据列表不应为null");
            
            verify(parmCardFeeGroupInfoSelfMapper, times(1))
                    .selectByCondition(eq("TEST001"), eq("测试描述"), eq("001"));
            
            return true;
        });
    }

    /**
     * 测试分页查询 - 空结果情况
     */
    @Test
    void testFindByPage_EmptyResult() {
        SimpleMockUtils.executeWithStaticMocks(() -> {
            // Arrange
            when(parmCardFeeGroupInfoSelfMapper.selectByCondition(anyString(), anyString(), anyString()))
                    .thenReturn(new ArrayList<>());

            // Act
            PageResultDTO<CardFeeGroupInfoResDTO> result = parmCardFeeGroupService
                    .findByPage(1, 10, "NOTFOUND", null, "001");

            // Assert
            assertNull(result, "空结果时应返回null");
            
            verify(parmCardFeeGroupInfoSelfMapper, times(1))
                    .selectByCondition(eq("NOTFOUND"), isNull(), eq("001"));
            
            return true;
        });
    }

    /**
     * 测试添加卡片费用组参数 - 成功情况
     */
    @Test
    void testAddCardFeeGroup_Success() {
        SimpleMockUtils.executeWithStaticMocks(() -> {
            // Arrange
            when(parmCardFeeGroupInfoSelfMapper.isExistByOrgAndTid(anyString(), anyString()))
                    .thenReturn(0);

            // Act
            ParameterCompare result = parmCardFeeGroupService.addCardFeeGroup(mockCardFeeGroupInfoReqDTO);

            // Assert
            assertNotNull(result, "添加结果不应为null");
            assertEquals(mockCardFeeGroupInfoReqDTO.getTableId(), result.getMainParmId(), 
                    "主参数ID应该正确");
            
            verify(parmCardFeeGroupInfoSelfMapper, times(1))
                    .isExistByOrgAndTid(eq("001"), eq("TEST001"));
            
            return true;
        });
    }

    /**
     * 测试添加卡片费用组参数 - 数据已存在异常
     */
    @Test
    void testAddCardFeeGroup_DataExists() {
        SimpleMockUtils.executeWithStaticMocks(() -> {
            // Arrange
            when(parmCardFeeGroupInfoSelfMapper.isExistByOrgAndTid(anyString(), anyString()))
                    .thenReturn(1);

            // Act & Assert
            assertThrows(AnyTxnParameterException.class, () -> {
                parmCardFeeGroupService.addCardFeeGroup(mockCardFeeGroupInfoReqDTO);
            }, "数据已存在时应抛出异常");
            
            verify(parmCardFeeGroupInfoSelfMapper, times(1))
                    .isExistByOrgAndTid(eq("001"), eq("TEST001"));
            
            return true;
        });
    }

    /**
     * 测试添加卡片费用组参数 - 重复费用子类型异常
     */
    @Test
    void testAddCardFeeGroup_DuplicateSubFeeType() {
        SimpleMockUtils.executeWithStaticMocks(() -> {
            // Arrange
            CardFeeGroupInfoReqDTO duplicateReqDTO = createDuplicateSubFeeTypeReqDTO();
            when(parmCardFeeGroupInfoSelfMapper.isExistByOrgAndTid(anyString(), anyString()))
                    .thenReturn(0);

            // Act & Assert
            assertThrows(AnyTxnParameterException.class, () -> {
                parmCardFeeGroupService.addCardFeeGroup(duplicateReqDTO);
            }, "重复费用子类型时应抛出异常");
            
            return true;
        });
    }

    /**
     * 测试更新卡片费用组参数 - 成功情况
     */
    @Test
    void testModifyCardFeeGroup_Success() {
        SimpleMockUtils.executeWithStaticMocks(() -> {
            // Arrange
            when(parmCardFeeGroupInfoSelfMapper.selectByOrgAndTid(anyString(), anyString()))
                    .thenReturn(mockCardFeeGroupInfoList);
            when(parmCardFeeGroupInfoSelfMapper.deleteByOrgAndTid(anyString(), anyString()))
                    .thenReturn(1);

            // Act
            ParameterCompare result = parmCardFeeGroupService.modifyCardFeeGroup(mockCardFeeGroupInfoReqDTO);

            // Assert
            assertNotNull(result, "更新结果不应为null");
            assertEquals(mockCardFeeGroupInfoReqDTO.getTableId(), result.getMainParmId(), 
                    "主参数ID应该正确");
            
            verify(parmCardFeeGroupInfoSelfMapper, times(1))
                    .selectByOrgAndTid(eq("001"), eq("TEST001"));
            verify(parmCardFeeGroupInfoSelfMapper, times(1))
                    .deleteByOrgAndTid(eq("001"), eq("TEST001"));
            
            return true;
        });
    }

    /**
     * 测试删除卡片费用组参数 - 成功情况
     */
    @Test
    void testRemoveCardFeeGroup_Success() {
        SimpleMockUtils.executeWithStaticMocks(() -> {
            // Arrange
            when(parmCardFeeGroupInfoSelfMapper.isExistByOrgAndTid(anyString(), anyString()))
                    .thenReturn(1);
            when(parmCardFeeGroupInfoSelfMapper.selectByOrgAndTid(anyString(), anyString()))
                    .thenReturn(mockCardFeeGroupInfoList);

            // Act
            ParameterCompare result = parmCardFeeGroupService.removeCardFeeGroup("001", "TEST001");

            // Assert
            assertNotNull(result, "删除结果不应为null");
            
            verify(parmCardFeeGroupInfoSelfMapper, times(1))
                    .isExistByOrgAndTid(eq("001"), eq("TEST001"));
            verify(parmCardFeeGroupInfoSelfMapper, times(1))
                    .selectByOrgAndTid(eq("001"), eq("TEST001"));
            
            return true;
        });
    }

    /**
     * 测试删除卡片费用组参数 - 参数为空异常
     */
    @ParameterizedTest
    @ValueSource(strings = {"", " "})
    void testRemoveCardFeeGroup_EmptyParameters(String emptyValue) {
        SimpleMockUtils.executeWithStaticMocks(() -> {
            // Act & Assert
            assertThrows(AnyTxnParameterException.class, () -> {
                parmCardFeeGroupService.removeCardFeeGroup(emptyValue, "TEST001");
            }, "机构号为空时应抛出异常");

            assertThrows(AnyTxnParameterException.class, () -> {
                parmCardFeeGroupService.removeCardFeeGroup("001", emptyValue);
            }, "表ID为空时应抛出异常");
            
            return true;
        });
    }

    /**
     * 测试删除卡片费用组参数 - 数据不存在异常
     */
    @Test
    void testRemoveCardFeeGroup_DataNotExists() {
        SimpleMockUtils.executeWithStaticMocks(() -> {
            // Arrange
            when(parmCardFeeGroupInfoSelfMapper.isExistByOrgAndTid(anyString(), anyString()))
                    .thenReturn(0);

            // Act & Assert
            assertThrows(AnyTxnParameterException.class, () -> {
                parmCardFeeGroupService.removeCardFeeGroup("001", "NOTFOUND");
            }, "数据不存在时应抛出异常");
            
            verify(parmCardFeeGroupInfoSelfMapper, times(1))
                    .isExistByOrgAndTid(eq("001"), eq("NOTFOUND"));
            
            return true;
        });
    }

    /**
     * 测试根据机构号和表ID查询 - 成功情况
     */
    @Test
    void testFindByOrgAndTableId_Success() {
        SimpleMockUtils.executeWithStaticMocks(() -> {
            // Arrange
            when(parmCardFeeGroupInfoSelfMapper.selectByOrgAndTid(anyString(), anyString()))
                    .thenReturn(mockCardFeeGroupInfoList);
            
            CardFeeDefinitionResDTO mockFeeDefinition = createMockCardFeeDefinitionResDTO();
            when(parmCardFeeDefiniTionService.findByIndex(anyString(), anyString(), anyString(), anyString()))
                    .thenReturn(mockFeeDefinition);

            // Act
            CardFeeGroupInfoResDTO result = parmCardFeeGroupService.findByOrgAndTableId("001", "TEST001");

            // Assert
            assertNotNull(result, "查询结果不应为null");
            assertEquals("001", result.getOrganizationNumber(), "机构号应该正确");
            assertEquals("TEST001", result.getTableId(), "表ID应该正确");
            assertNotNull(result.getCardFeeGroupDetailList(), "详情列表不应为null");
            assertFalse(result.getCardFeeGroupDetailList().isEmpty(), "详情列表不应为空");
            
            verify(parmCardFeeGroupInfoSelfMapper, times(1))
                    .selectByOrgAndTid(eq("001"), eq("TEST001"));
            
            return true;
        });
    }

    /**
     * 测试根据机构号和表ID查询 - 数据不存在异常
     */
    @Test
    void testFindByOrgAndTableId_DataNotExists() {
        SimpleMockUtils.executeWithStaticMocks(() -> {
            // Arrange
            when(parmCardFeeGroupInfoSelfMapper.selectByOrgAndTid(anyString(), anyString()))
                    .thenReturn(new ArrayList<>());

            // Act & Assert
            assertThrows(AnyTxnParameterException.class, () -> {
                parmCardFeeGroupService.findByOrgAndTableId("001", "NOTFOUND");
            }, "数据不存在时应抛出异常");
            
            verify(parmCardFeeGroupInfoSelfMapper, times(1))
                    .selectByOrgAndTid(eq("001"), eq("NOTFOUND"));
            
            return true;
        });
    }

    /**
     * 测试根据费用代码查询 - 成功情况
     */
    @Test
    void testFindByOrgTidAndFeeCode_Success() {
        SimpleMockUtils.executeWithStaticMocks(() -> {
            // Arrange
            when(parmCardFeeGroupInfoSelfMapper.selectByOrgTidAndFeeCode(anyString(), anyString(), anyString()))
                    .thenReturn(mockCardFeeGroupInfoList);

            // Act
            List<CardFeeGroupInfoDTO> result = parmCardFeeGroupService
                    .findByOrgTidAndFeeCode("001", "TEST001", "FEE001");

            // Assert
            assertNotNull(result, "查询结果不应为null");
            assertFalse(result.isEmpty(), "结果列表不应为空");
            
            verify(parmCardFeeGroupInfoSelfMapper, times(1))
                    .selectByOrgTidAndFeeCode(eq("001"), eq("TEST001"), eq("FEE001"));
            
            return true;
        });
    }

    /**
     * 测试根据费用代码查询 - 空结果
     */
    @Test
    void testFindByOrgTidAndFeeCode_EmptyResult() {
        SimpleMockUtils.executeWithStaticMocks(() -> {
            // Arrange
            when(parmCardFeeGroupInfoSelfMapper.selectByOrgTidAndFeeCode(anyString(), anyString(), anyString()))
                    .thenReturn(new ArrayList<>());

            // Act
            List<CardFeeGroupInfoDTO> result = parmCardFeeGroupService
                    .findByOrgTidAndFeeCode("001", "TEST001", "NOTFOUND");

            // Assert
            assertNotNull(result, "查询结果不应为null");
            assertTrue(result.isEmpty(), "结果列表应为空");
            
            return true;
        });
    }

    /**
     * 测试根据费用子类型查询 - 成功情况
     */
    @Test
    void testFindByOrgTidAndSubFeeType_Success() {
        SimpleMockUtils.executeWithStaticMocks(() -> {
            // Arrange
            when(parmCardFeeGroupInfoSelfMapper.selectByOrgTidAndSubFeeType(anyString(), anyString(), anyString()))
                    .thenReturn(mockCardFeeGroupInfoList);

            // Act
            List<CardFeeGroupInfoDTO> result = parmCardFeeGroupService
                    .findByOrgTidAndSubFeeType("001", "TEST001", "SUB001");

            // Assert
            assertNotNull(result, "查询结果不应为null");
            assertFalse(result.isEmpty(), "结果列表不应为空");
            
            verify(parmCardFeeGroupInfoSelfMapper, times(1))
                    .selectByOrgTidAndSubFeeType(eq("001"), eq("TEST001"), eq("SUB001"));
            
            return true;
        });
    }

    /**
     * 测试根据费用子类型查询 - 空结果
     */
    @Test
    void testFindByOrgTidAndSubFeeType_EmptyResult() {
        SimpleMockUtils.executeWithStaticMocks(() -> {
            // Arrange
            when(parmCardFeeGroupInfoSelfMapper.selectByOrgTidAndSubFeeType(anyString(), anyString(), anyString()))
                    .thenReturn(new ArrayList<>());

            // Act
            List<CardFeeGroupInfoDTO> result = parmCardFeeGroupService
                    .findByOrgTidAndSubFeeType("001", "TEST001", "NOTFOUND");

            // Assert
            assertNotNull(result, "查询结果不应为null");
            assertTrue(result.isEmpty(), "结果列表应为空");
            
            return true;
        });
    }

    /**
     * 测试并发操作场景
     */
    @Test
    void testConcurrentOperations() {
        SimpleMockUtils.executeWithStaticMocks(() -> {
            // Arrange
            when(parmCardFeeGroupInfoSelfMapper.isExistByOrgAndTid(anyString(), anyString()))
                    .thenReturn(0);
            when(parmCardFeeGroupInfoSelfMapper.selectByOrgAndTid(anyString(), anyString()))
                    .thenReturn(mockCardFeeGroupInfoList);

            // Act - 模拟并发添加操作
            for (int i = 0; i < 10; i++) {
                CardFeeGroupInfoReqDTO concurrent = createMockCardFeeGroupInfoReqDTO();
                concurrent.setTableId("CONCURRENT" + i);
                
                ParameterCompare result = parmCardFeeGroupService.addCardFeeGroup(concurrent);
                assertNotNull(result, "并发操作结果不应为null");
            }

            // Assert
            verify(parmCardFeeGroupInfoSelfMapper, times(10))
                    .isExistByOrgAndTid(anyString(), anyString());
            
            return true;
        });
    }

    /**
     * 测试性能边界情况
     */
    @Test
    void testPerformanceBoundary() {
        SimpleMockUtils.executeWithStaticMocks(() -> {
            // Arrange - 大量数据
            List<ParmCardFeeGroupInfo> largeDataSet = new ArrayList<>();
            for (int i = 0; i < 1000; i++) {
                largeDataSet.add(createMockParmCardFeeGroupInfo("LARGE" + i));
            }
            
            when(parmCardFeeGroupInfoSelfMapper.selectByCondition(anyString(), anyString(), anyString()))
                    .thenReturn(largeDataSet);

            // Act
            long startTime = System.currentTimeMillis();
            PageResultDTO<CardFeeGroupInfoResDTO> result = parmCardFeeGroupService
                    .findByPage(1, 100, null, null, "001");
            long endTime = System.currentTimeMillis();

            // Assert
            assertNotNull(result, "大数据量查询结果不应为null");
            assertTrue(endTime - startTime < 5000, "查询时间应在合理范围内");
            
            return true;
        });
    }

    // ==================== 辅助方法 ====================

    /**
     * 创建Mock的CardFeeGroupInfoReqDTO
     */
    private CardFeeGroupInfoReqDTO createMockCardFeeGroupInfoReqDTO() {
        CardFeeGroupInfoReqDTO dto = new CardFeeGroupInfoReqDTO();
        dto.setOrganizationNumber("001");
        dto.setTableId("TEST001");
        dto.setDescription("测试费用组");
        dto.setStatus("1");
        dto.setCardFeeGroupDetailList(createMockCardFeeGroupDetailList());
        return dto;
    }

    /**
     * 创建包含重复费用子类型的请求DTO
     */
    private CardFeeGroupInfoReqDTO createDuplicateSubFeeTypeReqDTO() {
        CardFeeGroupInfoReqDTO dto = createMockCardFeeGroupInfoReqDTO();
        List<CardFeeGroupDetailDTO> duplicateList = new ArrayList<>();
        
        // 创建两个相同的费用子类型
        CardFeeGroupDetailDTO detail1 = createMockCardFeeGroupDetailDTO("SUB001", "TYPE001", "FEE001");
        CardFeeGroupDetailDTO detail2 = createMockCardFeeGroupDetailDTO("SUB001", "TYPE001", "FEE002");
        
        duplicateList.add(detail1);
        duplicateList.add(detail2);
        dto.setCardFeeGroupDetailList(duplicateList);
        
        return dto;
    }

    /**
     * 创建Mock的CardFeeGroupDetailDTO列表
     */
    private List<CardFeeGroupDetailDTO> createMockCardFeeGroupDetailList() {
        List<CardFeeGroupDetailDTO> list = new ArrayList<>();
        list.add(createMockCardFeeGroupDetailDTO("SUB001", "TYPE001", "FEE001"));
        list.add(createMockCardFeeGroupDetailDTO("SUB002", "TYPE002", "FEE002"));
        return list;
    }

    /**
     * 创建Mock的CardFeeGroupDetailDTO
     */
    private CardFeeGroupDetailDTO createMockCardFeeGroupDetailDTO(String subFeeTypeCode, 
                                                                 String feeTypeCode, 
                                                                 String feeCode) {
        CardFeeGroupDetailDTO dto = new CardFeeGroupDetailDTO();
        dto.setSubFeeTypeCode(subFeeTypeCode);
        dto.setFeeTypeCode(feeTypeCode);
        dto.setFeeCode(feeCode);
        dto.setDescription("测试费用");
        dto.setFeeAmount(SimpleTestUtils.createAmount());
        return dto;
    }

    /**
     * 创建Mock的ParmCardFeeGroupInfo列表
     */
    private List<ParmCardFeeGroupInfo> createMockCardFeeGroupInfoList() {
        List<ParmCardFeeGroupInfo> list = new ArrayList<>();
        list.add(createMockParmCardFeeGroupInfo("TEST001"));
        return list;
    }

    /**
     * 创建Mock的ParmCardFeeGroupInfo
     */
    private ParmCardFeeGroupInfo createMockParmCardFeeGroupInfo(String tableId) {
        ParmCardFeeGroupInfo info = new ParmCardFeeGroupInfo();
        info.setId(SimpleTestUtils.createCardNumber());
        info.setOrganizationNumber("001");
        info.setTableId(tableId);
        info.setDescription("测试费用组");
        info.setStatus("1");
        info.setFeeTypeCode("TYPE001");
        info.setSubFeeTypeCode("SUB001");
        info.setFeeCode("FEE001");
        return info;
    }

    /**
     * 创建Mock的CardFeeDefinitionResDTO
     */
    private CardFeeDefinitionResDTO createMockCardFeeDefinitionResDTO() {
        CardFeeDefinitionResDTO dto = new CardFeeDefinitionResDTO();
        dto.setDescription("测试费用定义");
        dto.setFeeAmount(BigDecimal.valueOf(100.00));
        return dto;
    }
}