package com.anytech.anytxn.parameter.authorization.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.authorization.mapper.ParmVelocityControlMapper;
import com.anytech.anytxn.parameter.authorization.mapper.ParmVelocityControlSelfMapper;
import com.anytech.anytxn.parameter.authorization.mapper.ParmVelocityDefinitionSelfMapper;
import com.anytech.anytxn.parameter.authorization.mapper.ParmVelocitySetDetailSelfMapper;
import com.anytech.anytxn.parameter.authorization.service.VelocityControlServiceImpl;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.VelocityControlDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.VelocityControlDetailDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.VelocityDefinitionDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.model.ParmVelocityControl;
import com.anytech.anytxn.parameter.base.authorization.domain.model.ParmVelocityDefinition;
import com.anytech.anytxn.parameter.base.authorization.domain.model.ParmVelocitySetDetail;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;

/**
 * VelocityControlService的单元测试类
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@ExtendWith(MockitoExtension.class)
class VelocityControlServiceTest {

    @Mock
    private ParmVelocityControlMapper parmVelocityControlMapper;

    @Mock
    private ParmVelocityControlSelfMapper parmVelocityControlSelfMapper;

    @Mock
    private ParmVelocitySetDetailSelfMapper velocitySetDetailSelfMapper;

    @Mock
    private ParmVelocityDefinitionSelfMapper velocityDefinitionSelfMapper;

    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private VelocityControlServiceImpl velocityControlService;

    private String testOrganizationNumber;
    private String testCardProductCode;
    private String testAuthTxnType;
    private String testAuthTxnCode;
    private String testCheckCurrency;
    private String testVelocitySetCode;
    private String testId;
    private ParmVelocityControl testParmVelocityControl;
    private VelocityControlDTO testVelocityControlDTO;
    private VelocityControlDetailDTO testVelocityControlDetailDTO;

    @BeforeEach
    void setUp() throws Exception {
        // 通过反射初始化OrgNumberUtils的静态实例
        Field orgNumberUtilField = OrgNumberUtils.class.getDeclaredField("orgNumberUtil");
        orgNumberUtilField.setAccessible(true);
        orgNumberUtilField.set(null, new OrgNumberUtils());
        
        // 初始化测试数据
        testOrganizationNumber = "001";
        testCardProductCode = "CARD001";
        testAuthTxnType = "01";
        testAuthTxnCode = "001";
        testCheckCurrency = "USD";
        testVelocitySetCode = "VS001";
        testId = "123456789";

        // 初始化ParmVelocityControl
        testParmVelocityControl = new ParmVelocityControl();
        testParmVelocityControl.setId(testId);
        testParmVelocityControl.setOrganizationNumber(testOrganizationNumber);
        testParmVelocityControl.setCardProductCode(testCardProductCode);
        testParmVelocityControl.setAuthTxnType(testAuthTxnType);
        testParmVelocityControl.setAuthTxnCode(testAuthTxnCode);
        testParmVelocityControl.setCheckCurrency(testCheckCurrency);
        testParmVelocityControl.setVelocitySetCode(testVelocitySetCode);
        testParmVelocityControl.setDescription("测试流量控制规则");
        testParmVelocityControl.setStatus("1");

        // 初始化VelocityControlDTO
        testVelocityControlDTO = new VelocityControlDTO();
        testVelocityControlDTO.setId(testId);
        testVelocityControlDTO.setOrganizationNumber(testOrganizationNumber);
        testVelocityControlDTO.setCardProductCode(testCardProductCode);
        testVelocityControlDTO.setAuthTxnType(testAuthTxnType);
        testVelocityControlDTO.setAuthTxnCode(testAuthTxnCode);
        testVelocityControlDTO.setCheckCurrency(testCheckCurrency);
        testVelocityControlDTO.setVelocitySetCode(testVelocitySetCode);
        testVelocityControlDTO.setDescription("测试流量控制规则");
        testVelocityControlDTO.setStatus("1");

        // 初始化VelocityControlDetailDTO
        testVelocityControlDetailDTO = new VelocityControlDetailDTO();
        testVelocityControlDetailDTO.setCardProductCode(testCardProductCode);
        testVelocityControlDetailDTO.setAuthTxnType(testAuthTxnType);
        testVelocityControlDetailDTO.setAuthTxnCode(testAuthTxnCode);
        testVelocityControlDetailDTO.setCheckCurrency(testCheckCurrency);
        testVelocityControlDetailDTO.setVelocitySetCode(testVelocitySetCode);
        testVelocityControlDetailDTO.setDescription("测试流量控制规则详情");

        List<VelocityControlDetailDTO> detailList = Arrays.asList(testVelocityControlDetailDTO);
        testVelocityControlDTO.setVelocityControlDetailDtos(detailList);
    }

    /**
     * 测试分页查询流量规则 - 成功场景
     */
    @Test
    void testFindByPage_Success() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn(testOrganizationNumber);
            
            when(parmVelocityControlSelfMapper.selectByCondition(testCardProductCode, testOrganizationNumber))
                .thenReturn(Arrays.asList(testParmVelocityControl));

            // Act
            PageResultDTO<VelocityControlDTO> result = velocityControlService.findByPage(1, 10, testCardProductCode, testOrganizationNumber);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getPage());
            assertEquals(10, result.getRows());
            assertEquals(1, result.getData().size());
            assertEquals(testCardProductCode, result.getData().get(0).getCardProductCode());
            verify(parmVelocityControlSelfMapper).selectByCondition(testCardProductCode, testOrganizationNumber);
        }
    }

    /**
     * 测试分页查询流量规则 - 空机构号使用默认值
     */
    @Test
    void testFindByPage_EmptyOrganizationNumber() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn(testOrganizationNumber);
            
            when(parmVelocityControlSelfMapper.selectByCondition(testCardProductCode, testOrganizationNumber))
                .thenReturn(Arrays.asList(testParmVelocityControl));

            // Act
            PageResultDTO<VelocityControlDTO> result = velocityControlService.findByPage(1, 10, testCardProductCode, "");

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getData().size());
            verify(parmVelocityControlSelfMapper).selectByCondition(testCardProductCode, testOrganizationNumber);
        }
    }

    /**
     * 测试分页查询流量规则 - 数据库异常
     */
    @Test
    void testFindByPage_DatabaseException() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn(testOrganizationNumber);
            
            when(parmVelocityControlSelfMapper.selectByCondition(testCardProductCode, testOrganizationNumber))
                .thenThrow(new RuntimeException("Database connection failed"));

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
                velocityControlService.findByPage(1, 10, testCardProductCode, testOrganizationNumber);
            });
            
            assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST.getCode(), exception.getErrCode());
        }
    }

    /**
     * 测试根据卡片编号查询流量规则 - 成功场景
     */
    @Test
    void testFindByCardProNum_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            when(parmVelocityControlSelfMapper.selectByCardProNum(testCardProductCode, testOrganizationNumber))
                .thenReturn(Arrays.asList(testParmVelocityControl));
            
            beanMappingMock.when(() -> BeanMapping.copy(any(ParmVelocityControl.class), eq(VelocityControlDetailDTO.class)))
                .thenReturn(testVelocityControlDetailDTO);

            // Act
            VelocityControlDTO result = velocityControlService.findByCardProNum(testCardProductCode, testOrganizationNumber);

            // Assert
            assertNotNull(result);
            assertEquals(testCardProductCode, result.getCardProductCode());
            assertEquals(testOrganizationNumber, result.getOrganizationNumber());
            assertEquals(1, result.getVelocityControlDetailDtos().size());
            verify(parmVelocityControlSelfMapper).selectByCardProNum(testCardProductCode, testOrganizationNumber);
        }
    }

    /**
     * 测试根据卡片编号查询流量规则 - 数据库异常
     */
    @Test
    void testFindByCardProNum_DatabaseException() {
        // Arrange
        when(parmVelocityControlSelfMapper.selectByCardProNum(testCardProductCode, testOrganizationNumber))
            .thenThrow(new RuntimeException("Database connection failed"));

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            velocityControlService.findByCardProNum(testCardProductCode, testOrganizationNumber);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST.getCode(), exception.getErrCode());
    }

    /**
     * 测试新增流量规则 - 成功场景
     */
    @Test
    void testAddVelocityControl_Success() {
        // Arrange
        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class)) {
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("tenant001");
            when(numberIdGenerator.generateId("tenant001")).thenReturn(Long.valueOf(testId));
            
            when(parmVelocityControlSelfMapper.selectVelocityControlByUniqueIdx(
                testOrganizationNumber, testCardProductCode, testAuthTxnType, testAuthTxnCode, testCheckCurrency))
                .thenReturn(null);

            // Act
            ParameterCompare result = velocityControlService.addVelocityControl(testVelocityControlDTO);

            // Assert
            assertNotNull(result);
            assertEquals(testCardProductCode, result.getMainParmId());
            verify(parmVelocityControlSelfMapper).selectVelocityControlByUniqueIdx(
                testOrganizationNumber, testCardProductCode, testAuthTxnType, testAuthTxnCode, testCheckCurrency);
        }
    }

    /**
     * 测试新增流量规则 - 参数为空
     */
    @Test
    void testAddVelocityControl_NullParameter() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            velocityControlService.addVelocityControl(null);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试新增流量规则 - 记录已存在
     */
    @Test
    void testAddVelocityControl_RecordExists() {
        // Arrange
        when(parmVelocityControlSelfMapper.selectVelocityControlByUniqueIdx(
            testOrganizationNumber, testCardProductCode, testAuthTxnType, testAuthTxnCode, testCheckCurrency))
            .thenReturn(testParmVelocityControl);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            velocityControlService.addVelocityControl(testVelocityControlDTO);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_DATA_ALREADY_EXISTS_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试修改流量规则 - 成功场景
     */
    @Test
    void testUpdateVelocityControl_Success() {
        // Arrange
        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class)) {
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("tenant001");
            when(numberIdGenerator.generateId("tenant001")).thenReturn(Long.valueOf(testId));
            
            when(parmVelocityControlSelfMapper.selectByCardProNum(testCardProductCode, testOrganizationNumber))
                .thenReturn(Arrays.asList(testParmVelocityControl));

            // Act
            ParameterCompare result = velocityControlService.updateVelocityControl(testVelocityControlDTO);

            // Assert
            assertNotNull(result);
            assertEquals(testCardProductCode, result.getMainParmId());
            verify(parmVelocityControlSelfMapper).selectByCardProNum(testCardProductCode, testOrganizationNumber);
        }
    }

    /**
     * 测试修改流量规则 - 参数为空
     */
    @Test
    void testUpdateVelocityControl_NullParameter() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            velocityControlService.updateVelocityControl(null);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试修改流量规则 - 卡片编号为空
     */
    @Test
    void testUpdateVelocityControl_EmptyCardProductCode() {
        // Arrange
        testVelocityControlDTO.setCardProductCode("");

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            velocityControlService.updateVelocityControl(testVelocityControlDTO);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试修改流量规则 - 记录不存在
     */
    @Test
    void testUpdateVelocityControl_RecordNotFound() {
        // Arrange
        when(parmVelocityControlSelfMapper.selectByCardProNum(testCardProductCode, testOrganizationNumber))
            .thenReturn(Collections.emptyList());

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            velocityControlService.updateVelocityControl(testVelocityControlDTO);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试删除流量规则 - 成功场景
     */
    @Test
    void testDeleteVelocityControl_Success() {
        // Arrange
        when(parmVelocityControlSelfMapper.selectByCardProNum(testCardProductCode, testOrganizationNumber))
            .thenReturn(Arrays.asList(testParmVelocityControl));

        // Act
        ParameterCompare result = velocityControlService.deleteVelocityControl(testCardProductCode, testOrganizationNumber);

        // Assert
        assertNotNull(result);
        assertEquals(testCardProductCode, result.getMainParmId());
        verify(parmVelocityControlSelfMapper).selectByCardProNum(testCardProductCode, testOrganizationNumber);
    }

    /**
     * 测试删除流量规则 - 卡片编号为空
     */
    @Test
    void testDeleteVelocityControl_EmptyCardProductCode() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            velocityControlService.deleteVelocityControl("", testOrganizationNumber);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试删除流量规则 - 记录不存在
     */
    @Test
    void testDeleteVelocityControl_RecordNotFound() {
        // Arrange
        when(parmVelocityControlSelfMapper.selectByCardProNum(testCardProductCode, testOrganizationNumber))
            .thenReturn(Collections.emptyList());

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            velocityControlService.deleteVelocityControl(testCardProductCode, testOrganizationNumber);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试根据唯一索引查询流量规则 - 成功场景
     */
    @Test
    void testFindByUniqueIndex_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            when(parmVelocityControlSelfMapper.selectVelocityControlByUniqueIdx(
                testOrganizationNumber, testCardProductCode, testAuthTxnType, testAuthTxnCode, testCheckCurrency))
                .thenReturn(testParmVelocityControl);
            
            beanMappingMock.when(() -> BeanMapping.copy(testParmVelocityControl, VelocityControlDTO.class))
                .thenReturn(testVelocityControlDTO);

            // Act
            VelocityControlDTO result = velocityControlService.findByUniqueIndex(
                testOrganizationNumber, testCardProductCode, testAuthTxnType, testAuthTxnCode, testCheckCurrency);

            // Assert
            assertNotNull(result);
            assertEquals(testCardProductCode, result.getCardProductCode());
            assertEquals(testAuthTxnType, result.getAuthTxnType());
            verify(parmVelocityControlSelfMapper).selectVelocityControlByUniqueIdx(
                testOrganizationNumber, testCardProductCode, testAuthTxnType, testAuthTxnCode, testCheckCurrency);
        }
    }

    /**
     * 测试根据唯一索引查询流量规则 - 记录不存在
     */
    @Test
    void testFindByUniqueIndex_RecordNotFound() {
        // Arrange
        when(parmVelocityControlSelfMapper.selectVelocityControlByUniqueIdx(
            testOrganizationNumber, testCardProductCode, testAuthTxnType, testAuthTxnCode, testCheckCurrency))
            .thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            velocityControlService.findByUniqueIndex(
                testOrganizationNumber, testCardProductCode, testAuthTxnType, testAuthTxnCode, testCheckCurrency);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT.getCode(), exception.getErrCode());
    }
} 
