package com.anytech.anytxn.parameter.common.service;

import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.base.common.domain.dto.ParmCurrencyCodeDTO;
import com.anytech.anytxn.parameter.base.common.domain.model.unicast.ParmCurrencyCode;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.broadcast.ParmCurrencyCodeMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.ParmCurrencyCodeSelfMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * CurrencyCodeServiceImpl test class
 */
@ExtendWith(MockitoExtension.class)
class CurrencyCodeServiceTest {

    @Mock
    private ParmCurrencyCodeSelfMapper parmCurrencyCodeSelfMapper;

    @Mock
    private ParmCurrencyCodeMapper parmCurrencyCodeMapper;

    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private CurrencyCodeServiceImpl currencyCodeService;

    private ParmCurrencyCodeDTO testCurrencyCodeDTO;
    private ParmCurrencyCode testCurrencyCode;

    @BeforeEach
    void setUp() {
        testCurrencyCodeDTO = new ParmCurrencyCodeDTO();
        testCurrencyCodeDTO.setId("123456789012345678");
        testCurrencyCodeDTO.setCurrencyCode("840");
        testCurrencyCodeDTO.setAlphabeticCode("USD");
        testCurrencyCodeDTO.setDescription("US Dollar");
        testCurrencyCodeDTO.setDecimalPlace(2);
        testCurrencyCodeDTO.setDecimalSeparator(".");
        testCurrencyCodeDTO.setAlphabeticCodePosition("L");
        testCurrencyCodeDTO.setSignPosition("B");
        testCurrencyCodeDTO.setDciSettlementIndicator("Y");
        testCurrencyCodeDTO.setVersionNumber(1L);
        testCurrencyCodeDTO.setOrganizationNumber("0000");
        testCurrencyCodeDTO.setUpdateBy("TEST_USER");
        testCurrencyCodeDTO.setCreateTime(LocalDateTime.now());
        testCurrencyCodeDTO.setUpdateTime(LocalDateTime.now());

        testCurrencyCode = new ParmCurrencyCode();
        testCurrencyCode.setId("123456789012345678");
        testCurrencyCode.setCurrencyCode("840");
        testCurrencyCode.setAlphabeticCode("USD");
        testCurrencyCode.setDescription("US Dollar");
        testCurrencyCode.setDecimalPlace(2);
        testCurrencyCode.setDecimalSeparator(".");
        testCurrencyCode.setAlphabeticCodePosition("L");
        testCurrencyCode.setSignPosition("B");
        testCurrencyCode.setDciSettlementIndicator("Y");
        testCurrencyCode.setOrganizationNumber("0000");
    }

    /**
     * Test selectCurrencyCodeBasic method with successful result
     */
    @Test
    void testSelectCurrencyCodeBasic_Success() {
        try (MockedStatic<PageHelper> pageHelperMock = mockStatic(PageHelper.class)) {
            // Arrange
            Integer page = 1;
            Integer rows = 10;
            Page<ParmCurrencyCode> mockPage = new Page<>(page, rows);
            mockPage.setTotal(1L);
            mockPage.setPages(1);
            List<ParmCurrencyCode> currencyCodes = Arrays.asList(testCurrencyCode);

            Mockito.lenient().when(parmCurrencyCodeSelfMapper.selectPageList()).thenReturn(currencyCodes);

            // Act
            PageResultDTO<ParmCurrencyCodeDTO> result = currencyCodeService.selectCurrencyCodeBasic(page, rows);

            // Assert
            assertNotNull(result);
            assertEquals(page, result.getPage());
            assertEquals(rows, result.getRows());
            assertEquals(1L, result.getTotal());
            assertNotNull(result.getData());
            assertEquals(1, result.getData().size());
            assertEquals("USD", result.getData().get(0).getAlphabeticCode());
        }
    }

    /**
     * Test selectCurrencyCodeBasic method with empty result
     */
    @Test
    void testSelectCurrencyCodeBasic_EmptyResult() {
        try (MockedStatic<PageHelper> pageHelperMock = mockStatic(PageHelper.class)) {
            // Arrange
            Integer page = 1;
            Integer rows = 10;
            Page<ParmCurrencyCode> mockPage = new Page<>(page, rows);

            Mockito.lenient().when(parmCurrencyCodeSelfMapper.selectPageList()).thenReturn(Collections.emptyList());

            // Act
            PageResultDTO<ParmCurrencyCodeDTO> result = currencyCodeService.selectCurrencyCodeBasic(page, rows);

            // Assert
            assertNull(result);
        }
    }

    /**
     * Test selectById method with successful result
     */
    @Test
    void testSelectById_Success() {
        // Arrange
        String id = "123456789012345678";
        Mockito.lenient().when(parmCurrencyCodeSelfMapper.selectByPrimaryKey(id)).thenReturn(testCurrencyCode);

        // Act
        ParmCurrencyCodeDTO result = currencyCodeService.selectById(id);

        // Assert
        assertNotNull(result);
        assertEquals("USD", result.getAlphabeticCode());
        assertEquals("840", result.getCurrencyCode());
        assertEquals("US Dollar", result.getDescription());
    }

    /**
     * Test selectById method with null result
     */
    @Test
    void testSelectById_NotFound() {
        // Arrange
        String id = "nonexistent";
        Mockito.lenient().when(parmCurrencyCodeSelfMapper.selectByPrimaryKey(id)).thenReturn(null);

        // Act
        ParmCurrencyCodeDTO result = currencyCodeService.selectById(id);

        // Assert
        assertNull(result);
    }

    /**
     * Test add method with successful creation
     */
    @Test
    void testAdd_Success() {
        try (MockedStatic<com.anytech.anytxn.common.core.utils.TenantUtils> tenantUtilsMock = 
             mockStatic(com.anytech.anytxn.common.core.utils.TenantUtils.class)) {
            
            // Arrange
            Mockito.lenient().when(parmCurrencyCodeSelfMapper.selectAlphabticon("USD")).thenReturn(null);
            Mockito.lenient().when(numberIdGenerator.generateId(anyString())).thenReturn(123456789012345678L);
            tenantUtilsMock.when(com.anytech.anytxn.common.core.utils.TenantUtils::getTenantId).thenReturn("TENANT_001");

            // Act
            ParameterCompare result = currencyCodeService.add(testCurrencyCodeDTO);

            // Assert
            assertNotNull(result);
            assertEquals("123456789012345678", result.getMainParmId());
            assertNotNull(result.getAfter());
        }
    }

    /**
     * Test add method with duplicate alphabetic code
     */
    @Test
    void testAdd_DuplicateAlphabeticCode() {
        // Arrange
        Mockito.lenient().when(parmCurrencyCodeSelfMapper.selectAlphabticon("USD")).thenReturn(testCurrencyCode);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            currencyCodeService.add(testCurrencyCodeDTO);
        });

    }

    /**
     * Test delete method with successful deletion
     */
    @Test
    void testDelete_Success() {
        // Arrange
        String id = "123456789012345678";
        Mockito.lenient().when(parmCurrencyCodeSelfMapper.selectByPrimaryKey(id)).thenReturn(testCurrencyCode);

        // Act
        ParameterCompare result = currencyCodeService.delete(id);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getBefore());
        assertEquals("0000", ((ParmCurrencyCode) result.getBefore()).getOrganizationNumber());
    }

    /**
     * Test delete method with null id
     */
    @Test
    void testDelete_NullId() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            currencyCodeService.delete(null);
        });

    }

    /**
     * Test delete method with non-existent data
     */
    @Test
    void testDelete_DataNotExists() {
        // Arrange
        String id = "nonexistent";
        Mockito.lenient().when(parmCurrencyCodeSelfMapper.selectByPrimaryKey(id)).thenReturn(null);

        // Act
        ParameterCompare result = currencyCodeService.delete(id);

        // Assert
        assertNull(result);
    }

    /**
     * Test update method with successful update
     */
    @Test
    void testUpdate_Success() {
        // Arrange
        Mockito.lenient().when(parmCurrencyCodeSelfMapper.selectByPrimaryKey("123456789012345678")).thenReturn(testCurrencyCode);

        // Act
        ParameterCompare result = currencyCodeService.update(testCurrencyCodeDTO);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getAfter());
        assertNotNull(result.getBefore());
        assertEquals("0000", ((ParmCurrencyCode) result.getBefore()).getOrganizationNumber());
    }

    /**
     * Test update method with null parameter
     */
    @Test
    void testUpdate_NullParameter() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            currencyCodeService.update(null);
        });

    }

    /**
     * Test update method with non-existent data
     */
    @Test
    void testUpdate_DataNotExists() {
        // Arrange
        Mockito.lenient().when(parmCurrencyCodeSelfMapper.selectByPrimaryKey("123456789012345678")).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            currencyCodeService.update(testCurrencyCodeDTO);
        });
        

    }

    /**
     * Test updateDb method with successful update
     */
    @Test
    void testUpdateDb_Success() throws Exception {
        // Arrange
        ParmModificationRecord record = new ParmModificationRecord();
        record.setParmBody("{\"id\":\"123456789012345678\",\"currencyCode\":\"840\",\"alphabeticCode\":\"USD\",\"description\":\"US Dollar\",\"decimalPlace\":2,\"decimalSeparator\":\".\",\"alphabeticCodePosition\":\"L\",\"signPosition\":\"B\",\"displayPriorityCode\":\"1\",\"dciSettlementIndicator\":\"Y\"}");
        
        Mockito.lenient().when(parmCurrencyCodeMapper.updateByPrimaryKey(any(ParmCurrencyCode.class))).thenReturn(1);

        // Act
        Method updateDbMethod = CurrencyCodeServiceImpl.class.getDeclaredMethod("updateDb", ParmModificationRecord.class);
        updateDbMethod.setAccessible(true);
        boolean result = (boolean) updateDbMethod.invoke(currencyCodeService, record);

        // Assert
        assertTrue(result);
    }

    /**
     * Test insertDb method with successful insert
     */
    @Test
    void testInsertDb_Success() throws Exception {
        try (MockedStatic<com.anytech.anytxn.common.core.utils.TenantUtils> tenantUtilsMock = 
             mockStatic(com.anytech.anytxn.common.core.utils.TenantUtils.class)) {
            
            // Arrange
            ParmModificationRecord record = new ParmModificationRecord();
            record.setParmBody("{\"currencyCode\":\"840\",\"alphabeticCode\":\"USD\",\"description\":\"US Dollar\",\"decimalPlace\":2,\"decimalSeparator\":\".\",\"alphabeticCodePosition\":\"L\",\"signPosition\":\"B\",\"displayPriorityCode\":\"1\",\"dciSettlementIndicator\":\"Y\"}");
            record.setApplicationBy("TEST_USER");
            
            Mockito.lenient().when(numberIdGenerator.generateId(anyString())).thenReturn(123456789012345678L);
            tenantUtilsMock.when(com.anytech.anytxn.common.core.utils.TenantUtils::getTenantId).thenReturn("TENANT_001");
            Mockito.lenient().when(parmCurrencyCodeSelfMapper.insert(any(ParmCurrencyCodeDTO.class))).thenReturn(1);

            // Act
            Method insertDbMethod = CurrencyCodeServiceImpl.class.getDeclaredMethod("insertDb", ParmModificationRecord.class);
            insertDbMethod.setAccessible(true);
            boolean result = (boolean) insertDbMethod.invoke(currencyCodeService, record);

            // Assert
            assertTrue(result);
        }
    }

    /**
     * Test deleteDb method with successful deletion
     */
    @Test
    void testDeleteDb_Success() throws Exception {
        // Arrange
        ParmModificationRecord record = new ParmModificationRecord();
        record.setParmBody("{\"id\":\"123456789012345678\"}");
        
        Mockito.lenient().when(parmCurrencyCodeSelfMapper.deleteByPrimaryKey("123456789012345678")).thenReturn(1);

        // Act
        Method deleteDbMethod = CurrencyCodeServiceImpl.class.getDeclaredMethod("deleteDb", ParmModificationRecord.class);
        deleteDbMethod.setAccessible(true);
        boolean result = (boolean) deleteDbMethod.invoke(currencyCodeService, record);

        // Assert
        assertTrue(result);
    }
}