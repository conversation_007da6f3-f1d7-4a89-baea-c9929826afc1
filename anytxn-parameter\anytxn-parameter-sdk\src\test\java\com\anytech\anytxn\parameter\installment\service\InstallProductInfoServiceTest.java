package com.anytech.anytxn.parameter.installment.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallProductInfoReqDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallProductInfoResDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallProductInfoSearchDTO;
import com.anytech.anytxn.parameter.base.installment.domain.model.InstallProductInfo;
import com.anytech.anytxn.parameter.installment.mapper.InstallProductInfoMapper;
import com.anytech.anytxn.parameter.installment.mapper.InstallProductInfoSelfMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * InstallProductInfoService 单元测试类
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class InstallProductInfoServiceTest {

    @Mock
    private InstallProductInfoMapper installProductInfoMapper;
    
    @Mock
    private InstallProductInfoSelfMapper installProductInfoSelfMapper;
    
    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private InstallProductInfoServiceImpl installProductInfoService;

    private MockedStatic<OrgNumberUtils> orgNumberUtilsMockedStatic;
    private MockedStatic<TenantUtils> tenantUtilsMockedStatic;
    private MockedStatic<BeanMapping> beanMappingMockedStatic;
    private MockedStatic<PageHelper> pageHelperMockedStatic;

    @BeforeEach
    void setUp() {
        orgNumberUtilsMockedStatic = mockStatic(OrgNumberUtils.class);
        tenantUtilsMockedStatic = mockStatic(TenantUtils.class);
        beanMappingMockedStatic = mockStatic(BeanMapping.class);
        pageHelperMockedStatic = mockStatic(PageHelper.class);

        // 设置静态方法的默认返回值
        Mockito.lenient().when(OrgNumberUtils.getOrg()).thenReturn("0001");
        Mockito.lenient().when(TenantUtils.getTenantId()).thenReturn("6001");
    }

    @AfterEach
    void tearDown() {
        if (orgNumberUtilsMockedStatic != null) {
            orgNumberUtilsMockedStatic.close();
        }
        if (tenantUtilsMockedStatic != null) {
            tenantUtilsMockedStatic.close();
        }
        if (beanMappingMockedStatic != null) {
            beanMappingMockedStatic.close();
        }
        if (pageHelperMockedStatic != null) {
            pageHelperMockedStatic.close();
        }
    }

    /**
     * 测试根据ID查询分期产品参数 - 成功场景
     */
    @Test
    void testGetById_Success() {
        // Arrange
        String id = "*********";
        InstallProductInfo productInfo = createValidInstallProductInfo();
        InstallProductInfoResDTO expectedResult = createValidInstallProductInfoResDTO();
        
        Mockito.lenient().when(installProductInfoMapper.selectByPrimaryKey(id)).thenReturn(productInfo);
        beanMappingMockedStatic.when(() -> BeanMapping.copy(productInfo, InstallProductInfoResDTO.class)).thenReturn(expectedResult);

        // Act
        InstallProductInfoResDTO result = installProductInfoService.getById(id);

        // Assert
        assertNotNull(result);
        assertEquals(expectedResult.getId(), result.getId());
        verify(installProductInfoMapper).selectByPrimaryKey(id);
    }

    /**
     * 测试根据ID查询分期产品参数 - ID为空
     */
    @Test
    void testGetById_IdNull() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> installProductInfoService.getById(null));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试根据ID查询分期产品参数 - 数据不存在
     */
    @Test
    void testGetById_DataNotExists() {
        // Arrange
        String id = "*********";
        
        Mockito.lenient().when(installProductInfoMapper.selectByPrimaryKey(id)).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> installProductInfoService.getById(id));
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_PRODUCT_INFO_BY_ID_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试添加分期产品参数 - 成功场景
     */
    @Test
    void testAdd_Success() {
        // Arrange
        InstallProductInfoReqDTO reqDTO = createValidInstallProductInfoReqDTO();
        InstallProductInfo productInfo = createValidInstallProductInfo();
        
        Mockito.lenient().when(installProductInfoSelfMapper.isExists(reqDTO.getOrganizationNumber(), reqDTO.getProductCode())).thenReturn(0);
        Mockito.lenient().when(numberIdGenerator.generateId(anyString())).thenReturn(*********L);
        beanMappingMockedStatic.when(() -> BeanMapping.copy(reqDTO, InstallProductInfo.class)).thenReturn(productInfo);

        // Act
        ParameterCompare result = installProductInfoService.add(reqDTO);

        // Assert
        assertNotNull(result);
        verify(installProductInfoSelfMapper).isExists(reqDTO.getOrganizationNumber(), reqDTO.getProductCode());
    }

    /**
     * 测试添加分期产品参数 - 必填参数为空
     */
    @Test
    void testAdd_RequiredParametersEmpty() {
        // Arrange
        InstallProductInfoReqDTO reqDTO = new InstallProductInfoReqDTO();
        reqDTO.setOrganizationNumber("");

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> installProductInfoService.add(reqDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_ORG_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试添加分期产品参数 - 数据已存在
     */
    @Test
    void testAdd_DataExists() {
        // Arrange
        InstallProductInfoReqDTO reqDTO = createValidInstallProductInfoReqDTO();
        
        Mockito.lenient().when(installProductInfoSelfMapper.isExists(reqDTO.getOrganizationNumber(), reqDTO.getProductCode())).thenReturn(1);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> installProductInfoService.add(reqDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.D_EXIST_INSTALL_PRODUCT_INFO_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试删除分期产品参数 - 成功场景
     */
    @Test
    void testRemove_Success() {
        // Arrange
        String id = "*********";
        InstallProductInfo productInfo = createValidInstallProductInfo();
        
        Mockito.lenient().when(installProductInfoMapper.selectByPrimaryKey(id)).thenReturn(productInfo);

        // Act
        ParameterCompare result = installProductInfoService.remove(id);

        // Assert
        assertNotNull(result);
        verify(installProductInfoMapper).selectByPrimaryKey(id);
    }

    /**
     * 测试删除分期产品参数 - ID为空
     */
    @Test
    void testRemove_IdNull() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> installProductInfoService.remove(null));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试删除分期产品参数 - 数据不存在
     */
    @Test
    void testRemove_DataNotExists() {
        // Arrange
        String id = "*********";
        
        Mockito.lenient().when(installProductInfoMapper.selectByPrimaryKey(id)).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> installProductInfoService.remove(id));
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_PRODUCT_INFO_BY_ID_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 创建有效的InstallProductInfoReqDTO对象
     */
    private InstallProductInfoReqDTO createValidInstallProductInfoReqDTO() {
        InstallProductInfoReqDTO reqDTO = new InstallProductInfoReqDTO();
        reqDTO.setId(*********L);
        reqDTO.setOrganizationNumber("0001");
        reqDTO.setProductCode("P001001");
        reqDTO.setProdType("A");
        reqDTO.setTerm(12);
        reqDTO.setCycle("M");
        reqDTO.setPaymentWay("1");
        reqDTO.setFeeCodeId("F001");
        reqDTO.setPostingTransactionParmId("T001");
        reqDTO.setAdvancedEndParmId("E001");
        reqDTO.setAutoInstallmentFlag("1");
        reqDTO.setProductDesc("Test Product");
        reqDTO.setStatus(Constants.ENABLED);
        reqDTO.setFeeReceiveFlag("1");
        reqDTO.setLimitProcessMode("1");
        reqDTO.setBillDateMethod("1");
        reqDTO.setBalanceMethod("1");
        reqDTO.setGuaranteeType("1");
        reqDTO.setTransferFee(new BigDecimal("10.00"));
        reqDTO.setAmountOfDelayed(50);
        reqDTO.setMinPaymentAmount(new BigDecimal("100.00"));
        reqDTO.setSupportInstallmentTerms("3,6,12,24");
        reqDTO.setNonInterestBearing("0");
        reqDTO.setInstalmentInterestParmTableId("I001");
        reqDTO.setUpdateBy("TEST_USER");
        reqDTO.setVersionNumber(1L);
        return reqDTO;
    }

    /**
     * 创建有效的InstallProductInfo对象
     */
    private InstallProductInfo createValidInstallProductInfo() {
        InstallProductInfo productInfo = new InstallProductInfo();
        productInfo.setId("*********");
        productInfo.setOrganizationNumber("0001");
        productInfo.setProductCode("P001001");
        productInfo.setProdType("A");
        productInfo.setTerm(12);
        productInfo.setCycle("M");
        productInfo.setPaymentWay("1");
        productInfo.setFeeCodeId("F001");
        productInfo.setPostingTransactionParmId("T001");
        productInfo.setAdvancedEndParmId("E001");
        productInfo.setAutoInstallmentFlag("1");
        productInfo.setProductDesc("Test Product");
        productInfo.setStatus(Constants.ENABLED);
        productInfo.setFeeReceiveFlag("1");
        productInfo.setLimitProcessMode("1");
        productInfo.setBillDateMethod("1");
        productInfo.setBalanceMethod("1");
        productInfo.setGuaranteeType("1");
        productInfo.setTransferFee(new BigDecimal("10.00"));
        productInfo.setAmountOfDelayed(50);
        productInfo.setMinPaymentAmount(new BigDecimal("100.00"));
        productInfo.setSupportInstallmentTerms("3,6,12,24");
        productInfo.setNonInterestBearing("0");
        productInfo.setInstalmentInterestParmTableId("I001");
        productInfo.setVersionNumber(1L);
        productInfo.setCreateTime(LocalDateTime.now());
        productInfo.setUpdateTime(LocalDateTime.now());
        productInfo.setUpdateBy("TEST_USER");
        return productInfo;
    }

    /**
     * 测试修改分期产品参数 - 成功场景
     */
    @Test
    void testModify_Success() {
        // Arrange
        InstallProductInfoReqDTO reqDTO = createValidInstallProductInfoReqDTO();
        InstallProductInfo existingProductInfo = createValidInstallProductInfo();

        Mockito.lenient().when(installProductInfoMapper.selectByPrimaryKey(reqDTO.getId().toString())).thenReturn(existingProductInfo);
        Mockito.lenient().when(installProductInfoSelfMapper.isExists(reqDTO.getOrganizationNumber(), reqDTO.getProductCode())).thenReturn(0);

        // Act
        ParameterCompare result = installProductInfoService.modify(reqDTO);

        // Assert
        assertNotNull(result);
        verify(installProductInfoMapper).selectByPrimaryKey(reqDTO.getId().toString());
    }

    /**
     * 测试修改分期产品参数 - ID为空
     */
    @Test
    void testModify_IdNull() {
        // Arrange
        InstallProductInfoReqDTO reqDTO = createValidInstallProductInfoReqDTO();
        reqDTO.setId(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installProductInfoService.modify(reqDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试修改分期产品参数 - 数据不存在
     */
    @Test
    void testModify_DataNotExists() {
        // Arrange
        InstallProductInfoReqDTO reqDTO = createValidInstallProductInfoReqDTO();

        Mockito.lenient().when(installProductInfoMapper.selectByPrimaryKey(reqDTO.getId().toString())).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installProductInfoService.modify(reqDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_PRODUCT_INFO_BY_ID_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试分页查询分期产品参数 - 成功场景
     */
    @Test
    void testFindPage_Success() {
        // Arrange
        Integer pageNum = 1;
        Integer pageSize = 10;
        InstallProductInfoSearchDTO searchDTO = new InstallProductInfoSearchDTO();
        searchDTO.setOrganizationNumber("0001");

        List<InstallProductInfo> productInfoList = Collections.singletonList(createValidInstallProductInfo());
        List<InstallProductInfoResDTO> expectedResults = Collections.singletonList(createValidInstallProductInfoResDTO());
        Page<InstallProductInfo> page = new Page<>(pageNum, pageSize);
        page.setTotal(1);
        page.setPages(1);

        pageHelperMockedStatic.when(() -> PageHelper.startPage(pageNum, pageSize)).thenReturn(page);
        Mockito.lenient().when(installProductInfoSelfMapper.selectByCondition(any(InstallProductInfoSearchDTO.class))).thenReturn(productInfoList);
        beanMappingMockedStatic.when(() -> BeanMapping.copyList(productInfoList, InstallProductInfoResDTO.class)).thenReturn(expectedResults);

        // Act
        PageResultDTO<InstallProductInfoResDTO> result = installProductInfoService.findPage(pageNum, pageSize, searchDTO);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getData().size());
        verify(installProductInfoSelfMapper).selectByCondition(any(InstallProductInfoSearchDTO.class));
    }

    /**
     * 测试查询所有分期产品参数 - 成功场景
     */
    @Test
    void testFindAll_Success() {
        // Arrange
        String organizationNumber = "0001";
        String installmentType = "A";

        List<InstallProductInfo> productInfoList = Collections.singletonList(createValidInstallProductInfo());
        List<InstallProductInfoResDTO> expectedResults = Collections.singletonList(createValidInstallProductInfoResDTO());

        Mockito.lenient().when(installProductInfoSelfMapper.selectAll(true, organizationNumber)).thenReturn(productInfoList);
        beanMappingMockedStatic.when(() -> BeanMapping.copyList(productInfoList, InstallProductInfoResDTO.class)).thenReturn(expectedResults);

        // Act
        List<InstallProductInfoResDTO> result = installProductInfoService.findAll(organizationNumber, installmentType);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(installProductInfoSelfMapper).selectAll(true, organizationNumber);
    }

    /**
     * 测试根据产品码查询是否存在 - 成功场景
     */
    @Test
    void testGetInstallProInfoIsExistByCode_Success() {
        // Arrange
        String productCode = "P001001";
        String organizationNumber = "0001";

        Mockito.lenient().when(installProductInfoSelfMapper.getInstallProInfoIsExistByCode(productCode, organizationNumber)).thenReturn(1);

        // Act
        Map<String, Object> result = installProductInfoService.getInstallProInfoIsExistByCode(productCode, organizationNumber);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.get("productCode"));
        verify(installProductInfoSelfMapper).getInstallProInfoIsExistByCode(productCode, organizationNumber);
    }

    /**
     * 测试根据产品码查询是否存在 - 产品码为空
     */
    @Test
    void testGetInstallProInfoIsExistByCode_ProductCodeEmpty() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installProductInfoService.getInstallProInfoIsExistByCode("", "0001"));
        assertEquals(AnyTxnParameterRespCodeEnum.D_INSTALL_PRODUCT_INFO_PRODUCT_CODE_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试根据产品码查询是否存在 - 数据不存在
     */
    @Test
    void testGetInstallProInfoIsExistByCode_DataNotExists() {
        // Arrange
        String productCode = "P001001";
        String organizationNumber = "0001";

        Mockito.lenient().when(installProductInfoSelfMapper.getInstallProInfoIsExistByCode(productCode, organizationNumber)).thenReturn(0);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installProductInfoService.getInstallProInfoIsExistByCode(productCode, organizationNumber));
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_PRODUCT_INFO_BY_PRODUCT_CODE_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 创建有效的InstallProductInfoResDTO对象
     */
    private InstallProductInfoResDTO createValidInstallProductInfoResDTO() {
        InstallProductInfoResDTO resDTO = new InstallProductInfoResDTO();
        resDTO.setId("*********");
        resDTO.setOrganizationNumber("0001");
        resDTO.setProductCode("P001001");
        resDTO.setProdType("A");
        resDTO.setTerm(12);
        resDTO.setCycle("M");
        resDTO.setPaymentWay("1");
        resDTO.setFeeCodeId("F001");
        resDTO.setPostingTransactionParmId("T001");
        resDTO.setAdvancedEndParmId("E001");
        resDTO.setAutoInstallmentFlag("1");
        resDTO.setProductDesc("Test Product");
        resDTO.setStatus(Constants.ENABLED);
        resDTO.setFeeReceiveFlag("1");
        resDTO.setLimitProcessMode("1");
        resDTO.setBillDateMethod("1");
        resDTO.setBalanceMethod("1");
        resDTO.setGuaranteeType("1");
        resDTO.setTransferFee(new BigDecimal("10.00"));
        resDTO.setAmountOfDelayed(50);
        resDTO.setMinPaymentAmount(new BigDecimal("100.00"));
        resDTO.setSupportInstallmentTerms("3,6,12,24");
        resDTO.setNonInterestBearing("0");
        resDTO.setInstalmentInterestParmTableId("I001");
        resDTO.setVersionNumber(1L);
        resDTO.setCreateTime(LocalDateTime.now());
        resDTO.setUpdateTime(LocalDateTime.now());
        resDTO.setUpdateBy("TEST_USER");
        return resDTO;
    }

    /**
     * 测试根据机构号和产品码查询 - 成功场景
     */
    @Test
    void testFindByIndex_Success() {
        // Arrange
        String organizationNumber = "0001";
        String productCode = "P001001";
        InstallProductInfo productInfo = createValidInstallProductInfo();
        InstallProductInfoResDTO expectedResult = createValidInstallProductInfoResDTO();

        Mockito.lenient().when(installProductInfoSelfMapper.selectByIndex(organizationNumber, productCode)).thenReturn(productInfo);
        beanMappingMockedStatic.when(() -> BeanMapping.copy(productInfo, InstallProductInfoResDTO.class)).thenReturn(expectedResult);

        // Act
        InstallProductInfoResDTO result = installProductInfoService.findByIndex(organizationNumber, productCode);

        // Assert
        assertNotNull(result);
        assertEquals(expectedResult.getId(), result.getId());
        verify(installProductInfoSelfMapper).selectByIndex(organizationNumber, productCode);
    }

    /**
     * 测试根据机构号和产品码查询 - 机构号为空
     */
    @Test
    void testFindByIndex_OrganizationNumberEmpty() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installProductInfoService.findByIndex("", "P001001"));
        assertEquals(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_ORG_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试根据机构号和产品码查询 - 产品码为空
     */
    @Test
    void testFindByIndex_ProductCodeEmpty() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installProductInfoService.findByIndex("0001", ""));
        assertEquals(AnyTxnParameterRespCodeEnum.D_INSTALL_PRODUCT_INFO_PRODUCT_CODE_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试根据机构号和产品码查询 - 数据不存在
     */
    @Test
    void testFindByIndex_DataNotExists() {
        // Arrange
        String organizationNumber = "0001";
        String productCode = "P001001";

        Mockito.lenient().when(installProductInfoSelfMapper.selectByIndex(organizationNumber, productCode)).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installProductInfoService.findByIndex(organizationNumber, productCode));
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_PRODUCT_INFO_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试根据机构号、分期类型、期数、还款方式、手续费收取方式、尾款方式查询 - 成功场景
     */
    @Test
    void testFindByIndexOrgNumTypeTermPayWayFee_Success() {
        // Arrange
        String organizationNumber = "0001";
        String prodType = "A";
        Integer term = 12;
        String paymentWay = "1";
        String feeReceiveFlag = "1";
        String balanceMethod = "1";
        InstallProductInfo productInfo = createValidInstallProductInfo();
        InstallProductInfoResDTO expectedResult = createValidInstallProductInfoResDTO();

        Mockito.lenient().when(installProductInfoSelfMapper.selectByIndexTwo(
            organizationNumber, prodType, term, paymentWay, feeReceiveFlag, balanceMethod)).thenReturn(productInfo);
        beanMappingMockedStatic.when(() -> BeanMapping.copy(productInfo, InstallProductInfoResDTO.class)).thenReturn(expectedResult);

        // Act
        InstallProductInfoResDTO result = installProductInfoService.findByIndexOrgNumTypeTermPayWayFee(
            organizationNumber, prodType, term, paymentWay, feeReceiveFlag, balanceMethod);

        // Assert
        assertNotNull(result);
        assertEquals(expectedResult.getId(), result.getId());
        verify(installProductInfoSelfMapper).selectByIndexTwo(
            organizationNumber, prodType, term, paymentWay, feeReceiveFlag, balanceMethod);
    }

    /**
     * 测试根据机构号、分期类型、期数、还款方式、手续费收取方式、尾款方式查询 - 机构号为空
     */
    @Test
    void testFindByIndexOrgNumTypeTermPayWayFee_OrganizationNumberEmpty() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installProductInfoService.findByIndexOrgNumTypeTermPayWayFee("", "A", 12, "1", "1", "1"));
        assertEquals(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_ORG_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试根据机构号、分期类型、期数、还款方式、手续费收取方式、尾款方式查询 - 分期类型为空
     */
    @Test
    void testFindByIndexOrgNumTypeTermPayWayFee_ProdTypeEmpty() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installProductInfoService.findByIndexOrgNumTypeTermPayWayFee("0001", "", 12, "1", "1", "1"));
        assertEquals(AnyTxnParameterRespCodeEnum.D_INSTALL_PRODUCT_INFO_PRODUCT_TYPE_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试根据机构号、分期类型、期数、还款方式、手续费收取方式、尾款方式查询 - 期数为空
     */
    @Test
    void testFindByIndexOrgNumTypeTermPayWayFee_TermNull() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installProductInfoService.findByIndexOrgNumTypeTermPayWayFee("0001", "A", null, "1", "1", "1"));
        assertEquals(AnyTxnParameterRespCodeEnum.D_INSTALL_PRODUCT_INFO_TERM_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试根据机构号、分期类型、期数查询 - 成功场景
     */
    @Test
    void testFindProInfoByTermAndType_Success() {
        // Arrange
        String organizationNumber = "0001";
        String prodType = "A";
        Integer term = 12;
        InstallProductInfo productInfo = createValidInstallProductInfo();
        InstallProductInfoResDTO expectedResult = createValidInstallProductInfoResDTO();

        Mockito.lenient().when(installProductInfoSelfMapper.findProInfoByTermAndType(organizationNumber, prodType, term)).thenReturn(productInfo);
        beanMappingMockedStatic.when(() -> BeanMapping.copy(productInfo, InstallProductInfoResDTO.class)).thenReturn(expectedResult);

        // Act
        InstallProductInfoResDTO result = installProductInfoService.findProInfoByTermAndType(organizationNumber, prodType, term);

        // Assert
        assertNotNull(result);
        assertEquals(expectedResult.getId(), result.getId());
        verify(installProductInfoSelfMapper).findProInfoByTermAndType(organizationNumber, prodType, term);
    }

    /**
     * 测试根据机构号、分期类型、期数查询 - 机构号为空
     */
    @Test
    void testFindProInfoByTermAndType_OrganizationNumberEmpty() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installProductInfoService.findProInfoByTermAndType("", "A", 12));
        assertEquals(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_ORG_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试根据机构号、分期类型、期数查询 - 分期类型为空
     */
    @Test
    void testFindProInfoByTermAndType_ProdTypeEmpty() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installProductInfoService.findProInfoByTermAndType("0001", "", 12));
        assertEquals(AnyTxnParameterRespCodeEnum.D_INSTALL_PRODUCT_INFO_PRODUCT_TYPE_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试根据机构号、分期类型、期数查询 - 期数为空
     */
    @Test
    void testFindProInfoByTermAndType_TermNull() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installProductInfoService.findProInfoByTermAndType("0001", "A", null));
        assertEquals(AnyTxnParameterRespCodeEnum.D_INSTALL_PRODUCT_INFO_TERM_FAULT.getCode(), exception.getErrCode());
    }
}
