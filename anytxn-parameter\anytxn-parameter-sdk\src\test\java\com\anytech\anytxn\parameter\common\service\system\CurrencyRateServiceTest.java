package com.anytech.anytxn.parameter.common.service.system;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.base.common.domain.dto.CurrencyRateReqDTO;
import com.anytech.anytxn.parameter.base.common.domain.dto.CurrencyRateRes;
import com.anytech.anytxn.parameter.base.common.domain.dto.CurrencyRateResDTO;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmCurrencyRate;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmSysDict;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmCurrencyRateHistoryMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmCurrencyRateMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmCurrencyRateSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.system.ParmSysDictSelfMapper;
import com.anytech.anytxn.parameter.common.service.system.CurrencyRateServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Currency rate service test class
 * Test CurrencyRateServiceImpl
 */
@ExtendWith(MockitoExtension.class)
class CurrencyRateServiceTest {

    @Mock
    private ParmCurrencyRateMapper parmCurrencyRateMapper;

    @Mock
    private ParmCurrencyRateSelfMapper parmCurrencyRateSelfMapper;

    @Mock
    private Number16IdGen numberIdGenerator;

    @Mock
    private ParmCurrencyRateHistoryMapper parmCurrencyRateHistoryMapper;

    @Mock
    private ParmSysDictSelfMapper parmSysDictSelfMapper;

    @InjectMocks
    private CurrencyRateServiceImpl currencyRateService;

    private CurrencyRateReqDTO currencyRateReqDTO;
    private ParmCurrencyRate parmCurrencyRate;
    private CurrencyRateResDTO currencyRateResDTO;

    @BeforeEach
    void setUp() {
        // Initialize test data
        currencyRateReqDTO = new CurrencyRateReqDTO();
        currencyRateReqDTO.setId("1234567890123456");
        currencyRateReqDTO.setOrganizationNumber("001");
        currencyRateReqDTO.setRateType("0");
        currencyRateReqDTO.setCurrencySource("USD");
        currencyRateReqDTO.setCurrencyDestination("SGD");
        currencyRateReqDTO.setRateValue(15000L);
        currencyRateReqDTO.setExponent(4);
        currencyRateReqDTO.setDescription("USD to SGD rate");
        currencyRateReqDTO.setStatus("1");
        currencyRateReqDTO.setVersionNumber(1);
        currencyRateReqDTO.setCreateTime(LocalDateTime.now());
        currencyRateReqDTO.setUpdateTime(LocalDateTime.now());

        parmCurrencyRate = new ParmCurrencyRate();
        parmCurrencyRate.setId("1234567890123456");
        parmCurrencyRate.setOrganizationNumber("001");
        parmCurrencyRate.setRateType("0");
        parmCurrencyRate.setCurrencySource("USD");
        parmCurrencyRate.setCurrencyDestination("SGD");
        parmCurrencyRate.setRateValue(15000L);
        parmCurrencyRate.setExponent(4);
        parmCurrencyRate.setDescription("USD to SGD rate");
        parmCurrencyRate.setStatus("1");
        parmCurrencyRate.setVersionNumber(1L);
        parmCurrencyRate.setCreateTime(LocalDateTime.now());
        parmCurrencyRate.setUpdateTime(LocalDateTime.now());

        currencyRateResDTO = new CurrencyRateResDTO();
        currencyRateResDTO.setId("1234567890123456");
        currencyRateResDTO.setOrganizationNumber("001");
        currencyRateResDTO.setRateType("0");
        currencyRateResDTO.setCurrencySource("USD");
        currencyRateResDTO.setCurrencyDestination("SGD");
        currencyRateResDTO.setRateValue(15000L);
        currencyRateResDTO.setExponent(4);
        currencyRateResDTO.setDescription("USD to SGD rate");
        currencyRateResDTO.setStatus("1");
        currencyRateResDTO.setVersionNumber(1);
        currencyRateResDTO.setCreateTime(LocalDateTime.now());
        currencyRateResDTO.setUpdateTime(LocalDateTime.now());
    }

    /**
     * Test find by organization and currency and rate type - success scenario
     * Test CurrencyRateServiceImpl.findByOrgAndCurrencyAndRateType() method
     */
    @Test
    void testFindByOrgAndCurrencyAndRateType_Success() {
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            // Arrange
            Mockito.lenient().when(parmCurrencyRateSelfMapper.selectByOrgAndCurrencyAndRateType(
                    anyString(), anyString(), anyString(), anyString())).thenReturn(parmCurrencyRate);
            beanMappingMock.when(() -> BeanMapping.copy(any(ParmCurrencyRate.class), eq(CurrencyRateResDTO.class)))
                    .thenReturn(currencyRateResDTO);

            // Act
            CurrencyRateResDTO result = currencyRateService.findByOrgAndCurrencyAndRateType("001", "USD", "SGD", "0");

            // Assert
            assertNotNull(result);
            assertEquals("USD", result.getCurrencySource());
            assertEquals("SGD", result.getCurrencyDestination());
            verify(parmCurrencyRateSelfMapper).selectByOrgAndCurrencyAndRateType("001", "USD", "SGD", "0");
        }
    }

    /**
     * Test find by organization and currency and rate type - data not found exception
     * Test CurrencyRateServiceImpl.findByOrgAndCurrencyAndRateType() method
     */
    @Test
    void testFindByOrgAndCurrencyAndRateType_DataNotFound() {
        // Arrange
        Mockito.lenient().when(parmCurrencyRateSelfMapper.selectByOrgAndCurrencyAndRateType(
                anyString(), anyString(), anyString(), anyString())).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> currencyRateService.findByOrgAndCurrencyAndRateType("001", "USD", "SGD", "0"));
        assertEquals(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT, exception.getErrCode());
        verify(parmCurrencyRateSelfMapper).selectByOrgAndCurrencyAndRateType("001", "USD", "SGD", "0");
    }

    /**
     * Test find one by ID - success scenario
     * Test CurrencyRateServiceImpl.findOne() method
     */
    @Test
    void testFindOne_Success() {
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            // Arrange
            Mockito.lenient().when(parmCurrencyRateMapper.selectByPrimaryKey(anyString())).thenReturn(parmCurrencyRate);
            beanMappingMock.when(() -> BeanMapping.copy(any(ParmCurrencyRate.class), eq(CurrencyRateResDTO.class)))
                    .thenReturn(currencyRateResDTO);

            // Act
            CurrencyRateResDTO result = currencyRateService.findOne("1234567890123456");

            // Assert
            assertNotNull(result);
            assertEquals("1234567890123456", result.getId());
            assertEquals("USD", result.getCurrencySource());
            verify(parmCurrencyRateMapper).selectByPrimaryKey("1234567890123456");
        }
    }

    /**
     * Test find one by ID - null parameter exception
     * Test CurrencyRateServiceImpl.findOne() method
     */
    @Test
    void testFindOne_NullParameter() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> currencyRateService.findOne(null));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT, exception.getErrCode());
    }

    /**
     * Test find one by ID - data not found exception
     * Test CurrencyRateServiceImpl.findOne() method
     */
    @Test
    void testFindOne_DataNotFound() {
        // Arrange
        Mockito.lenient().when(parmCurrencyRateMapper.selectByPrimaryKey(anyString())).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> currencyRateService.findOne("nonexistent"));
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_CURRENCY_RATE_FAULT, exception.getErrCode());
        verify(parmCurrencyRateMapper).selectByPrimaryKey("nonexistent");
    }

    /**
     * Test find page - success scenario
     * Test CurrencyRateServiceImpl.findPage() method
     */
    @Test
    void testFindPage_Success() {
        try (MockedStatic<PageHelper> pageHelperMock = mockStatic(PageHelper.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {

            // Arrange
            Page<ParmCurrencyRate> page = new Page<>(1, 10);
            page.setTotal(1);
            page.setPages(1);
            List<ParmCurrencyRate> currencyRateList = new ArrayList<>();
            currencyRateList.add(parmCurrencyRate);
            List<CurrencyRateResDTO> resDTOList = new ArrayList<>();
            resDTOList.add(currencyRateResDTO);

            pageHelperMock.when(() -> PageHelper.startPage(anyInt(), anyInt())).thenReturn(page);
            orgUtilsMock.when(() -> OrgNumberUtils.getOrg()).thenReturn("001");
            Mockito.lenient().when(parmCurrencyRateSelfMapper.selectByCondition(any(Map.class))).thenReturn(currencyRateList);
            beanMappingMock.when(() -> BeanMapping.copyList(any(List.class), eq(CurrencyRateResDTO.class)))
                    .thenReturn(resDTOList);

            // Act
            PageResultDTO<CurrencyRateResDTO> result = currencyRateService.findPage(1, 10, "USD", "SGD", "Test", "15000", "001");

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getPage());
            assertEquals(10, result.getRows());
            assertEquals(1, result.getTotal());
            assertEquals(1, result.getData().size());
            verify(parmCurrencyRateSelfMapper).selectByCondition(any(Map.class));
        }
    }

    /**
     * Test add currency rate - success scenario
     * Test CurrencyRateServiceImpl.add() method
     */
    @Test
    void testAdd_Success() {
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class)) {

            // Arrange
            Mockito.lenient().when(parmCurrencyRateSelfMapper.isExists(anyString(), anyString(), anyString(), anyString())).thenReturn(0);
            Mockito.lenient().when(numberIdGenerator.generateId(anyString())).thenReturn(1234567890123456L);
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("001");
            beanMappingMock.when(() -> BeanMapping.copy(any(CurrencyRateReqDTO.class), eq(ParmCurrencyRate.class)))
                    .thenReturn(parmCurrencyRate);

            // Act
            ParameterCompare result = currencyRateService.add(currencyRateReqDTO);

            // Assert
            assertNotNull(result);
            assertEquals("USD-SGD", result.getMainParmId());
            verify(parmCurrencyRateSelfMapper).isExists("001", "USD", "SGD", "0");
            verify(numberIdGenerator).generateId("001");
        }
    }

    /**
     * Test add currency rate - currency rate already exists exception
     * Test CurrencyRateServiceImpl.add() method
     */
    @Test
    void testAdd_CurrencyRateExists() {
        // Arrange
        Mockito.lenient().when(parmCurrencyRateSelfMapper.isExists(anyString(), anyString(), anyString(), anyString())).thenReturn(1);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> currencyRateService.add(currencyRateReqDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.D_EXIST_PARM_CURRENCY_RATE_FAULT, exception.getErrCode());
        verify(parmCurrencyRateSelfMapper).isExists("001", "USD", "SGD", "0");
    }

    /**
     * Test modify currency rate - success scenario
     * Test CurrencyRateServiceImpl.modify() method
     */
    @Test
    void testModify_Success() {
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            // Arrange
            currencyRateReqDTO.setId("1234567890123456");
            Mockito.lenient().when(parmCurrencyRateMapper.selectByPrimaryKey(anyString())).thenReturn(parmCurrencyRate);
            Mockito.lenient().when(parmCurrencyRateSelfMapper.selectByOrgAndCurrencyAndRateType(
                    anyString(), anyString(), anyString(), anyString())).thenReturn(null);
            beanMappingMock.when(() -> BeanMapping.copy(any(CurrencyRateReqDTO.class), eq(ParmCurrencyRate.class)))
                    .thenReturn(parmCurrencyRate);

            // Act
            ParameterCompare result = currencyRateService.modify(currencyRateReqDTO);

            // Assert
            assertNotNull(result);
            assertNotNull(result.getBefore());
            assertNotNull(result.getAfter());
            verify(parmCurrencyRateMapper).selectByPrimaryKey("1234567890123456");
        }
    }

    /**
     * Test modify currency rate - null ID exception
     * Test CurrencyRateServiceImpl.modify() method
     */
    @Test
    void testModify_NullId() {
        // Arrange
        currencyRateReqDTO.setId(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> currencyRateService.modify(currencyRateReqDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT, exception.getErrCode());
    }

    /**
     * Test remove currency rate - success scenario
     * Test CurrencyRateServiceImpl.remove() method
     */
    @Test
    void testRemove_Success() {
        // Arrange
        Mockito.lenient().when(parmCurrencyRateMapper.selectByPrimaryKey(anyString())).thenReturn(parmCurrencyRate);

        // Act
        ParameterCompare result = currencyRateService.remove("1234567890123456");

        // Assert
        assertNotNull(result);
        assertNotNull(result.getBefore());
        verify(parmCurrencyRateMapper).selectByPrimaryKey("1234567890123456");
    }

    /**
     * Test remove currency rate - data not found exception
     * Test CurrencyRateServiceImpl.remove() method
     */
    @Test
    void testRemove_DataNotFound() {
        // Arrange
        Mockito.lenient().when(parmCurrencyRateMapper.selectByPrimaryKey(anyString())).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> currencyRateService.remove("nonexistent"));
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_CURRENCY_RATE_FAULT, exception.getErrCode());
        verify(parmCurrencyRateMapper).selectByPrimaryKey("nonexistent");
    }

    /**
     * Test find exchange rate by currency - success scenario
     * Test CurrencyRateServiceImpl.findExchangeRateByCurrency() method
     */
    @Test
    void testFindExchangeRateByCurrency_Success() {
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {

            // Arrange
            CurrencyRateRes currencyRateRes = new CurrencyRateRes();
            currencyRateRes.setCurrencySource("USD");
            currencyRateRes.setCurrencyDestination("SGD");
            currencyRateRes.setRateValue(15000L);

            orgUtilsMock.when(() -> OrgNumberUtils.getOrg()).thenReturn("001");
            Mockito.lenient().when(parmCurrencyRateSelfMapper.selectByOrgAndCurrencyAndRateType(
                    anyString(), anyString(), anyString(), anyString())).thenReturn(parmCurrencyRate);
            beanMappingMock.when(() -> BeanMapping.copy(any(ParmCurrencyRate.class), eq(CurrencyRateRes.class)))
                    .thenReturn(currencyRateRes);

            // Act
            CurrencyRateRes result = currencyRateService.findExchangeRateByCurrency("USD", "SGD", "B");

            // Assert
            assertNotNull(result);
            assertEquals("USD", result.getCurrencySource());
            assertEquals("SGD", result.getCurrencyDestination());
            assertEquals(15000L, result.getRateValue());
            verify(parmCurrencyRateSelfMapper).selectByOrgAndCurrencyAndRateType("001", "USD", "SGD", "0");
        }
    }

    /**
     * Test find exchange rate by currency - invalid source currency exception
     * Test CurrencyRateServiceImpl.findExchangeRateByCurrency() method
     */
    @Test
    void testFindExchangeRateByCurrency_InvalidSourceCurrency() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> currencyRateService.findExchangeRateByCurrency("US", "SGD", "B"));
        assertEquals(AnyTxnParameterRespCodeEnum.S_PARAM_CURRENCY_RATE_ERROR, exception.getErrCode());
    }

    /**
     * Test find exchange rate by currency - data not found exception
     * Test CurrencyRateServiceImpl.findExchangeRateByCurrency() method
     */
    @Test
    void testFindExchangeRateByCurrency_DataNotFound() {
        try (MockedStatic<OrgNumberUtils> orgUtilsMock = mockStatic(OrgNumberUtils.class)) {
            // Arrange
            orgUtilsMock.when(() -> OrgNumberUtils.getOrg()).thenReturn("001");
            Mockito.lenient().when(parmCurrencyRateSelfMapper.selectByOrgAndCurrencyAndRateType(
                    anyString(), anyString(), anyString(), anyString())).thenReturn(null);

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                    () -> currencyRateService.findExchangeRateByCurrency("USD", "EUR", "B"));
            assertEquals(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT, exception.getErrCode());
            verify(parmCurrencyRateSelfMapper).selectByOrgAndCurrencyAndRateType("001", "USD", "EUR", "0");
        }
    }

    /**
     * Test database update method
     * Test CurrencyRateServiceImpl.updateDb() method
     */
    @Test
    void testUpdateDb_Success() {
        try (MockedStatic<JSON> jsonMock = mockStatic(JSON.class)) {
            // Arrange
            ParmModificationRecord record = new ParmModificationRecord();
            record.setParmBody("{\"id\":\"123\",\"currencySource\":\"USD\"}");

            jsonMock.when(() -> JSON.parseObject(anyString(), eq(ParmCurrencyRate.class)))
                    .thenReturn(parmCurrencyRate);
            Mockito.lenient().when(parmCurrencyRateMapper.updateByPrimaryKeySelective(any(ParmCurrencyRate.class))).thenReturn(1);


            verify(parmCurrencyRateMapper).updateByPrimaryKeySelective(any(ParmCurrencyRate.class));
        }
    }

    /**
     * Test database insert method
     * Test CurrencyRateServiceImpl.insertDb() method
     */
    @Test
    void testInsertDb_Success() {
        try (MockedStatic<JSON> jsonMock = mockStatic(JSON.class)) {
            // Arrange
            ParmModificationRecord record = new ParmModificationRecord();
            record.setParmBody("{\"id\":\"123\",\"currencySource\":\"USD\"}");

            jsonMock.when(() -> JSON.parseObject(anyString(), eq(ParmCurrencyRate.class)))
                    .thenReturn(parmCurrencyRate);
            Mockito.lenient().when(parmCurrencyRateMapper.insertSelective(any(ParmCurrencyRate.class))).thenReturn(1);

            verify(parmCurrencyRateMapper).insertSelective(any(ParmCurrencyRate.class));
        }
    }

    /**
     * Test database delete method
     * Test CurrencyRateServiceImpl.deleteDb() method
     */
    @Test
    void testDeleteDb_Success() {
        try (MockedStatic<JSON> jsonMock = mockStatic(JSON.class)) {
            // Arrange
            ParmModificationRecord record = new ParmModificationRecord();
            record.setParmBody("{\"id\":\"123\",\"currencySource\":\"USD\"}");

            jsonMock.when(() -> JSON.parseObject(anyString(), eq(ParmCurrencyRate.class)))
                    .thenReturn(parmCurrencyRate);
            Mockito.lenient().when(parmCurrencyRateMapper.deleteByPrimaryKey(anyString())).thenReturn(1);


            verify(parmCurrencyRateMapper).deleteByPrimaryKey(anyString());
        }
    }
}