package com.anytech.anytxn.parameter.installment.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.utils.BeanMapping;

import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallmentInterestInfoDTO;
import com.anytech.anytxn.parameter.base.installment.domain.model.ParmInstallInterestInfo;
import com.anytech.anytxn.parameter.installment.mapper.ParmInstallInterestInfoMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * InstallmentInterestInfoServiceImpl单元测试类
 * 
 * <AUTHOR>
 * @date 2022/11/9
 */
@ExtendWith(MockitoExtension.class)
class InstallmentInterestInfoServiceTest {

    @Mock
    private ParmInstallInterestInfoMapper installInterestInfoMapper;

    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private InstallmentInterestInfoServiceImpl installmentInterestInfoService;

    private MockedStatic<PageHelper> pageHelperMockedStatic;
    private MockedStatic<BeanMapping> beanMappingMockedStatic;
    private MockedStatic<TenantUtils> tenantUtilsMockedStatic;
    private MockedStatic<JSON> jsonMockedStatic;

    private InstallmentInterestInfoDTO testDTO;
    private ParmInstallInterestInfo testEntity;
    private List<ParmInstallInterestInfo> testEntityList;
    private Page testPage;

    @BeforeEach
    void setUp() {
        // 初始化静态Mock
        pageHelperMockedStatic = mockStatic(PageHelper.class);
        beanMappingMockedStatic = mockStatic(BeanMapping.class);
        tenantUtilsMockedStatic = mockStatic(TenantUtils.class);
        jsonMockedStatic = mockStatic(JSON.class);

        // 设置通用Mock行为
        setupCommonMocks();

        // 初始化测试数据
        setupTestData();
    }

    private void setupTestData() {
        // 创建测试DTO
        testDTO = new InstallmentInterestInfoDTO();
        testDTO.setId("TEST_ID_001");
        testDTO.setOrganizationNumber("ORG001");
        testDTO.setInterestTableId("INTEREST_TABLE_001");
        testDTO.setInterestCalculationMethod("SIMPLE");
        testDTO.setInterestCalculationFlag("Y");
        testDTO.setAnnualInterestRate(new BigDecimal("12.50"));
        testDTO.setInterestPaymentMethod(new BigDecimal("1"));
        testDTO.setInterestDesc("Test Interest Description");
        testDTO.setStatus("1");
        testDTO.setUpdateBy("TEST_USER");
        testDTO.setCreateTime(LocalDateTime.now());
        testDTO.setUpdateTime(LocalDateTime.now());
        testDTO.setVersionNumber(1L);

        // 创建测试Entity
        testEntity = new ParmInstallInterestInfo();
        testEntity.setId("TEST_ID_001");
        testEntity.setOrganizationNumber("ORG001");
        testEntity.setInterestTableId("INTEREST_TABLE_001");
        testEntity.setInterestCalculationMethod("SIMPLE");
        testEntity.setInterestCalculationFlag("Y");
        testEntity.setAnnualInterestRate(new BigDecimal("12.50"));
        testEntity.setInterestPaymentMethod(new BigDecimal("1"));
        testEntity.setInterestDesc("Test Interest Description");
        testEntity.setStatus("1");
        testEntity.setUpdateBy("TEST_USER");
        testEntity.setCreateTime(LocalDateTime.now());
        testEntity.setUpdateTime(LocalDateTime.now());
        testEntity.setVersionNumber(1L);

        // 创建测试Entity列表
        testEntityList = Arrays.asList(testEntity);

        // 创建测试分页对象
        testPage = new Page(1, 10);
        testPage.setTotal(1L);
        testPage.setPages(1);
    }

    private void setupCommonMocks() {
        // Mock Number16IdGen
        Mockito.lenient().when(numberIdGenerator.generateId(anyString())).thenReturn(123456789L);

        // Mock TenantUtils
        tenantUtilsMockedStatic.when(() -> TenantUtils.getTenantId()).thenReturn("TENANT_001");
    }

    /**
     * 测试getPage方法 - 正常情况
     * 验证分页查询功能正常工作
     */
    @Test
    void testGetPage_Success() {
        // Arrange
        Integer pageNum = 1;
        Integer pageSize = 10;
        InstallmentInterestInfoDTO queryDTO = new InstallmentInterestInfoDTO();
        queryDTO.setOrganizationNumber("ORG001");

        // Mock PageHelper
        pageHelperMockedStatic.when(() -> PageHelper.startPage(pageNum, pageSize)).thenReturn(testPage);

        // Mock BeanMapping
        beanMappingMockedStatic.when(() -> BeanMapping.copyList(testEntityList, InstallmentInterestInfoDTO.class))
                .thenReturn(Arrays.asList(testDTO));

        Mockito.lenient().when(installInterestInfoMapper.selectByCondition(any(InstallmentInterestInfoDTO.class)))
                .thenReturn(testEntityList);

        // Act
        PageResultDTO<InstallmentInterestInfoDTO> result = installmentInterestInfoService.getPage(pageNum, pageSize, queryDTO);

        // Assert
        assertNotNull(result);
        assertEquals(pageNum, result.getPage());
        assertEquals(pageSize, result.getRows());
        assertEquals(1L, result.getTotal());
        assertEquals(1L, result.getTotalPage());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().size());

        // Verify interactions
        verify(installInterestInfoMapper).selectByCondition(any(InstallmentInterestInfoDTO.class));
    }

    /**
     * 测试getPage方法 - 查询条件为null时创建新对象
     * 验证当查询条件的organizationNumber为null时，会创建新的DTO对象
     */
    @Test
    void testGetPage_NullOrganizationNumber() {
        // Arrange
        Integer pageNum = 1;
        Integer pageSize = 10;
        InstallmentInterestInfoDTO queryDTO = new InstallmentInterestInfoDTO();
        queryDTO.setOrganizationNumber(null); // organizationNumber为null

        Mockito.lenient().when(installInterestInfoMapper.selectByCondition(any(InstallmentInterestInfoDTO.class)))
                .thenReturn(testEntityList);

        // Act
        PageResultDTO<InstallmentInterestInfoDTO> result = installmentInterestInfoService.getPage(pageNum, pageSize, queryDTO);

        // Assert
        assertNotNull(result);
        assertEquals(pageNum, result.getPage());
        assertEquals(pageSize, result.getRows());
        assertEquals(1L, result.getTotal());
        assertEquals(1L, result.getTotalPage());
        assertNotNull(result.getData());
        assertEquals(1, result.getData().size());

        // Verify interactions
        verify(installInterestInfoMapper).selectByCondition(any(InstallmentInterestInfoDTO.class));
    }

    /**
     * 测试getPage方法 - 空结果
     * 验证查询结果为空时的处理
     */
    @Test
    void testGetPage_EmptyResult() {
        // Arrange
        Integer pageNum = 1;
        Integer pageSize = 10;
        InstallmentInterestInfoDTO queryDTO = new InstallmentInterestInfoDTO();
        queryDTO.setOrganizationNumber("ORG001");

        Mockito.lenient().when(installInterestInfoMapper.selectByCondition(any(InstallmentInterestInfoDTO.class)))
                .thenReturn(Collections.emptyList());
        Mockito.lenient().when(BeanMapping.copyList(eq(Collections.emptyList()), eq(InstallmentInterestInfoDTO.class)))
                .thenReturn(Collections.emptyList());

        // Act
        PageResultDTO<InstallmentInterestInfoDTO> result = installmentInterestInfoService.getPage(pageNum, pageSize, queryDTO);

        // Assert
        assertNotNull(result);
        assertEquals(pageNum, result.getPage());
        assertEquals(pageSize, result.getRows());
        assertEquals(1L, result.getTotal());
        assertEquals(1L, result.getTotalPage());
        assertNotNull(result.getData());
        assertEquals(0, result.getData().size());

        // Verify interactions
        verify(installInterestInfoMapper).selectByCondition(any(InstallmentInterestInfoDTO.class));
    }

    /**
     * 测试insert方法 - 正常情况
     * 验证插入功能正常工作
     */
    @Test
    void testInsert_Success() {
        // Arrange
        InstallmentInterestInfoDTO insertDTO = createValidDTO();

        // Act
        ParameterCompare result = installmentInterestInfoService.insert(insertDTO);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getAfter());
        assertNull(result.getBefore());
        assertEquals(ParmInstallInterestInfo.class, result.getClazz());

        // Verify interactions
        verify(numberIdGenerator).generateId(anyString());
    }

    private InstallmentInterestInfoDTO createValidDTO() {
        InstallmentInterestInfoDTO dto = new InstallmentInterestInfoDTO();
        dto.setInterestTableId("INTEREST_TABLE_001");
        dto.setInterestCalculationMethod("SIMPLE");
        dto.setInterestCalculationFlag("Y");
        dto.setAnnualInterestRate(new BigDecimal("12.50"));
        dto.setInterestPaymentMethod(new BigDecimal("1"));
        return dto;
    }

    /**
     * 测试insert方法 - 必填字段为空异常
     * 验证当interestCalculationFlag为空时抛出异常
     */
    @Test
    void testInsert_InterestCalculationFlagEmpty() {
        // Arrange
        InstallmentInterestInfoDTO insertDTO = createValidDTO();
        insertDTO.setInterestCalculationFlag(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installmentInterestInfoService.insert(insertDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试insert方法 - interestCalculationMethod为空异常
     * 验证当interestCalculationMethod为空时抛出异常
     */
    @Test
    void testInsert_InterestCalculationMethodEmpty() {
        // Arrange
        InstallmentInterestInfoDTO insertDTO = createValidDTO();
        insertDTO.setInterestCalculationMethod("");

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installmentInterestInfoService.insert(insertDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试insert方法 - interestPaymentMethod为空异常
     * 验证当interestPaymentMethod为null时抛出异常
     */
    @Test
    void testInsert_InterestPaymentMethodNull() {
        // Arrange
        InstallmentInterestInfoDTO insertDTO = createValidDTO();
        insertDTO.setInterestPaymentMethod(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installmentInterestInfoService.insert(insertDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试insert方法 - interestTableId为空异常
     * 验证当interestTableId为空时抛出异常
     */
    @Test
    void testInsert_InterestTableIdEmpty() {
        // Arrange
        InstallmentInterestInfoDTO insertDTO = createValidDTO();
        insertDTO.setInterestTableId("");

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installmentInterestInfoService.insert(insertDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试insert方法 - annualInterestRate为空异常
     * 验证当annualInterestRate为null时抛出异常
     */
    @Test
    void testInsert_AnnualInterestRateNull() {
        // Arrange
        InstallmentInterestInfoDTO insertDTO = createValidDTO();
        insertDTO.setAnnualInterestRate(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installmentInterestInfoService.insert(insertDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试updateInstallmentInterestInfo方法 - 正常情况
     * 验证更新功能正常工作
     */
    @Test
    void testUpdateInstallmentInterestInfo_Success() {
        // Arrange
        InstallmentInterestInfoDTO updateDTO = createValidDTO();
        updateDTO.setId("TEST_ID_001");

        Mockito.lenient().when(installInterestInfoMapper.selectByPrimaryKey(anyString()))
                .thenReturn(testEntity);

        // Act
        ParameterCompare result = installmentInterestInfoService.updateInstallmentInterestInfo(updateDTO);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getBefore());
        assertNotNull(result.getAfter());
        assertEquals(InstallmentInterestInfoDTO.class, result.getClazz());

        // Verify interactions
        verify(installInterestInfoMapper).selectByPrimaryKey(updateDTO.getId());
    }

    /**
     * 测试updateInstallmentInterestInfo方法 - 数据不存在异常
     * 验证当要更新的数据不存在时抛出异常
     */
    @Test
    void testUpdateInstallmentInterestInfo_DataNotExists() {
        // Arrange
        InstallmentInterestInfoDTO updateDTO = createValidDTO();
        updateDTO.setId("NON_EXISTENT_ID");

        Mockito.lenient().when(installInterestInfoMapper.selectByPrimaryKey(anyString()))
                .thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installmentInterestInfoService.updateInstallmentInterestInfo(updateDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT.getCode(), exception.getErrCode());

        // Verify interactions
        verify(installInterestInfoMapper).selectByPrimaryKey(updateDTO.getId());
    }

    /**
     * 测试updateInstallmentInterestInfo方法 - 必填字段验证
     * 验证更新时必填字段验证正常工作
     */
    @Test
    void testUpdateInstallmentInterestInfo_RequiredFieldValidation() {
        // Arrange
        InstallmentInterestInfoDTO updateDTO = createValidDTO();
        updateDTO.setId("TEST_ID_001");
        updateDTO.setInterestCalculationFlag(null); // 设置必填字段为空

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installmentInterestInfoService.updateInstallmentInterestInfo(updateDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试deleteInstallmentInterestInfo方法 - 正常情况
     * 验证删除功能正常工作
     */
    @Test
    void testDeleteInstallmentInterestInfo_Success() {
        // Arrange
        String id = "TEST_ID_001";

        Mockito.lenient().when(installInterestInfoMapper.selectByPrimaryKey(anyString()))
                .thenReturn(testEntity);

        // Act
        ParameterCompare result = installmentInterestInfoService.deleteInstallmentInterestInfo(id);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getBefore());
        assertNull(result.getAfter());
        assertEquals(ParmInstallInterestInfo.class, result.getClazz());

        // Verify interactions
        verify(installInterestInfoMapper).selectByPrimaryKey(id);
    }

    /**
     * 测试deleteInstallmentInterestInfo方法 - ID为空异常
     * 验证当ID为空时抛出异常
     */
    @Test
    void testDeleteInstallmentInterestInfo_IdEmpty() {
        // Arrange
        String id = "";

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installmentInterestInfoService.deleteInstallmentInterestInfo(id));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试deleteInstallmentInterestInfo方法 - ID为null异常
     * 验证当ID为null时抛出异常
     */
    @Test
    void testDeleteInstallmentInterestInfo_IdNull() {
        // Arrange
        String id = null;

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installmentInterestInfoService.deleteInstallmentInterestInfo(id));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试deleteInstallmentInterestInfo方法 - 数据不存在异常
     * 验证当要删除的数据不存在时抛出异常
     */
    @Test
    void testDeleteInstallmentInterestInfo_DataNotExists() {
        // Arrange
        String id = "NON_EXISTENT_ID";

        Mockito.lenient().when(installInterestInfoMapper.selectByPrimaryKey(anyString()))
                .thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installmentInterestInfoService.deleteInstallmentInterestInfo(id));
        assertEquals(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT.getCode(), exception.getErrCode());

        // Verify interactions
        verify(installInterestInfoMapper).selectByPrimaryKey(id);
    }

    /**
     * 测试getInstallmentInterestInfo方法 - 正常情况
     * 验证根据ID查询功能正常工作
     */
    @Test
    void testGetInstallmentInterestInfo_Success() {
        // Arrange
        String id = "TEST_ID_001";

        Mockito.lenient().when(installInterestInfoMapper.selectByPrimaryKey(anyString()))
                .thenReturn(testEntity);

        // Act
        InstallmentInterestInfoDTO result = installmentInterestInfoService.getInstallmentInterestInfo(id);

        // Assert
        assertNotNull(result);
        assertEquals(testDTO.getId(), result.getId());
        assertEquals(testDTO.getInterestTableId(), result.getInterestTableId());
        assertEquals(testDTO.getInterestCalculationMethod(), result.getInterestCalculationMethod());

        // Verify interactions
        verify(installInterestInfoMapper).selectByPrimaryKey(id);
    }

    /**
     * 测试getInstallmentInterestInfo方法 - 数据不存在
     * 验证当查询的数据不存在时返回null
     */
    @Test
    void testGetInstallmentInterestInfo_DataNotExists() {
        // Arrange
        String id = "NON_EXISTENT_ID";

        Mockito.lenient().when(installInterestInfoMapper.selectByPrimaryKey(anyString()))
                .thenReturn(null);

        // Act
        InstallmentInterestInfoDTO result = installmentInterestInfoService.getInstallmentInterestInfo(id);

        // Assert
        assertNull(result);

        // Verify interactions
        verify(installInterestInfoMapper).selectByPrimaryKey(id);
    }

    /**
     * 测试updateDb方法 - 正常情况
     * 通过反射测试protected方法updateDb
     */
    @Test
    void testUpdateDb_Success() throws Exception {
        // Arrange
        ParmModificationRecord record = new ParmModificationRecord();
        record.setParmBody(JSON.toJSONString(testEntity));
        record.setApplicationBy("TEST_USER");

        Mockito.lenient().when(installInterestInfoMapper.updateSelective(any(ParmInstallInterestInfo.class)))
                .thenReturn(1);

        // Act
        java.lang.reflect.Method updateDbMethod = InstallmentInterestInfoServiceImpl.class
                .getDeclaredMethod("updateDb", ParmModificationRecord.class);
        updateDbMethod.setAccessible(true);
        Boolean result = (Boolean) updateDbMethod.invoke(installmentInterestInfoService, record);

        // Assert
        assertTrue(result);

        // Verify interactions
        verify(installInterestInfoMapper).updateSelective(any(ParmInstallInterestInfo.class));
    }

    /**
     * 测试updateDb方法 - 更新失败
     * 验证当数据库更新返回0时返回false
     */
    @Test
    void testUpdateDb_Failed() throws Exception {
        // Arrange
        ParmModificationRecord record = new ParmModificationRecord();
        record.setParmBody(JSON.toJSONString(testEntity));
        record.setApplicationBy("TEST_USER");

        Mockito.lenient().when(installInterestInfoMapper.updateSelective(any(ParmInstallInterestInfo.class)))
                .thenReturn(0);

        // Act
        java.lang.reflect.Method updateDbMethod = InstallmentInterestInfoServiceImpl.class
                .getDeclaredMethod("updateDb", ParmModificationRecord.class);
        updateDbMethod.setAccessible(true);
        Boolean result = (Boolean) updateDbMethod.invoke(installmentInterestInfoService, record);

        // Assert
        assertFalse(result);

        // Verify interactions
        verify(installInterestInfoMapper).updateSelective(any(ParmInstallInterestInfo.class));
    }

    /**
     * 测试insertDb方法 - 正常情况
     * 通过反射测试protected方法insertDb
     */
    @Test
    void testInsertDb_Success() throws Exception {
        // Arrange
        ParmModificationRecord record = new ParmModificationRecord();
        record.setParmBody(JSON.toJSONString(testEntity));
        record.setApplicationBy("TEST_USER");

        Mockito.lenient().when(installInterestInfoMapper.insert(any(ParmInstallInterestInfo.class)))
                .thenReturn(1);

        // Act
        java.lang.reflect.Method insertDbMethod = InstallmentInterestInfoServiceImpl.class
                .getDeclaredMethod("insertDb", ParmModificationRecord.class);
        insertDbMethod.setAccessible(true);
        Boolean result = (Boolean) insertDbMethod.invoke(installmentInterestInfoService, record);

        // Assert
        assertTrue(result);

        // Verify interactions
        verify(installInterestInfoMapper).insert(any(ParmInstallInterestInfo.class));
    }

    /**
     * 测试insertDb方法 - 插入失败
     * 验证当数据库插入返回0时返回false
     */
    @Test
    void testInsertDb_Failed() throws Exception {
        // Arrange
        ParmModificationRecord record = new ParmModificationRecord();
        record.setParmBody(JSON.toJSONString(testEntity));
        record.setApplicationBy("TEST_USER");

        Mockito.lenient().when(installInterestInfoMapper.insert(any(ParmInstallInterestInfo.class)))
                .thenReturn(0);

        // Act
        java.lang.reflect.Method insertDbMethod = InstallmentInterestInfoServiceImpl.class
                .getDeclaredMethod("insertDb", ParmModificationRecord.class);
        insertDbMethod.setAccessible(true);
        Boolean result = (Boolean) insertDbMethod.invoke(installmentInterestInfoService, record);

        // Assert
        assertFalse(result);

        // Verify interactions
        verify(installInterestInfoMapper).insert(any(ParmInstallInterestInfo.class));
    }

    /**
     * 清理资源
     */
    @org.junit.jupiter.api.AfterEach
    void tearDown() {
        if (pageHelperMockedStatic != null) {
            pageHelperMockedStatic.close();
        }
        if (beanMappingMockedStatic != null) {
            beanMappingMockedStatic.close();
        }
        if (tenantUtilsMockedStatic != null) {
            tenantUtilsMockedStatic.close();
        }
        if (jsonMockedStatic != null) {
            jsonMockedStatic.close();
        }
    }
}
