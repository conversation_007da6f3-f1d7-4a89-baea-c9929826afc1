package com.anytech.anytxn.parameter.account.service;

import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.account.mapper.ParmAcctPmtAllocBasicColMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmAcctPmtAllocBasicDefMapper;
import com.anytech.anytxn.parameter.account.service.ParmAcctPmtAllocBasicDefServiceImpl;
import com.anytech.anytxn.parameter.base.account.domain.dto.AcctPmtBasicColReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.AcctPmtBasicColResDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.AcctPmtBasicInfoReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.AcctPmtBasicInfoResDTO;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmAcctPmtAllocBasicCol;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmAcctPmtAllocBasicDef;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * ParmAcctPmtAllocBasicDefServiceImpl 单元测试类
 * 测试账户间还款分配基本参数定义服务的所有业务方法
 *
 * <AUTHOR> Assistant
 * @date 2025-06-27
 * @version 1.0
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class ParmAcctPmtAllocBasicDefServiceTest {

    @InjectMocks
    private ParmAcctPmtAllocBasicDefServiceImpl parmAcctPmtAllocBasicDefService;

    @Mock
    private ParmAcctPmtAllocBasicDefMapper parmAcctPmtAllocBasicDefMapper;

    @Mock
    private ParmAcctPmtAllocBasicColMapper parmAcctPmtAllocBasicColMapper;

    @Mock
    private Number16IdGen numberIdGenerator;

    private ParmAcctPmtAllocBasicDef testParmAcctPmtAllocBasicDef;
    private AcctPmtBasicInfoReqDTO testReqDTO;
    private List<AcctPmtBasicColReqDTO> testAcctPmtBasicColReqDTOList;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testParmAcctPmtAllocBasicDef = new ParmAcctPmtAllocBasicDef();
        testParmAcctPmtAllocBasicDef.setId("TEST_ID_001");
        testParmAcctPmtAllocBasicDef.setTableId("TEST_TABLE_001");
        testParmAcctPmtAllocBasicDef.setOrganizationNumber("ORG001");
        testParmAcctPmtAllocBasicDef.setDescription("测试账户间还款分配");
        testParmAcctPmtAllocBasicDef.setOverpayCollectMethod("0");
        testParmAcctPmtAllocBasicDef.setPersonalTableId("PERSONAL_001");
        testParmAcctPmtAllocBasicDef.setStatus("1");

        // 使用try-with-resources确保OrgNumberUtils静态Mock生效
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            // 创建控制信息列表
            testAcctPmtBasicColReqDTOList = new ArrayList<>();
            AcctPmtBasicColReqDTO colReqDTO = new AcctPmtBasicColReqDTO();
            colReqDTO.setAccountProductNumber("PROD001");
            colReqDTO.setAllocationSequenceDefault(1L);
            colReqDTO.setStatus("1");
            testAcctPmtBasicColReqDTOList.add(colReqDTO);
            
            testReqDTO = new AcctPmtBasicInfoReqDTO();
            testReqDTO.setTableId("TEST_TABLE_001");
            testReqDTO.setOrganizationNumber("ORG001");
            testReqDTO.setDescription("测试账户间还款分配");
            testReqDTO.setOverpayCollectMethod("0");
            testReqDTO.setPersonalTableId("PERSONAL_001");
            testReqDTO.setStatus("1");
            testReqDTO.setAcctPmtBasicColResDTOList(testAcctPmtBasicColReqDTOList);
        }
    }

    /**
     * 测试方法：testAdd_Success
     * 用来测试 ParmAcctPmtAllocBasicDefServiceImpl 方法 add
     * 验证成功添加账户间还款分配基本参数定义的场景
     */
    @Test
    void testAdd_Success() {
        // Arrange
        when(parmAcctPmtAllocBasicDefMapper.isExists("ORG001", "TEST_TABLE_001")).thenReturn(0);
        when(numberIdGenerator.generateId(anyString())).thenReturn(123456789L);

        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class)) {
            
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("TENANT001");

            // Act
            ParameterCompare result = parmAcctPmtAllocBasicDefService.add(testReqDTO);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getMainParmId()).isEqualTo("TEST_TABLE_001");
            assertThat(result.getAfter()).isNotNull();

            verify(parmAcctPmtAllocBasicDefMapper).isExists("ORG001", "TEST_TABLE_001");
            verify(numberIdGenerator).generateId(anyString());
        }
    }

    /**
     * 测试方法：testAdd_AlreadyExists
     * 用来测试 ParmAcctPmtAllocBasicDefServiceImpl 方法 add
     * 验证当参数表ID已存在时抛出异常的场景
     */
    @Test
    void testAdd_AlreadyExists() {
        // Arrange
        when(parmAcctPmtAllocBasicDefMapper.isExists("ORG001", "TEST_TABLE_001")).thenReturn(1);

        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("ORG001");

            // Assert
            assertThatThrownBy(() -> parmAcctPmtAllocBasicDefService.add(testReqDTO))
                    .isInstanceOf(AnyTxnParameterException.class);

            verify(parmAcctPmtAllocBasicDefMapper).isExists("ORG001", "TEST_TABLE_001");
        }
    }

    /**
     * 测试方法：testModify_Success
     * 用来测试 ParmAcctPmtAllocBasicDefServiceImpl 方法 modify
     * 验证成功修改账户间还款分配基本参数定义的场景
     */
    @Test
    void testModify_Success() {
        // Arrange
        testReqDTO.setId("TEST_ID_001");
        when(parmAcctPmtAllocBasicDefMapper.selectByPrimaryKey("TEST_ID_001"))
                .thenReturn(testParmAcctPmtAllocBasicDef);

        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("ORG001");

            // Act
            ParameterCompare result = parmAcctPmtAllocBasicDefService.modify(testReqDTO);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getMainParmId()).isEqualTo("TEST_TABLE_001");

            verify(parmAcctPmtAllocBasicDefMapper).selectByPrimaryKey("TEST_ID_001");
        }
    }

    /**
     * 测试方法：testModify_NotFound
     * 用来测试 ParmAcctPmtAllocBasicDefServiceImpl 方法 modify
     * 验证当记录不存在时抛出异常的场景
     */
    @Test
    void testModify_NotFound() {
        // Arrange
        testReqDTO.setId("TEST_ID_001");
        when(parmAcctPmtAllocBasicDefMapper.selectByPrimaryKey("TEST_ID_001"))
                .thenReturn(null);

        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("ORG001");

            // Assert
            assertThatThrownBy(() -> parmAcctPmtAllocBasicDefService.modify(testReqDTO))
                    .isInstanceOf(AnyTxnParameterException.class);

            verify(parmAcctPmtAllocBasicDefMapper).selectByPrimaryKey("TEST_ID_001");
        }
    }

    /**
     * 测试方法：testFindPage_Success
     * 用来测试 ParmAcctPmtAllocBasicDefServiceImpl 方法 findPage
     * 验证分页查询成功的场景
     */
    @Test
    void testFindPage_Success() {
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            // Arrange - 简化测试，只验证核心逻辑
            List<ParmAcctPmtAllocBasicDef> mockList = new ArrayList<>();
            mockList.add(testParmAcctPmtAllocBasicDef);
            
            when(parmAcctPmtAllocBasicDefMapper.selectByCondition(any(AcctPmtBasicInfoReqDTO.class))).thenReturn(mockList);

            // Act
            PageResultDTO<AcctPmtBasicInfoResDTO> result = 
                    parmAcctPmtAllocBasicDefService.findPage(1, 10, testReqDTO);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getData()).isNotNull();
            verify(parmAcctPmtAllocBasicDefMapper).selectByCondition(any(AcctPmtBasicInfoReqDTO.class));
        }
    }

    /**
     * 测试方法：testFindById_Success
     * 用来测试 ParmAcctPmtAllocBasicDefServiceImpl 方法 findById
     * 验证根据ID查询成功的场景
     */
    @Test
    void testFindById_Success() {
        // Arrange
        when(parmAcctPmtAllocBasicDefMapper.selectByPrimaryKey("TEST_ID_001"))
                .thenReturn(testParmAcctPmtAllocBasicDef);

        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            List<ParmAcctPmtAllocBasicCol> colList = new ArrayList<>();
            ParmAcctPmtAllocBasicCol col = new ParmAcctPmtAllocBasicCol();
            col.setAccountProductNumber("PROD001");
            col.setAllocationSequenceDefault(1L);
            colList.add(col);

            when(parmAcctPmtAllocBasicColMapper.selectByOrgNumAndTableId("ORG001", "TEST_TABLE_001"))
                    .thenReturn(colList);

            // Act
            AcctPmtBasicInfoResDTO result = parmAcctPmtAllocBasicDefService.findById("TEST_ID_001");

            // Assert
            assertThat(result).isNotNull();

            verify(parmAcctPmtAllocBasicDefMapper).selectByPrimaryKey("TEST_ID_001");
            verify(parmAcctPmtAllocBasicColMapper).selectByOrgNumAndTableId("ORG001", "TEST_TABLE_001");
        }
    }

    /**
     * 测试方法：testFindById_NotFound
     * 用来测试 ParmAcctPmtAllocBasicDefServiceImpl 方法 findById
     * 验证当记录不存在时抛出异常的场景
     */
    @Test
    void testFindById_NotFound() {
        // Arrange
        when(parmAcctPmtAllocBasicDefMapper.selectByPrimaryKey("TEST_ID_001"))
                .thenReturn(null);

        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("ORG001");

            // Assert
            assertThatThrownBy(() -> parmAcctPmtAllocBasicDefService.findById("TEST_ID_001"))
                    .isInstanceOf(AnyTxnParameterException.class);

            verify(parmAcctPmtAllocBasicDefMapper).selectByPrimaryKey("TEST_ID_001");
        }
    }

    /**
     * 测试方法：testFindById_NullId
     * 用来测试 ParmAcctPmtAllocBasicDefServiceImpl 方法 findById
     * 验证当ID为null时抛出异常的场景
     */
    @Test
    void testFindById_NullId() {
        // Assert
        assertThatThrownBy(() -> parmAcctPmtAllocBasicDefService.findById(null))
                .isInstanceOf(AnyTxnParameterException.class);
    }

    /**
     * 测试方法：testRemove_Success
     * 用来测试 ParmAcctPmtAllocBasicDefServiceImpl 方法 remove
     * 验证成功删除的场景
     */
    @Test
    void testRemove_Success() {
        // Arrange
        when(parmAcctPmtAllocBasicDefMapper.selectByPrimaryKey("TEST_ID_001"))
                .thenReturn(testParmAcctPmtAllocBasicDef);

        // Act
        ParameterCompare result = parmAcctPmtAllocBasicDefService.remove("TEST_ID_001");

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getMainParmId()).isEqualTo("TEST_TABLE_001");
        assertThat(result.getBefore()).isNotNull();

        verify(parmAcctPmtAllocBasicDefMapper).selectByPrimaryKey("TEST_ID_001");
    }

    /**
     * 测试方法：testRemove_NotFound
     * 用来测试 ParmAcctPmtAllocBasicDefServiceImpl 方法 remove
     * 验证当记录不存在时抛出异常的场景
     */
    @Test
    void testRemove_NotFound() {
        // Arrange
        when(parmAcctPmtAllocBasicDefMapper.selectByPrimaryKey("TEST_ID_001"))
                .thenReturn(null);

        // Assert
        assertThatThrownBy(() -> parmAcctPmtAllocBasicDefService.remove("TEST_ID_001"))
                .isInstanceOf(AnyTxnParameterException.class);

        verify(parmAcctPmtAllocBasicDefMapper).selectByPrimaryKey("TEST_ID_001");
    }

    /**
     * 测试方法：testRemove_NullId
     * 用来测试 ParmAcctPmtAllocBasicDefServiceImpl 方法 remove
     * 验证当ID为null时抛出异常的场景
     */
    @Test
    void testRemove_NullId() {
        // Assert
        assertThatThrownBy(() -> parmAcctPmtAllocBasicDefService.remove(null))
                .isInstanceOf(AnyTxnParameterException.class);
    }

    /**
     * 测试方法：testQueryAcctPmtBasicDefInfoByOrgTId_Success
     * 用来测试 ParmAcctPmtAllocBasicDefServiceImpl 方法 queryAcctPmtBasicDefInfoByOrgTId
     * 验证根据机构号和表ID查询成功的场景
     */
    @Test
    void testQueryAcctPmtBasicDefInfoByOrgTId_Success() {
        // Arrange
        when(parmAcctPmtAllocBasicDefMapper.selectByOrgNumAndTableId("ORG001", "TEST_TABLE_001"))
                .thenReturn(testParmAcctPmtAllocBasicDef);

        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
            
            List<ParmAcctPmtAllocBasicCol> colList = new ArrayList<>();
            ParmAcctPmtAllocBasicCol col = new ParmAcctPmtAllocBasicCol();
            col.setAccountProductNumber("PROD001");
            col.setAllocationSequenceDefault(1L);
            colList.add(col);

            when(parmAcctPmtAllocBasicColMapper.selectByOrgNumAndTableId("ORG001", "TEST_TABLE_001"))
                    .thenReturn(colList);

            // Act
            AcctPmtBasicInfoResDTO result = 
                    parmAcctPmtAllocBasicDefService.queryAcctPmtBasicDefInfoByOrgTId("ORG001", "TEST_TABLE_001");

            // Assert
            assertThat(result).isNotNull();

            verify(parmAcctPmtAllocBasicDefMapper).selectByOrgNumAndTableId("ORG001", "TEST_TABLE_001");
            verify(parmAcctPmtAllocBasicColMapper).selectByOrgNumAndTableId("ORG001", "TEST_TABLE_001");
        }
    }
} 
