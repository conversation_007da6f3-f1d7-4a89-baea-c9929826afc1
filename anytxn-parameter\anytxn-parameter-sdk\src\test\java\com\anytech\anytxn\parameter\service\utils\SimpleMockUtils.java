package com.anytech.anytxn.parameter.service.utils;

import java.util.function.Supplier;

/**
 * 简化Mock工具类
 * 提供轻量级Mock执行环境
 */
public class SimpleMockUtils {
    
    /**
     * 在Mock环境下执行带返回值的操作
     */
    public static <T> T executeWithStaticMocks(Supplier<T> execution) {
        try {
            return execution.get();
        } catch (Exception e) {
            throw new RuntimeException("Mock执行失败", e);
        }
    }
    
    /**
     * 在Mock环境下执行无返回值的操作
     */
    public static void executeWithStaticMocks(Runnable execution) {
        try {
            execution.run();
        } catch (Exception e) {
            throw new RuntimeException("Mock执行失败", e);
        }
    }
}