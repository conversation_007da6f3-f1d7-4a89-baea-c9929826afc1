package com.anytech.anytxn.parameter.card.service;

import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardFaceLayoutInfoDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardFaceReqDTO;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardFaceLayoutInfo;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardFaceLayoutInfoMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardFaceLayoutInfoSelfMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * CardFaceLayoutInfoServiceImpl单元测试类
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
@ExtendWith(MockitoExtension.class)
class CardFaceLayoutInfoServiceTest {

    @Mock
    private ParmCardFaceLayoutInfoSelfMapper parmCardFaceLayoutInfoSelfMapper;

    @Mock
    private ParmCardFaceLayoutInfoMapper parmCardFaceLayoutInfoMapper;

    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private CardFaceLayoutInfoServiceImpl cardFaceLayoutInfoService;

    private MockedStatic<BeanMapping> beanMappingMockedStatic;
    private MockedStatic<TenantUtils> tenantUtilsMockedStatic;
    private MockedStatic<CollectionUtils> collectionUtilsMockedStatic;
    private MockedStatic<OrgNumberUtils> orgNumberUtilsMockedStatic;

    private CardFaceReqDTO cardFaceReqDTO;
    private CardFaceLayoutInfoDTO cardFaceLayoutInfoDTO;
    private ParmCardFaceLayoutInfo parmCardFaceLayoutInfo;
    private List<CardFaceLayoutInfoDTO> cardFaceLayoutInfoDTOList;

    @BeforeEach
    void setUp() {
        // 初始化静态Mock
        beanMappingMockedStatic = mockStatic(BeanMapping.class);
        tenantUtilsMockedStatic = mockStatic(TenantUtils.class);
        collectionUtilsMockedStatic = mockStatic(CollectionUtils.class);
        orgNumberUtilsMockedStatic = mockStatic(OrgNumberUtils.class);

        // 设置测试数据
        setupTestData();

        // 设置静态方法Mock
        setupStaticMocks();
    }

    private void setupTestData() {
        // 创建CardFaceReqDTO测试对象
        cardFaceReqDTO = new CardFaceReqDTO();
        cardFaceReqDTO.setOrganizationNumber("1001");
        cardFaceReqDTO.setTableId("FACE001");

        // 创建CardFaceLayoutInfoDTO测试对象
        cardFaceLayoutInfoDTO = new CardFaceLayoutInfoDTO();
        cardFaceLayoutInfoDTO.setCardLayoutCode("LAYOUT001");
        cardFaceLayoutInfoDTO.setDescription("Test Layout");
        cardFaceLayoutInfoDTO.setStatus("1");

        cardFaceLayoutInfoDTOList = Arrays.asList(cardFaceLayoutInfoDTO);
        cardFaceReqDTO.setCardFaceLayoutInfoDTOList(cardFaceLayoutInfoDTOList);

        // 创建ParmCardFaceLayoutInfo测试对象
        parmCardFaceLayoutInfo = new ParmCardFaceLayoutInfo();
        parmCardFaceLayoutInfo.setId(1L);
        parmCardFaceLayoutInfo.setOrganizationNumber("1001");
        parmCardFaceLayoutInfo.setCardFaceTableId("FACE001");
        parmCardFaceLayoutInfo.setCardLayoutCode("LAYOUT001");
        parmCardFaceLayoutInfo.setStatus("1");
        parmCardFaceLayoutInfo.setCreateTime(LocalDateTime.now());
        parmCardFaceLayoutInfo.setUpdateTime(LocalDateTime.now());
        parmCardFaceLayoutInfo.setUpdateBy(Constants.DEFAULT_UPDATE_BY);
        parmCardFaceLayoutInfo.setVersionNumber(1L);
    }

    private void setupStaticMocks() {
        // Mock静态方法
        orgNumberUtilsMockedStatic.when(() -> OrgNumberUtils.getOrg()).thenReturn("1001");
        tenantUtilsMockedStatic.when(() -> TenantUtils.getTenantId()).thenReturn("tenant001");
        beanMappingMockedStatic.when(() -> BeanMapping.copy(any(CardFaceLayoutInfoDTO.class), eq(ParmCardFaceLayoutInfo.class)))
                .thenReturn(parmCardFaceLayoutInfo);
        collectionUtilsMockedStatic.when(() -> CollectionUtils.isEmpty(any())).thenCallRealMethod();
        collectionUtilsMockedStatic.when(() -> CollectionUtils.isNotEmpty(any())).thenCallRealMethod();

        // Mock依赖方法
        Mockito.lenient().when(numberIdGenerator.generateId(anyString())).thenReturn(1L);
        Mockito.lenient().when(parmCardFaceLayoutInfoMapper.insert(any(ParmCardFaceLayoutInfo.class))).thenReturn(1);
        Mockito.lenient().when(parmCardFaceLayoutInfoMapper.selectLayoutCodeByFaceIdAndLayoutCode(anyString(), anyString()))
                .thenReturn(new ArrayList<>());
    }

    @Test
    void testAddCardFaceLayoutCodes_Success() {
        // Arrange - 成功路径测试
        
        // Act
        assertDoesNotThrow(() -> cardFaceLayoutInfoService.addCardFaceLayoutCodes(cardFaceReqDTO));

        // Assert
        verify(parmCardFaceLayoutInfoMapper, times(1)).selectLayoutCodeByFaceIdAndLayoutCode("1001", "FACE001");
        verify(numberIdGenerator, times(1)).generateId("tenant001");
        verify(parmCardFaceLayoutInfoMapper, times(1)).insert(any(ParmCardFaceLayoutInfo.class));
    }

    @Test
    void testAddCardFaceLayoutCodes_EmptyList() {
        // Arrange - 空列表测试
        cardFaceReqDTO.setCardFaceLayoutInfoDTOList(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> cardFaceLayoutInfoService.addCardFaceLayoutCodes(cardFaceReqDTO));
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_CARD_FACE_FAULT.getCode(), exception.getErrCode());
    }

    @Test
    void testAddCardFaceLayoutCodes_ExistingLayout() {
        // Arrange - 已存在版面代码测试
        List<CardFaceLayoutInfoDTO> existingList = Arrays.asList(cardFaceLayoutInfoDTO);
        Mockito.lenient().when(parmCardFaceLayoutInfoMapper.selectLayoutCodeByFaceIdAndLayoutCode("1001", "FACE001"))
                .thenReturn(existingList);

        // Act
        assertDoesNotThrow(() -> cardFaceLayoutInfoService.addCardFaceLayoutCodes(cardFaceReqDTO));

        // Assert - 应该跳过已存在的记录
        verify(parmCardFaceLayoutInfoMapper, never()).insert(any(ParmCardFaceLayoutInfo.class));
    }

    @Test
    void testAddCardFaceLayoutCodes_InsertFailed() {
        // Arrange - 插入失败测试
        Mockito.lenient().when(parmCardFaceLayoutInfoMapper.insert(any(ParmCardFaceLayoutInfo.class))).thenReturn(0);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> cardFaceLayoutInfoService.addCardFaceLayoutCodes(cardFaceReqDTO));
        
        assertEquals(AnyTxnParameterRespCodeEnum.INSERT_CARD_FACE_LAYOUT_EXCEPTION.getCode(), exception.getErrCode());
    }

    @Test
    void testDeleteByOrgAndFaceId_Success() {
        // Arrange
        String organizationNumber = "1001";
        String cardFaceTableId = "FACE001";

        // Act
        assertDoesNotThrow(() -> cardFaceLayoutInfoService.deleteByOrgAndFaceId(organizationNumber, cardFaceTableId));

        // Assert
        verify(parmCardFaceLayoutInfoSelfMapper, times(1)).deleteByOrgAndFaceId(organizationNumber, cardFaceTableId);
    }

    @Test
    void testQueryByOrgAndFaceId_Success() {
        // Arrange
        String organizationNumber = "1001";
        String cardFaceTableId = "FACE001";
        List<CardFaceLayoutInfoDTO> expectedResult = Arrays.asList(cardFaceLayoutInfoDTO);
        
        Mockito.lenient().when(parmCardFaceLayoutInfoMapper.selectLayoutCodeByFaceIdAndLayoutCode(organizationNumber, cardFaceTableId))
                .thenReturn(expectedResult);

        // Act
        List<CardFaceLayoutInfoDTO> result = cardFaceLayoutInfoService.queryByOrgAndFaceId(organizationNumber, cardFaceTableId);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("LAYOUT001", result.get(0).getCardLayoutCode());
        verify(parmCardFaceLayoutInfoMapper, times(1)).selectLayoutCodeByFaceIdAndLayoutCode(organizationNumber, cardFaceTableId);
    }

    @Test
    void testQueryByOrgAndFaceId_EmptyResult() {
        // Arrange
        String organizationNumber = "1001";
        String cardFaceTableId = "FACE001";
        
        Mockito.lenient().when(parmCardFaceLayoutInfoMapper.selectLayoutCodeByFaceIdAndLayoutCode(organizationNumber, cardFaceTableId))
                .thenReturn(Collections.emptyList());

        // Act
        List<CardFaceLayoutInfoDTO> result = cardFaceLayoutInfoService.queryByOrgAndFaceId(organizationNumber, cardFaceTableId);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(parmCardFaceLayoutInfoMapper, times(1)).selectLayoutCodeByFaceIdAndLayoutCode(organizationNumber, cardFaceTableId);
    }

    @AfterEach
    void tearDown() {
        // 关闭静态Mock
        if (beanMappingMockedStatic != null) {
            beanMappingMockedStatic.close();
        }
        if (tenantUtilsMockedStatic != null) {
            tenantUtilsMockedStatic.close();
        }
        if (collectionUtilsMockedStatic != null) {
            collectionUtilsMockedStatic.close();
        }
        if (orgNumberUtilsMockedStatic != null) {
            orgNumberUtilsMockedStatic.close();
        }
    }
}
