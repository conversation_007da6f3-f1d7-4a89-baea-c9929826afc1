package com.anytech.anytxn.parameter.service.utils;

import java.math.BigDecimal;
import java.util.Random;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 简化测试工具类
 * 提供基础测试数据创建功能
 */
public class SimpleTestUtils {
    
    private static final Random RANDOM = new Random();
    
    /**
     * 创建测试用卡号
     */
    public static String createCardNumber() {
        return "4000" + String.format("%012d", ThreadLocalRandom.current().nextLong(0, 999999999999L));
    }
    
    /**
     * 创建测试用机构号
     */
    public static String createOrgNumber() {
        return String.format("%03d", RANDOM.nextInt(999) + 1);
    }
    
    /**
     * 创建测试用金额
     */
    public static BigDecimal createAmount() {
        return BigDecimal.valueOf(RANDOM.nextDouble() * 1000).setScale(2, BigDecimal.ROUND_HALF_UP);
    }
    
    /**
     * 验证卡号格式是否正确
     */
    public static boolean isValidCardNumber(String cardNumber) {
        return cardNumber != null && cardNumber.matches("\\d{16}");
    }
    
    /**
     * 创建测试用表ID
     */
    public static String createTableId() {
        return "TABLE" + String.format("%03d", RANDOM.nextInt(999) + 1);
    }
    
    /**
     * 创建测试用费用代码
     */
    public static String createFeeCode() {
        return "FEE" + String.format("%03d", RANDOM.nextInt(999) + 1);
    }
    
    /**
     * 创建测试用描述
     */
    public static String createDescription() {
        String[] descriptions = {"测试费用", "服务费", "手续费", "管理费", "年费"};
        return descriptions[RANDOM.nextInt(descriptions.length)];
    }
}