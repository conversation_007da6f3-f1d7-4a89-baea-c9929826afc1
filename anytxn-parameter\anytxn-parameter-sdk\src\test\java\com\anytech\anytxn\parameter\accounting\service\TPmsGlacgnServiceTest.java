package com.anytech.anytxn.parameter.accounting.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.accounting.mapper.TPmsGlacgnMapper;
import com.anytech.anytxn.parameter.accounting.mapper.TPmsGlacgnSelfMapper;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.TPmsGlacgnDTO;
import com.anytech.anytxn.parameter.base.accounting.domain.model.TPmsGlacgn;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * TPmsGlacgnService 单元测试类
 *
 * <AUTHOR> Generator
 * @date 2024-12-19
 */
@ExtendWith(MockitoExtension.class)
class TPmsGlacgnServiceTest {

    @Mock
    private TPmsGlacgnMapper tPmsGlacgnMapper;

    @Mock
    private TPmsGlacgnSelfMapper tPmsGlacgnSelfMapper;

    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private TPmsGlacgnServiceImpl tPmsGlacgnService;

    private MockedStatic<OrgNumberUtils> orgNumberUtilsMockedStatic;
    private MockedStatic<BeanMapping> beanMappingMockedStatic;

    @BeforeEach
    void setUp() {
        orgNumberUtilsMockedStatic = mockStatic(OrgNumberUtils.class);
        beanMappingMockedStatic = mockStatic(BeanMapping.class);

        // 设置静态方法的默认返回值
        orgNumberUtilsMockedStatic.when(OrgNumberUtils::getOrg).thenReturn("001");
        Mockito.lenient().when(numberIdGenerator.generateId(anyString())).thenReturn(*********L);
    }

    @AfterEach
    void tearDown() {
        // 关闭静态mock
        if (orgNumberUtilsMockedStatic != null) {
            orgNumberUtilsMockedStatic.close();
        }
        if (beanMappingMockedStatic != null) {
            beanMappingMockedStatic.close();
        }
    }

    @Test
    void testFindByAnualFlag_Success() throws AnyTxnParameterException {
        // Arrange
        String glAnnualFlag = "Y";
        String organizationNumber = "001";
        
        TPmsGlacgn mockEntity = createMockTPmsGlacgn();
        List<TPmsGlacgn> mockEntityList = Collections.singletonList(mockEntity);
        
        TPmsGlacgnDTO mockDTO = createMockTPmsGlacgnDTO();
        List<TPmsGlacgnDTO> mockDTOList = Collections.singletonList(mockDTO);

        Mockito.lenient().when(tPmsGlacgnSelfMapper.findByAnualFlag(glAnnualFlag, organizationNumber))
                .thenReturn(mockEntityList);
        beanMappingMockedStatic.when(() -> BeanMapping.copyList(mockEntityList, TPmsGlacgnDTO.class))
                .thenReturn(mockDTOList);

        // Act
        List<TPmsGlacgnDTO> result = tPmsGlacgnService.findByAnualFlag(glAnnualFlag);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(mockDTO.getId(), result.get(0).getId());
        verify(tPmsGlacgnSelfMapper).findByAnualFlag(glAnnualFlag, organizationNumber);
    }

    @Test
    void testFindByAnualFlag_EmptyList() throws AnyTxnParameterException {
        // Arrange
        String glAnnualFlag = "N";
        String organizationNumber = "001";
        
        List<TPmsGlacgn> emptyList = new ArrayList<>();

        Mockito.lenient().when(tPmsGlacgnSelfMapper.findByAnualFlag(glAnnualFlag, organizationNumber))
                .thenReturn(emptyList);

        // Act
        List<TPmsGlacgnDTO> result = tPmsGlacgnService.findByAnualFlag(glAnnualFlag);

        // Assert
        assertNull(result);
        verify(tPmsGlacgnSelfMapper).findByAnualFlag(glAnnualFlag, organizationNumber);
    }

    @Test
    void testFindByCheckFlag_Success() throws AnyTxnParameterException {
        // Arrange
        String checkFlag = "Y";
        String organizationNumber = "001";
        
        TPmsGlacgn mockEntity = createMockTPmsGlacgn();
        List<TPmsGlacgn> mockEntityList = Collections.singletonList(mockEntity);
        
        TPmsGlacgnDTO mockDTO = createMockTPmsGlacgnDTO();
        List<TPmsGlacgnDTO> mockDTOList = Collections.singletonList(mockDTO);

        Mockito.lenient().when(tPmsGlacgnSelfMapper.findByCheckFlag(checkFlag, organizationNumber))
                .thenReturn(mockEntityList);
        beanMappingMockedStatic.when(() -> BeanMapping.copyList(mockEntityList, TPmsGlacgnDTO.class))
                .thenReturn(mockDTOList);

        // Act
        List<TPmsGlacgnDTO> result = tPmsGlacgnService.findByCheckFlag(checkFlag);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(mockDTO.getId(), result.get(0).getId());
        verify(tPmsGlacgnSelfMapper).findByCheckFlag(checkFlag, organizationNumber);
    }

    @Test
    void testFindByCheckFlag_EmptyList() throws AnyTxnParameterException {
        // Arrange
        String checkFlag = "N";
        String organizationNumber = "001";
        
        List<TPmsGlacgn> emptyList = new ArrayList<>();

        Mockito.lenient().when(tPmsGlacgnSelfMapper.findByCheckFlag(checkFlag, organizationNumber))
                .thenReturn(emptyList);

        // Act
        List<TPmsGlacgnDTO> result = tPmsGlacgnService.findByCheckFlag(checkFlag);

        // Assert
        assertNull(result);
        verify(tPmsGlacgnSelfMapper).findByCheckFlag(checkFlag, organizationNumber);
    }

    @Test
    void testFindByGlAcctAndCurrCode_Success() throws AnyTxnParameterException {
        // Arrange
        String organizationNumber = "001";
        String branchid = "DINERS";
        String glAcct = "1001";
        String currCode = "USD";
        
        TPmsGlacgn mockEntity = createMockTPmsGlacgn();
        TPmsGlacgnDTO mockDTO = createMockTPmsGlacgnDTO();

        Mockito.lenient().when(tPmsGlacgnSelfMapper.findByGlAcctAndCurrCode(organizationNumber, branchid, glAcct, currCode))
                .thenReturn(mockEntity);
        beanMappingMockedStatic.when(() -> BeanMapping.copy(mockEntity, TPmsGlacgnDTO.class))
                .thenReturn(mockDTO);

        // Act
        TPmsGlacgnDTO result = tPmsGlacgnService.findByGlAcctAndCurrCode(organizationNumber, branchid, glAcct, currCode);

        // Assert
        assertNotNull(result);
        assertEquals(mockDTO.getId(), result.getId());
        verify(tPmsGlacgnSelfMapper).findByGlAcctAndCurrCode(organizationNumber, branchid, glAcct, currCode);
    }

    @Test
    void testFindByGlAcctAndCurrCode_NotFound() throws AnyTxnParameterException {
        // Arrange
        String organizationNumber = "001";
        String branchid = "DINERS";
        String glAcct = "1001";
        String currCode = "USD";

        Mockito.lenient().when(tPmsGlacgnSelfMapper.findByGlAcctAndCurrCode(organizationNumber, branchid, glAcct, currCode))
                .thenReturn(null);

        // Act
        TPmsGlacgnDTO result = tPmsGlacgnService.findByGlAcctAndCurrCode(organizationNumber, branchid, glAcct, currCode);

        // Assert
        assertNull(result);
        verify(tPmsGlacgnSelfMapper).findByGlAcctAndCurrCode(organizationNumber, branchid, glAcct, currCode);
    }

    @Test
    void testFindAll_Success() throws AnyTxnParameterException {
        // Arrange
        String organizationNumber = "001";
        
        TPmsGlacgn mockEntity = createMockTPmsGlacgn();
        List<TPmsGlacgn> mockEntityList = Collections.singletonList(mockEntity);
        
        TPmsGlacgnDTO mockDTO = createMockTPmsGlacgnDTO();
        List<TPmsGlacgnDTO> mockDTOList = Collections.singletonList(mockDTO);

        Mockito.lenient().when(tPmsGlacgnSelfMapper.findAll(true, organizationNumber))
                .thenReturn(mockEntityList);
        beanMappingMockedStatic.when(() -> BeanMapping.copyList(mockEntityList, TPmsGlacgnDTO.class))
                .thenReturn(mockDTOList);

        // Act
        List<TPmsGlacgnDTO> result = tPmsGlacgnService.findAll(organizationNumber);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(mockDTO.getId(), result.get(0).getId());
        verify(tPmsGlacgnSelfMapper).findAll(true, organizationNumber);
    }

    @Test
    void testFindAll_EmptyList() throws AnyTxnParameterException {
        // Arrange
        String organizationNumber = "001";
        
        List<TPmsGlacgn> emptyList = new ArrayList<>();

        Mockito.lenient().when(tPmsGlacgnSelfMapper.findAll(true, organizationNumber))
                .thenReturn(emptyList);

        // Act
        List<TPmsGlacgnDTO> result = tPmsGlacgnService.findAll(organizationNumber);

        // Assert
        assertNull(result);
        verify(tPmsGlacgnSelfMapper).findAll(true, organizationNumber);
    }

    @Test
    void testSelectCount_Success() {
        // Arrange
        String organizationNumber = "001";
        Long expectedCount = 10L;

        Mockito.lenient().when(tPmsGlacgnSelfMapper.selectCount(organizationNumber))
                .thenReturn(expectedCount);

        // Act
        Long result = tPmsGlacgnService.selectCount();

        // Assert
        assertEquals(expectedCount, result);
        verify(tPmsGlacgnSelfMapper).selectCount(organizationNumber);
    }

    @Test
    void testDetail_Success() {
        // Arrange
        String id = "*********";
        TPmsGlacgn mockEntity = createMockTPmsGlacgn();

        Mockito.lenient().when(tPmsGlacgnMapper.selectByPrimaryKey(id)).thenReturn(mockEntity);

        // Act
        TPmsGlacgnDTO result = tPmsGlacgnService.detail(id);

        // Assert
        assertNotNull(result);
        assertEquals(id, result.getId());
        verify(tPmsGlacgnMapper).selectByPrimaryKey(id);
    }

    @Test
    void testDetail_NotFound() {
        // Arrange
        String id = "*********";

        Mockito.lenient().when(tPmsGlacgnMapper.selectByPrimaryKey(id)).thenReturn(null);

        // Act
        TPmsGlacgnDTO result = tPmsGlacgnService.detail(id);

        // Assert
        assertNull(result);
        verify(tPmsGlacgnMapper).selectByPrimaryKey(id);
    }

    @Test
    void testRemove_Success() {
        // Arrange
        String id = "*********";
        TPmsGlacgn mockEntity = createMockTPmsGlacgn();

        Mockito.lenient().when(tPmsGlacgnMapper.selectByPrimaryKey(id)).thenReturn(mockEntity);

        // Act
        ParameterCompare result = tPmsGlacgnService.remove(id);

        // Assert
        assertNotNull(result);
        verify(tPmsGlacgnMapper).selectByPrimaryKey(id);
    }

    @Test
    void testRemove_NullId() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            tPmsGlacgnService.remove(null);
        });

        assertEquals("1261011003", exception.getErrCode());
    }

    @Test
    void testRemove_DataNotExists() {
        // Arrange
        String id = "*********";

        Mockito.lenient().when(tPmsGlacgnMapper.selectByPrimaryKey(id)).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            tPmsGlacgnService.remove(id);
        });

        assertEquals("1022023000", exception.getErrCode());
    }

    @Test
    void testAdd_Success() {
        // Arrange
        TPmsGlacgnDTO inputDTO = createMockTPmsGlacgnDTO();
        inputDTO.setId(null); // 新增时ID为空

        Mockito.lenient().when(tPmsGlacgnSelfMapper.findByGlAcctAndCurrCode(
                inputDTO.getOrganizationNumber(), "DINERS", inputDTO.getGlAcct(), inputDTO.getCurrCode()))
                .thenReturn(null);

        // Act
        ParameterCompare result = tPmsGlacgnService.add(inputDTO);

        // Assert
        assertNotNull(result);
        assertNotNull(inputDTO.getId());
        assertEquals("DINERS", inputDTO.getBranchid());
        verify(tPmsGlacgnSelfMapper).findByGlAcctAndCurrCode(
                inputDTO.getOrganizationNumber(), "DINERS", inputDTO.getGlAcct(), inputDTO.getCurrCode());
    }

    @Test
    void testAdd_NullData() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            tPmsGlacgnService.add(null);
        });

        assertEquals("1261011026", exception.getErrCode());
    }

    @Test
    void testAdd_DataExists() {
        // Arrange
        TPmsGlacgnDTO inputDTO = createMockTPmsGlacgnDTO();
        inputDTO.setId(null);

        TPmsGlacgn existingEntity = createMockTPmsGlacgn();

        Mockito.lenient().when(tPmsGlacgnSelfMapper.findByGlAcctAndCurrCode(
                inputDTO.getOrganizationNumber(), "DINERS", inputDTO.getGlAcct(), inputDTO.getCurrCode()))
                .thenReturn(existingEntity);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            tPmsGlacgnService.add(inputDTO);
        });

        assertEquals("1261011025", exception.getErrCode());
    }

    @Test
    void testUpdate_Success() {
        // Arrange
        TPmsGlacgnDTO inputDTO = createMockTPmsGlacgnDTO();
        TPmsGlacgn existingEntity = createMockTPmsGlacgn();

        Mockito.lenient().when(tPmsGlacgnMapper.selectByPrimaryKey(inputDTO.getId()))
                .thenReturn(existingEntity);

        // Act
        ParameterCompare result = tPmsGlacgnService.update(inputDTO);

        // Assert
        assertNotNull(result);
        verify(tPmsGlacgnMapper).selectByPrimaryKey(inputDTO.getId());
    }

    @Test
    void testUpdate_NullData() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            tPmsGlacgnService.update(null);
        });

        assertEquals("1261011003", exception.getErrCode());
    }

    @Test
    void testUpdate_DataNotExists() {
        // Arrange
        TPmsGlacgnDTO inputDTO = createMockTPmsGlacgnDTO();

        Mockito.lenient().when(tPmsGlacgnMapper.selectByPrimaryKey(inputDTO.getId()))
                .thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            tPmsGlacgnService.update(inputDTO);
        });

        assertEquals("1022023000", exception.getErrCode());
    }

    @Test
    void testFindByCheckOption_Success() {
        // Arrange
        String glAcctBalchkOption = "Y";
        String organizationNumber = "001";

        TPmsGlacgn mockEntity = createMockTPmsGlacgn();
        List<TPmsGlacgn> mockEntityList = Collections.singletonList(mockEntity);

        TPmsGlacgnDTO mockDTO = createMockTPmsGlacgnDTO();
        List<TPmsGlacgnDTO> mockDTOList = Collections.singletonList(mockDTO);

        Mockito.lenient().when(tPmsGlacgnSelfMapper.findByCheckOption(glAcctBalchkOption, organizationNumber))
                .thenReturn(mockEntityList);
        beanMappingMockedStatic.when(() -> BeanMapping.copyList(mockEntityList, TPmsGlacgnDTO.class))
                .thenReturn(mockDTOList);

        // Act
        List<TPmsGlacgnDTO> result = tPmsGlacgnService.findByCheckOption(glAcctBalchkOption);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(mockDTO.getId(), result.get(0).getId());
        verify(tPmsGlacgnSelfMapper).findByCheckOption(glAcctBalchkOption, organizationNumber);
    }

    @Test
    void testFindByCheckOption_EmptyList() {
        // Arrange
        String glAcctBalchkOption = "N";
        String organizationNumber = "001";

        List<TPmsGlacgn> emptyList = new ArrayList<>();

        Mockito.lenient().when(tPmsGlacgnSelfMapper.findByCheckOption(glAcctBalchkOption, organizationNumber))
                .thenReturn(emptyList);

        // Act
        List<TPmsGlacgnDTO> result = tPmsGlacgnService.findByCheckOption(glAcctBalchkOption);

        // Assert
        assertNull(result);
        verify(tPmsGlacgnSelfMapper).findByCheckOption(glAcctBalchkOption, organizationNumber);
    }

    /**
     * 创建模拟的TPmsGlacgn实体对象
     */
    private TPmsGlacgn createMockTPmsGlacgn() {
        TPmsGlacgn entity = new TPmsGlacgn();
        entity.setId("*********");
        entity.setOrganizationNumber("001");
        entity.setBranchid("DINERS");
        entity.setGlAcct("1001");
        entity.setCurrCode("USD");
        entity.setGlClass("ASSET");
        entity.setGlAcctName("Test Account");
        entity.setGlOfCheckFlag("Y");
        entity.setGlTotalCheckFlag("Y");
        entity.setGlAnnualFlag("Y");
        entity.setGlPlNum("2001");
        entity.setGlPlNumName("PL Account");
        entity.setGlBankFlag("Y");
        entity.setGlBalFlag("D");
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateBy("testUser");
        entity.setVersionNumber(1L);
        entity.setGlAcctBalchkOption("Y");
        return entity;
    }

    /**
     * 创建模拟的TPmsGlacgnDTO对象
     */
    private TPmsGlacgnDTO createMockTPmsGlacgnDTO() {
        TPmsGlacgnDTO dto = new TPmsGlacgnDTO();
        dto.setId("*********");
        dto.setOrganizationNumber("001");
        dto.setBranchid("DINERS");
        dto.setGlAcct("1001");
        dto.setCurrCode("USD");
        dto.setGlClass("ASSET");
        dto.setGlAcctName("Test Account");
        dto.setGlOfCheckFlag("Y");
        dto.setGlTotalCheckFlag("Y");
        dto.setGlAnnualFlag("Y");
        dto.setGlPlNum("2001");
        dto.setGlPlNumName("PL Account");
        dto.setGlBankFlag("Y");
        dto.setGlBalFlag("D");
        dto.setCreateTime(LocalDateTime.now());
        dto.setUpdateTime(LocalDateTime.now());
        dto.setUpdateBy("testUser");
        dto.setVersionNumber(1L);
        dto.setGlAcctBalchkOption("Y");
        return dto;
    }
}
