package com.anytech.anytxn.parameter.account.service;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.account.mapper.ParmDelinquentControlDefineMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmDelinquentControlDefineSelfMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmDelinquentControlSelfMapper;
import com.anytech.anytxn.parameter.account.service.DelinquentControlDefineServiceImpl;
import com.anytech.anytxn.parameter.base.account.domain.dto.DelinquentControlDefineReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.DelinquentControlDefineResDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.DelinquentControlReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.DelinquentControlResDTO;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmDelinquentControl;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmDelinquentControlDefine;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * DelinquentControlDefineServiceTest 测试类
 * 测试延滞控制参数定义服务的核心功能
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class DelinquentControlDefineServiceTest {

    @Mock
    private ParmDelinquentControlDefineMapper parmDelinquentControlDefineMapper;
    
    @Mock
    private ParmDelinquentControlDefineSelfMapper parmDelinquentControlDefineSelfMapper;
    
    @Mock
    private ParmDelinquentControlSelfMapper parmDelinquentControlSelfMapper;
    
    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private DelinquentControlDefineServiceImpl delinquentControlDefineService;

    private DelinquentControlDefineReqDTO testReqDTO;
    private DelinquentControlDefineResDTO testResDTO;
    private ParmDelinquentControlDefine testEntity;
    private DelinquentControlReqDTO testControlReqDTO;
    private ParmDelinquentControl testControlEntity;

    @BeforeEach
    void setUp() {
        // 创建不继承BaseParam的测试数据（Entity对象）
        testEntity = new ParmDelinquentControlDefine();
        testEntity.setId("1");
        testEntity.setOrganizationNumber("001");
        testEntity.setTableId("DELINQ_DEF001");
        testEntity.setDescription("延滞控制参数定义测试");
        testEntity.setDelinquencyAgingInd("Y");
        testEntity.setStatus("1");
        testEntity.setBadCycleDue("10");
        testEntity.setCusAcctFinancestatusInd("Y");
        testEntity.setVersionNumber(1L);
        testEntity.setCreateTime(LocalDateTime.now());
        testEntity.setUpdateTime(LocalDateTime.now());
        
        // ParmDelinquentControl也继承BaseParam，所以不能在@BeforeEach中创建
        // 将在各个测试方法中根据需要创建
    }
    
    private void createTestDTOs() {
        // 在需要DTO对象和Entity对象的测试中调用此方法，确保在Mock环境中创建
        testReqDTO = new DelinquentControlDefineReqDTO();
        testReqDTO.setId("1");
        testReqDTO.setTableId("DELINQ_DEF001");
        testReqDTO.setDescription("延滞控制参数定义测试");
        testReqDTO.setDelinquencyAgingInd("Y");
        testReqDTO.setStatus("1");
        testReqDTO.setBadCycleDue("10");
        testReqDTO.setCusAcctFinancestatusInd("Y");
        testReqDTO.setVersionNumber(1L);
        
        testControlReqDTO = new DelinquentControlReqDTO();
        testControlReqDTO.setId(1L);
        testControlReqDTO.setTableId("DELINQ_DEF001");
        testControlReqDTO.setCycleDue(30);
        testControlReqDTO.setBlockCode("BLK001");
        testReqDTO.setControlReqList(List.of(testControlReqDTO));
        
        testResDTO = new DelinquentControlDefineResDTO();
        testResDTO.setId("1");
        testResDTO.setTableId("DELINQ_DEF001");
        testResDTO.setDescription("延滞控制参数定义测试");
        testResDTO.setDelinquencyAgingInd("Y");
        testResDTO.setStatus("1");
        testResDTO.setBadCycleDue("10");
        testResDTO.setCusAcctFinancestatusInd("Y");
        testResDTO.setVersionNumber(1L);
        testResDTO.setCreateTime(LocalDateTime.now());
        testResDTO.setUpdateTime(LocalDateTime.now());
        
        DelinquentControlResDTO testControlResDTO = new DelinquentControlResDTO();
        testControlResDTO.setId(1L);
        testControlResDTO.setTableId("DELINQ_DEF001");
        testControlResDTO.setCycleDue(30);
        testControlResDTO.setBlockCode("BLK001");
        testResDTO.setControlReqList(List.of(testControlResDTO));
        
        // 创建ParmDelinquentControl Entity对象（也继承BaseParam）
        testControlEntity = new ParmDelinquentControl();
        testControlEntity.setId(1L);
        testControlEntity.setOrganizationNumber("001");
        testControlEntity.setTableId("DELINQ_DEF001");
        testControlEntity.setCycleDue(30);
        testControlEntity.setBlockCode("BLK001");
    }

    /**
     * 测试通过机构号和表ID查询延滞控制参数定义 - 成功场景
     * 测试方法：DelinquentControlDefineServiceImpl.findDelinquentControlDefine(String, String)
     */
    @Test
    void testFindDelinquentControlDefine_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestDTOs(); // 在Mock环境中创建DTO对象
            
            when(parmDelinquentControlDefineSelfMapper.selectByOrgAndTableId("001", "DELINQ_DEF001"))
                    .thenReturn(testEntity);
            beanMappingMock.when(() -> BeanMapping.copy(any(ParmDelinquentControlDefine.class), eq(DelinquentControlDefineResDTO.class)))
                    .thenReturn(testResDTO);

            // Act
            DelinquentControlDefineResDTO result = delinquentControlDefineService.findDelinquentControlDefine("001", "DELINQ_DEF001");

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getId()).isEqualTo("1");
            assertThat(result.getTableId()).isEqualTo("DELINQ_DEF001");
            verify(parmDelinquentControlDefineSelfMapper).selectByOrgAndTableId("001", "DELINQ_DEF001");
        }
    }

    /**
     * 测试通过机构号和表ID查询延滞控制参数定义 - 未找到异常
     * 测试方法：DelinquentControlDefineServiceImpl.findDelinquentControlDefine(String, String)
     */
    @Test
    void testFindDelinquentControlDefine_NotFound_ThrowsException() {
        // Arrange
        when(parmDelinquentControlDefineSelfMapper.selectByOrgAndTableId("001", "DELINQ_DEF001"))
                .thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> delinquentControlDefineService.findDelinquentControlDefine("001", "DELINQ_DEF001"));

        assertThat(exception.getErrCode()).isEqualTo(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT.getCode());
        verify(parmDelinquentControlDefineSelfMapper).selectByOrgAndTableId("001", "DELINQ_DEF001");
    }

    /**
     * 测试分页查询所有延滞控制定义参数 - 成功场景
     * 测试方法：DelinquentControlDefineServiceImpl.findAll(Integer, Integer, DelinquentControlDefineReqDTO)
     */
    @Test
    void testFindAll_Success() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestDTOs(); // 在Mock环境中创建DTO对象
            
            when(parmDelinquentControlDefineSelfMapper.selectByCondition(any(DelinquentControlDefineReqDTO.class)))
                    .thenReturn(List.of(testEntity));
            beanMappingMock.when(() -> BeanMapping.copyList(anyList(), eq(DelinquentControlDefineResDTO.class)))
                    .thenReturn(List.of(testResDTO));

            // Act
            PageResultDTO<DelinquentControlDefineResDTO> result = delinquentControlDefineService.findAll(1, 10, testReqDTO);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getData()).hasSize(1);
            assertThat(result.getData().get(0).getId()).isEqualTo("1");
            verify(parmDelinquentControlDefineSelfMapper).selectByCondition(any(DelinquentControlDefineReqDTO.class));
        }
    }

    /**
     * 测试根据ID查询延滞控制定义 - 成功场景
     * 测试方法：DelinquentControlDefineServiceImpl.findById(String)
     */
    @Test
    void testFindById_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestDTOs(); // 在Mock环境中创建DTO和Entity对象
            
            when(parmDelinquentControlDefineMapper.selectByPrimaryKey("1")).thenReturn(testEntity);
            when(parmDelinquentControlSelfMapper.selectByOrgAndTableIdAndCycleDue("001", "DELINQ_DEF001", null))
                    .thenReturn(List.of(testControlEntity));
            
            // 创建一个预先准备好的DelinquentControlResDTO对象，避免在测试中创建
            DelinquentControlResDTO controlResDTO = new DelinquentControlResDTO();
            controlResDTO.setId(1L);
            controlResDTO.setTableId("DELINQ_DEF001");
            controlResDTO.setCycleDue(30);
            controlResDTO.setBlockCode("BLK001");
            
            beanMappingMock.when(() -> BeanMapping.copyList(anyList(), eq(DelinquentControlResDTO.class)))
                    .thenReturn(List.of(controlResDTO));

            // Act
            DelinquentControlDefineResDTO result = delinquentControlDefineService.findById("1");

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getId()).isEqualTo("1");
            assertThat(result.getControlReqList()).isNotNull();
            verify(parmDelinquentControlDefineMapper).selectByPrimaryKey("1");
            verify(parmDelinquentControlSelfMapper).selectByOrgAndTableIdAndCycleDue("001", "DELINQ_DEF001", null);
        }
    }

    /**
     * 测试根据ID查询延滞控制定义 - ID为空异常
     * 测试方法：DelinquentControlDefineServiceImpl.findById(String)
     */
    @Test
    void testFindById_NullId_ThrowsException() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> delinquentControlDefineService.findById(null));

        assertThat(exception.getErrCode()).isEqualTo(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode());
    }

    /**
     * 测试新增延滞控制定义 - 成功场景
     * 测试方法：DelinquentControlDefineServiceImpl.addDelinquentControlDefine(DelinquentControlDefineReqDTO)
     */
    @Test
    void testAddDelinquentControlDefine_Success() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class)) {
            
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            orgNumberUtilsMock.when(() -> OrgNumberUtils.getOrg(anyString())).thenReturn("001");
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("tenant001");
            createTestDTOs(); // 在Mock环境中创建DTO对象
            
            when(parmDelinquentControlDefineSelfMapper.selectByOrgAndTableId("001", "DELINQ_DEF001"))
                    .thenReturn(null);
            when(numberIdGenerator.generateId("tenant001")).thenReturn(12345L);

            // Act
            ParameterCompare result = delinquentControlDefineService.addDelinquentControlDefine(testReqDTO);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getMainParmId()).isEqualTo("DELINQ_DEF001");
            verify(parmDelinquentControlDefineSelfMapper).selectByOrgAndTableId("001", "DELINQ_DEF001");
        }
    }

    /**
     * 测试新增延滞控制定义 - 无效的badCycleDue异常
     * 测试方法：DelinquentControlDefineServiceImpl.addDelinquentControlDefine(DelinquentControlDefineReqDTO)
     */
    @Test
    void testAddDelinquentControlDefine_InvalidBadCycleDue_ThrowsException() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            createTestDTOs(); // 在Mock环境中创建DTO对象
            testReqDTO.setBadCycleDue("20"); // 超出范围 5-15

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                    () -> delinquentControlDefineService.addDelinquentControlDefine(testReqDTO));

            assertThat(exception.getErrCode()).isEqualTo(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_DELINQUENT_CONTROL_DEFINE_BY_BAD_CYCLE_DUE_FAULT.getCode());
        }
    }

    /**
     * 测试删除延滞控制定义 - 成功场景
     * 测试方法：DelinquentControlDefineServiceImpl.removeDelinquentControlDefine(String)
     */
    @Test
    void testRemoveDelinquentControlDefine_Success() {
        // Arrange
        when(parmDelinquentControlDefineMapper.selectByPrimaryKey("1")).thenReturn(testEntity);

        // Act
        ParameterCompare result = delinquentControlDefineService.removeDelinquentControlDefine("1");

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getMainParmId()).isEqualTo("DELINQ_DEF001");
        verify(parmDelinquentControlDefineMapper).selectByPrimaryKey("1");
    }
} 
