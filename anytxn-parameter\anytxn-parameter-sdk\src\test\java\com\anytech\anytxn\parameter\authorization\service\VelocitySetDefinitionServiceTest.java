package com.anytech.anytxn.parameter.authorization.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.authorization.mapper.ParmVelocityDefinitionSelfMapper;
import com.anytech.anytxn.parameter.authorization.mapper.ParmVelocitySetDefinitionMapper;
import com.anytech.anytxn.parameter.authorization.mapper.ParmVelocitySetDetailMapper;
import com.anytech.anytxn.parameter.authorization.service.VelocitySetDefinitionServiceImpl;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.VelocityDefinitionDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.VelocitySetDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.VelocitySetDefinitionDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.model.ParmVelocityDefinition;
import com.anytech.anytxn.parameter.base.authorization.domain.model.ParmVelocitySetDefinition;
import com.anytech.anytxn.parameter.base.authorization.domain.model.ParmVelocitySetDetail;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;

/**
 * VelocitySetDefinitionService的单元测试类
 * 
 * <AUTHOR>
 * @date 2025-06-26
 */
@ExtendWith(MockitoExtension.class)
class VelocitySetDefinitionServiceTest {

    @Mock
    private ParmVelocitySetDefinitionMapper parmVelocitySetDefinitionMapper;

    @Mock
    private ParmVelocitySetDetailMapper parmVelocitySetDetailMapper;

    @Mock
    private ParmVelocityDefinitionSelfMapper parmVelocityDefinitionSelfMapper;

    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private VelocitySetDefinitionServiceImpl velocitySetDefinitionService;

    private String testOrganizationNumber;
    private String testVelocitySetCode;
    private String testId;
    private String testDescription;
    private String testStatus;
    private String testVelocityCode;
    private Long testVersionNumber;
    private BigDecimal testMaxTxnAmount;
    private Long testMaxTxnCount;

    private ParmVelocitySetDefinition testParmVelocitySetDefinition;
    private VelocitySetDTO testVelocitySetDTO;
    private VelocitySetDefinitionDTO testVelocitySetDefinitionDTO;
    private ParmVelocityDefinition testParmVelocityDefinition;
    private ParmVelocitySetDetail testParmVelocitySetDetail;
    private VelocityDefinitionDTO testVelocityDefinitionDTO;

    @BeforeEach
    void setUp() throws Exception {
        // 通过反射初始化OrgNumberUtils的静态实例
        Field orgNumberUtilField = OrgNumberUtils.class.getDeclaredField("orgNumberUtil");
        orgNumberUtilField.setAccessible(true);
        orgNumberUtilField.set(null, new OrgNumberUtils());
        
        // 初始化测试数据
        testOrganizationNumber = "001";
        testVelocitySetCode = "VSC001";
        testId = "123456789";
        testDescription = "测试流量集合定义";
        testStatus = "1";
        testVelocityCode = "VEL001";
        testVersionNumber = 1L;
        testMaxTxnAmount = new BigDecimal("10000.00");
        testMaxTxnCount = 100L;

        // 初始化ParmVelocitySetDefinition
        testParmVelocitySetDefinition = new ParmVelocitySetDefinition();
        testParmVelocitySetDefinition.setId(testId);
        testParmVelocitySetDefinition.setOrganizationNumber(testOrganizationNumber);
        testParmVelocitySetDefinition.setVelocitySetCode(testVelocitySetCode);
        testParmVelocitySetDefinition.setDescription(testDescription);
        testParmVelocitySetDefinition.setStatus(testStatus);
        testParmVelocitySetDefinition.setVersionNumber(testVersionNumber);

        // 初始化VelocityDefinitionDTO
        testVelocityDefinitionDTO = new VelocityDefinitionDTO();
        testVelocityDefinitionDTO.setVelocityCde(testVelocityCode);
        testVelocityDefinitionDTO.setOrganizationNumber(testOrganizationNumber);
        testVelocityDefinitionDTO.setMaxTxnAmount(testMaxTxnAmount);
        testVelocityDefinitionDTO.setMaxTxnCount(testMaxTxnCount);
        testVelocityDefinitionDTO.setDescription(testDescription);

        // 初始化VelocitySetDTO
        testVelocitySetDTO = new VelocitySetDTO();
        testVelocitySetDTO.setId(testId);
        testVelocitySetDTO.setOrganizationNumber(testOrganizationNumber);
        testVelocitySetDTO.setVelocitySetCode(testVelocitySetCode);
        testVelocitySetDTO.setDescription(testDescription);
        testVelocitySetDTO.setStatus(testStatus);
        testVelocitySetDTO.setVersionNumber(testVersionNumber);
        testVelocitySetDTO.setVelocityDefinitionDTOList(Arrays.asList(testVelocityDefinitionDTO));

        // 初始化VelocitySetDefinitionDTO
        testVelocitySetDefinitionDTO = new VelocitySetDefinitionDTO();
        testVelocitySetDefinitionDTO.setId(testId);
        testVelocitySetDefinitionDTO.setOrganizationNumber(testOrganizationNumber);
        testVelocitySetDefinitionDTO.setVelocitySetCode(testVelocitySetCode);
        testVelocitySetDefinitionDTO.setDescription(testDescription);
        testVelocitySetDefinitionDTO.setStatus(testStatus);
        testVelocitySetDefinitionDTO.setVersionNumber(testVersionNumber);

        // 初始化ParmVelocityDefinition
        testParmVelocityDefinition = new ParmVelocityDefinition();
        testParmVelocityDefinition.setVelocityCde(testVelocityCode);
        testParmVelocityDefinition.setOrganizationNumber(testOrganizationNumber);
        testParmVelocityDefinition.setDescription(testDescription);
        testParmVelocityDefinition.setMaxTxnAmount(testMaxTxnAmount);
        testParmVelocityDefinition.setMaxTxnCount(testMaxTxnCount);

        // 初始化ParmVelocitySetDetail
        testParmVelocitySetDetail = new ParmVelocitySetDetail();
        testParmVelocitySetDetail.setId(testId);
        testParmVelocitySetDetail.setOrganizationNumber(testOrganizationNumber);
        testParmVelocitySetDetail.setVelocitySetCode(testVelocitySetCode);
        testParmVelocitySetDetail.setVelocityCde(testVelocityCode);
        testParmVelocitySetDetail.setStatus(testStatus);
    }

    /**
     * 测试分页查询流量集合定义 - 成功场景
     */
    @Test
    void testFindListVelocitySetDefinition_Success() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn(testOrganizationNumber);
            
            when(parmVelocitySetDefinitionMapper.selectByCondition(any(VelocitySetDTO.class)))
                .thenReturn(Arrays.asList(testParmVelocitySetDefinition));

            // Act
            PageResultDTO<VelocitySetDefinitionDTO> result = velocitySetDefinitionService.findListVelocitySetDefinition(1, 10, testVelocitySetDTO);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getPage());
            assertEquals(10, result.getRows());
            assertEquals(1, result.getData().size());
            assertEquals(testVelocitySetCode, result.getData().get(0).getVelocitySetCode());
            verify(parmVelocitySetDefinitionMapper).selectByCondition(any(VelocitySetDTO.class));
        }
    }

    /**
     * 测试分页查询流量集合定义 - 空参数使用默认值
     */
    @Test
    void testFindListVelocitySetDefinition_NullParameter() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn(testOrganizationNumber);
            
            when(parmVelocitySetDefinitionMapper.selectByCondition(any(VelocitySetDTO.class)))
                .thenReturn(Arrays.asList(testParmVelocitySetDefinition));

            // Act
            PageResultDTO<VelocitySetDefinitionDTO> result = velocitySetDefinitionService.findListVelocitySetDefinition(1, 10, null);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getData().size());
            verify(parmVelocitySetDefinitionMapper).selectByCondition(any(VelocitySetDTO.class));
        }
    }

    /**
     * 测试分页查询流量集合定义 - 数据库异常
     */
    @Test
    void testFindListVelocitySetDefinition_DatabaseException() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn(testOrganizationNumber);
            
            when(parmVelocitySetDefinitionMapper.selectByCondition(any(VelocitySetDTO.class)))
                .thenThrow(new RuntimeException("Database connection failed"));

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
                velocitySetDefinitionService.findListVelocitySetDefinition(1, 10, testVelocitySetDTO);
            });
            
            assertEquals(AnyTxnParameterRespCodeEnum.D_PAGE_VELOCITY_SET_DEFINITION_FAULT.getCode(), exception.getErrCode());
        }
    }

    /**
     * 测试新增流量集合定义 - 成功场景
     */
    @Test
    void testAddVelocitySetDefinition_Success() {
        // Arrange
        try (MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class)) {
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("tenant001");
            when(numberIdGenerator.generateId("tenant001")).thenReturn(Long.valueOf(testId));
            
            when(parmVelocitySetDefinitionMapper.isExistsByVelocitySetCode(testVelocitySetCode, testOrganizationNumber))
                .thenReturn(0);
            when(parmVelocitySetDetailMapper.isExistsByVelocityCde(testVelocityCode, testVelocitySetCode, testOrganizationNumber))
                .thenReturn(0);

            // Act
            ParameterCompare result = velocitySetDefinitionService.addVelocitySetDefinition(testVelocitySetDTO);

            // Assert
            assertNotNull(result);
            assertEquals(testVelocitySetCode, result.getMainParmId());
            verify(parmVelocitySetDefinitionMapper).isExistsByVelocitySetCode(testVelocitySetCode, testOrganizationNumber);
            verify(parmVelocitySetDetailMapper).isExistsByVelocityCde(testVelocityCode, testVelocitySetCode, testOrganizationNumber);
        }
    }

    /**
     * 测试新增流量集合定义 - 参数为空
     */
    @Test
    void testAddVelocitySetDefinition_NullParameter() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            velocitySetDefinitionService.addVelocitySetDefinition(null);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试新增流量集合定义 - 流量集合码已存在
     */
    @Test
    void testAddVelocitySetDefinition_VelocitySetCodeExists() {
        // Arrange
        when(parmVelocitySetDefinitionMapper.isExistsByVelocitySetCode(testVelocitySetCode, testOrganizationNumber))
            .thenReturn(1);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            velocitySetDefinitionService.addVelocitySetDefinition(testVelocitySetDTO);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_EXIST_VELOCITY_SET_CODE_DEFINITION_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试新增流量集合定义 - 流量检查码已存在
     */
    @Test
    void testAddVelocitySetDefinition_VelocityCodeExists() {
        // Arrange
        when(parmVelocitySetDefinitionMapper.isExistsByVelocitySetCode(testVelocitySetCode, testOrganizationNumber))
            .thenReturn(0);
        when(parmVelocitySetDetailMapper.isExistsByVelocityCde(testVelocityCode, testVelocitySetCode, testOrganizationNumber))
            .thenReturn(1);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            velocitySetDefinitionService.addVelocitySetDefinition(testVelocitySetDTO);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_EXIST_VELOCITY_DEFINITION_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试修改流量集合定义 - 成功场景
     */
    @Test
    void testModifyVelocitySetDefinition_Success() {
        // Arrange
        when(parmVelocitySetDefinitionMapper.selectByPrimaryKey(testId))
            .thenReturn(testParmVelocitySetDefinition);
        when(parmVelocitySetDetailMapper.selectByOrgNumberAndVelocitySetCode(testOrganizationNumber, testVelocitySetCode))
            .thenReturn(Arrays.asList(testParmVelocitySetDetail));

        // Act
        ParameterCompare result = velocitySetDefinitionService.modifyVelocitySetDefinition(testVelocitySetDTO);

        // Assert
        assertNotNull(result);
        assertEquals(testVelocitySetCode, result.getMainParmId());
        verify(parmVelocitySetDefinitionMapper).selectByPrimaryKey(testId);
    }

    /**
     * 测试删除流量集合定义 - 成功场景
     */
    @Test
    void testRemoveVelocitySetDefinition_Success() {
        // Arrange
        when(parmVelocitySetDefinitionMapper.selectByPrimaryKey(testId))
            .thenReturn(testParmVelocitySetDefinition);

        // Act
        ParameterCompare result = velocitySetDefinitionService.removeVelocitySetDefinition(testId);

        // Assert
        assertNotNull(result);
        assertEquals(testVelocitySetCode, result.getMainParmId());
        verify(parmVelocitySetDefinitionMapper).selectByPrimaryKey(testId);
    }

    /**
     * 测试根据主键查询流量集合定义 - 成功场景
     */
    @Test
    void testFindVelocitySetDefinition_Success() {
        // Arrange
        when(parmVelocitySetDefinitionMapper.selectByPrimaryKey(testId))
            .thenReturn(testParmVelocitySetDefinition);
        when(parmVelocitySetDetailMapper.selectByOrgNumberAndVelocitySetCode(testOrganizationNumber, testVelocitySetCode))
            .thenReturn(Arrays.asList(testParmVelocitySetDetail));

        // Act
        VelocitySetDTO result = velocitySetDefinitionService.findVelocitySetDefinition(testId);

        // Assert
        assertNotNull(result);
        assertEquals(testVelocitySetCode, result.getVelocitySetCode());
        assertEquals(testDescription, result.getDescription());
        assertEquals(1, result.getVelocityDefinitionDTOList().size());
        verify(parmVelocitySetDefinitionMapper).selectByPrimaryKey(testId);
        verify(parmVelocitySetDetailMapper).selectByOrgNumberAndVelocitySetCode(testOrganizationNumber, testVelocitySetCode);
    }

    /**
     * 测试根据主键查询流量集合定义 - 记录不存在
     */
    @Test
    void testFindVelocitySetDefinition_RecordNotFound() {
        // Arrange
        when(parmVelocitySetDefinitionMapper.selectByPrimaryKey(testId))
            .thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            velocitySetDefinitionService.findVelocitySetDefinition(testId);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_VELOCITY_SET_DEFINITION_BY_ID_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试根据主键查询流量集合定义 - 数据库异常
     */
    @Test
    void testFindVelocitySetDefinition_DatabaseException() {
        // Arrange
        when(parmVelocitySetDefinitionMapper.selectByPrimaryKey(testId))
            .thenThrow(new RuntimeException("Database connection failed"));

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            velocitySetDefinitionService.findVelocitySetDefinition(testId);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_VELOCITY_SET_DEFINITION_BY_ID_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试获取所有流量检查码 - 成功场景
     */
    @Test
    void testGetVelocityCodes_Success() {
        // Arrange
        when(parmVelocityDefinitionSelfMapper.selectAll(true, testOrganizationNumber))
            .thenReturn(Arrays.asList(testParmVelocityDefinition));

        // Act
        List<Map<String, String>> result = velocitySetDefinitionService.getVelocityCodes(testOrganizationNumber);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        Map<String, String> map = result.get(0);
        assertEquals(testVelocityCode, map.get("value"));
        assertEquals(testDescription, map.get("label"));
        verify(parmVelocityDefinitionSelfMapper).selectAll(true, testOrganizationNumber);
    }

    /**
     * 测试获取所有流量检查码 - 空列表
     */
    @Test
    void testGetVelocityCodes_EmptyList() {
        // Arrange
        when(parmVelocityDefinitionSelfMapper.selectAll(true, testOrganizationNumber))
            .thenReturn(Collections.emptyList());

        // Act
        List<Map<String, String>> result = velocitySetDefinitionService.getVelocityCodes(testOrganizationNumber);

        // Assert
        assertNotNull(result);
        assertEquals(0, result.size());
        verify(parmVelocityDefinitionSelfMapper).selectAll(true, testOrganizationNumber);
    }

    /**
     * 测试获取流量集合码 - 成功场景
     */
    @Test
    void testGetVelocitySetCodes_Success() {
        // Arrange
        when(parmVelocitySetDefinitionMapper.selectByOrganizationNumber(testOrganizationNumber))
            .thenReturn(Arrays.asList(testParmVelocitySetDefinition));

        // Act
        List<VelocitySetDefinitionDTO> result = velocitySetDefinitionService.getVelocitySetCodes(testOrganizationNumber);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testVelocitySetCode, result.get(0).getVelocitySetCode());
        verify(parmVelocitySetDefinitionMapper).selectByOrganizationNumber(testOrganizationNumber);
    }

    /**
     * 测试获取流量集合码 - 空列表
     */
    @Test
    void testGetVelocitySetCodes_EmptyList() {
        // Arrange
        when(parmVelocitySetDefinitionMapper.selectByOrganizationNumber(testOrganizationNumber))
            .thenReturn(Collections.emptyList());

        // Act
        List<VelocitySetDefinitionDTO> result = velocitySetDefinitionService.getVelocitySetCodes(testOrganizationNumber);

        // Assert
        assertNull(result);
        verify(parmVelocitySetDefinitionMapper).selectByOrganizationNumber(testOrganizationNumber);
    }

    /**
     * 测试获取流量检查码详情 - 成功场景
     */
    @Test
    void testGetVelocityCdes_Success() {
        // Arrange
        when(parmVelocitySetDetailMapper.selectByOrgNumberAndVelocitySetCode(testOrganizationNumber, testVelocitySetCode))
            .thenReturn(Arrays.asList(testParmVelocitySetDetail));
        when(parmVelocityDefinitionSelfMapper.selectByVelocityCode(testOrganizationNumber, testVelocityCode))
            .thenReturn(testParmVelocityDefinition);

        // Act
        List<VelocityDefinitionDTO> result = velocitySetDefinitionService.getVelocityCdes(testOrganizationNumber, testVelocitySetCode);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testVelocityCode, result.get(0).getVelocityCde());
        assertEquals(testDescription, result.get(0).getDescription());
        verify(parmVelocitySetDetailMapper).selectByOrgNumberAndVelocitySetCode(testOrganizationNumber, testVelocitySetCode);
        verify(parmVelocityDefinitionSelfMapper).selectByVelocityCode(testOrganizationNumber, testVelocityCode);
    }

    /**
     * 测试获取流量检查码详情 - 记录为空
     */
    @Test
    void testGetVelocityCdes_EmptyList() {
        // Arrange
        when(parmVelocitySetDetailMapper.selectByOrgNumberAndVelocitySetCode(testOrganizationNumber, testVelocitySetCode))
            .thenReturn(Collections.emptyList());

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            velocitySetDefinitionService.getVelocityCdes(testOrganizationNumber, testVelocitySetCode);
        });
        
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_VELOCITY_SET_CDE_NULL_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试获取流量检查码详情 - 多条记录
     */
    @Test
    void testGetVelocityCdes_MultipleRecords() {
        // Arrange
        ParmVelocitySetDetail secondDetail = new ParmVelocitySetDetail();
        secondDetail.setVelocityCde("VEL002");
        secondDetail.setOrganizationNumber(testOrganizationNumber);
        
        ParmVelocityDefinition secondDefinition = new ParmVelocityDefinition();
        secondDefinition.setVelocityCde("VEL002");
        secondDefinition.setDescription("第二个流量检查定义");
        
        when(parmVelocitySetDetailMapper.selectByOrgNumberAndVelocitySetCode(testOrganizationNumber, testVelocitySetCode))
            .thenReturn(Arrays.asList(testParmVelocitySetDetail, secondDetail));
        when(parmVelocityDefinitionSelfMapper.selectByVelocityCode(testOrganizationNumber, testVelocityCode))
            .thenReturn(testParmVelocityDefinition);
        when(parmVelocityDefinitionSelfMapper.selectByVelocityCode(testOrganizationNumber, "VEL002"))
            .thenReturn(secondDefinition);

        // Act
        List<VelocityDefinitionDTO> result = velocitySetDefinitionService.getVelocityCdes(testOrganizationNumber, testVelocitySetCode);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(testVelocityCode, result.get(0).getVelocityCde());
        assertEquals("VEL002", result.get(1).getVelocityCde());
        verify(parmVelocityDefinitionSelfMapper).selectByVelocityCode(testOrganizationNumber, testVelocityCode);
        verify(parmVelocityDefinitionSelfMapper).selectByVelocityCode(testOrganizationNumber, "VEL002");
    }
} 
