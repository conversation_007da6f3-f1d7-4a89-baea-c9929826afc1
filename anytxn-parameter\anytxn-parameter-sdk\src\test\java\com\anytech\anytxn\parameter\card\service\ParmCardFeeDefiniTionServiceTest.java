package com.anytech.anytxn.parameter.card.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.MessageSourceUtils;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.core.enums.MessageLanguageEnum;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardFeeDefinitionReqDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardFeeDefinitionResDTO;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardFeeDefinition;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardFeeDefinitionMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardFeeDefinitionSelfMapper;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ParmCardFeeDefiniTionServiceImpl 单元测试类
 * 
 * <AUTHOR> Generator
 * @date 2025-01-24
 */
@ExtendWith(MockitoExtension.class)
class ParmCardFeeDefiniTionServiceTest {

    @Mock
    private ParmCardFeeDefinitionMapper parmCardFeeDefinitionMapper;

    @Mock
    private Number16IdGen numberIdGenerator;

    @Mock
    private ParmCardFeeDefinitionSelfMapper parmCardFeeDefinitionSelfMapper;

    @InjectMocks
    private ParmCardFeeDefiniTionServiceImpl parmCardFeeDefiniTionService;

    private MockedStatic<PageHelper> pageHelperMockedStatic;
    private MockedStatic<StringUtils> stringUtilsMockedStatic;
    private MockedStatic<OrgNumberUtils> orgNumberUtilsMockedStatic;
    private MockedStatic<BeanMapping> beanMappingMockedStatic;
    private MockedStatic<JSON> jsonMockedStatic;
    private MockedStatic<TenantUtils> tenantUtilsMockedStatic;
    private MockedStatic<MessageSourceUtils> messageSourceUtilsMockedStatic;

    @BeforeEach
    void setUp() {
        pageHelperMockedStatic = mockStatic(PageHelper.class);
        stringUtilsMockedStatic = mockStatic(StringUtils.class);
        orgNumberUtilsMockedStatic = mockStatic(OrgNumberUtils.class);
        beanMappingMockedStatic = mockStatic(BeanMapping.class);
        jsonMockedStatic = mockStatic(JSON.class);
        tenantUtilsMockedStatic = mockStatic(TenantUtils.class);
        messageSourceUtilsMockedStatic = mockStatic(MessageSourceUtils.class);

        // Mock MessageSourceUtils to avoid Spring context initialization
        messageSourceUtilsMockedStatic.when(() -> MessageSourceUtils.requestLanguage()).thenReturn(MessageLanguageEnum.CN);
    }

    @AfterEach
    void tearDown() {
        if (pageHelperMockedStatic != null) {
            pageHelperMockedStatic.close();
        }
        if (stringUtilsMockedStatic != null) {
            stringUtilsMockedStatic.close();
        }
        if (orgNumberUtilsMockedStatic != null) {
            orgNumberUtilsMockedStatic.close();
        }
        if (beanMappingMockedStatic != null) {
            beanMappingMockedStatic.close();
        }
        if (jsonMockedStatic != null) {
            jsonMockedStatic.close();
        }
        if (tenantUtilsMockedStatic != null) {
            tenantUtilsMockedStatic.close();
        }
        if (messageSourceUtilsMockedStatic != null) {
            messageSourceUtilsMockedStatic.close();
        }
    }

    @Test
    void testFindByPage_Success() {
        // Arrange
        Integer pageNum = 1;
        Integer pageSize = 10;
        String feeCode = "FEE001";
        String description = "测试费用";
        String feeTypeCode = "0001";
        String organizationNumber = "ORG001";

        Page<ParmCardFeeDefinition> mockPage = new Page<>();
        List<ParmCardFeeDefinition> mockList = createMockParmCardFeeDefinitionList();
        List<CardFeeDefinitionResDTO> mockResList = createMockCardFeeDefinitionResDTOList();

        // Setup mocks in correct order
        orgNumberUtilsMockedStatic.when(() -> OrgNumberUtils.getOrg()).thenReturn("DEFAULT_ORG");
        stringUtilsMockedStatic.when(() -> StringUtils.isEmpty(organizationNumber)).thenReturn(false);
        pageHelperMockedStatic.when(() -> PageHelper.startPage(eq(pageNum), eq(pageSize))).thenReturn(mockPage);
        Mockito.lenient().when(parmCardFeeDefinitionMapper.selectByCondition(feeCode, description, feeTypeCode, organizationNumber))
                .thenReturn(mockList);
        beanMappingMockedStatic.when(() -> BeanMapping.copyList(mockList, CardFeeDefinitionResDTO.class))
                .thenReturn(mockResList);

        // Act
        PageResultDTO<CardFeeDefinitionResDTO> result = parmCardFeeDefiniTionService.findByPage(
                pageNum, pageSize, feeCode, description, feeTypeCode, organizationNumber);

        // Assert
        assertNotNull(result);
        assertEquals(mockResList, result.getData());
        verify(parmCardFeeDefinitionMapper).selectByCondition(feeCode, description, feeTypeCode, organizationNumber);
    }

    @Test
    void testFindByPage_WithEmptyOrganizationNumber() {
        // Arrange
        Integer pageNum = 1;
        Integer pageSize = 10;
        String feeCode = "FEE001";
        String description = "测试费用";
        String feeTypeCode = "0001";
        String organizationNumber = "";
        String defaultOrg = "DEFAULT_ORG";

        Page<ParmCardFeeDefinition> mockPage = new Page<>();
        List<ParmCardFeeDefinition> mockList = createMockParmCardFeeDefinitionList();
        List<CardFeeDefinitionResDTO> mockResList = createMockCardFeeDefinitionResDTOList();

        // Setup mocks in correct order
        orgNumberUtilsMockedStatic.when(() -> OrgNumberUtils.getOrg()).thenReturn(defaultOrg);
        stringUtilsMockedStatic.when(() -> StringUtils.isEmpty(organizationNumber)).thenReturn(true);
        pageHelperMockedStatic.when(() -> PageHelper.startPage(pageNum, pageSize)).thenReturn(mockPage);
        Mockito.lenient().when(parmCardFeeDefinitionMapper.selectByCondition(feeCode, description, feeTypeCode, defaultOrg))
                .thenReturn(mockList);
        beanMappingMockedStatic.when(() -> BeanMapping.copyList(mockList, CardFeeDefinitionResDTO.class))
                .thenReturn(mockResList);

        // Act
        PageResultDTO<CardFeeDefinitionResDTO> result = parmCardFeeDefiniTionService.findByPage(
                pageNum, pageSize, feeCode, description, feeTypeCode, organizationNumber);

        // Assert
        assertNotNull(result);
        assertEquals(mockResList, result.getData());
        verify(parmCardFeeDefinitionMapper).selectByCondition(feeCode, description, feeTypeCode, defaultOrg);
    }

    @Test
    void testFindById_Success() {
        // Arrange
        String id = "TEST_ID_001";
        ParmCardFeeDefinition mockEntity = createMockParmCardFeeDefinition();
        CardFeeDefinitionResDTO mockResDTO = createMockCardFeeDefinitionResDTO();

        Mockito.lenient().when(parmCardFeeDefinitionMapper.selectByPrimaryKey(id)).thenReturn(mockEntity);
        beanMappingMockedStatic.when(() -> BeanMapping.copy(mockEntity, CardFeeDefinitionResDTO.class))
                .thenReturn(mockResDTO);

        // Act
        CardFeeDefinitionResDTO result = parmCardFeeDefiniTionService.findById(id);

        // Assert
        assertNotNull(result);
        assertEquals(mockResDTO.getId(), result.getId());
        assertEquals(mockResDTO.getFeeCode(), result.getFeeCode());
        verify(parmCardFeeDefinitionMapper).selectByPrimaryKey(id);
    }

    @Test
    void testFindById_NotFound() {
        // Arrange
        String id = "NOT_EXIST_ID";

        Mockito.lenient().when(parmCardFeeDefinitionMapper.selectByPrimaryKey(id)).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> parmCardFeeDefiniTionService.findById(id));

        assertEquals(AnyTxnParameterRespCodeEnum.D_GET_CARD_FEE_DEFINITION_BY_ID_FAULT.getCode(), exception.getErrCode());
        verify(parmCardFeeDefinitionMapper).selectByPrimaryKey(id);
    }

    private List<ParmCardFeeDefinition> createMockParmCardFeeDefinitionList() {
        List<ParmCardFeeDefinition> list = new ArrayList<>();
        ParmCardFeeDefinition entity = createMockParmCardFeeDefinition();
        list.add(entity);
        return list;
    }

    private List<CardFeeDefinitionResDTO> createMockCardFeeDefinitionResDTOList() {
        List<CardFeeDefinitionResDTO> list = new ArrayList<>();
        CardFeeDefinitionResDTO dto = createMockCardFeeDefinitionResDTO();
        list.add(dto);
        return list;
    }

    private ParmCardFeeDefinition createMockParmCardFeeDefinition() {
        ParmCardFeeDefinition entity = new ParmCardFeeDefinition();
        entity.setId("TEST_ID_001");
        entity.setOrganizationNumber("ORG001");
        entity.setFeeCode("FEE001");
        entity.setDescription("测试费用");
        entity.setFeeTypeCode("0001");
        entity.setSubFeeTypeCode("000100");
        entity.setTransactionCode("TXN001");
        entity.setTransactionDescription("测试交易");
        entity.setFeeAmount(new BigDecimal("10.00"));
        entity.setStatus("1");
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateBy("TEST_USER");
        entity.setVersionNumber(1L);
        return entity;
    }

    private CardFeeDefinitionResDTO createMockCardFeeDefinitionResDTO() {
        CardFeeDefinitionResDTO dto = new CardFeeDefinitionResDTO();
        dto.setId("TEST_ID_001");
        dto.setOrganizationNumber("ORG001");
        dto.setFeeCode("FEE001");
        dto.setDescription("测试费用");
        dto.setFeeTypeCode("0001");
        dto.setSubFeeTypeCode("000100");
        dto.setTransactionCode("TXN001");
        dto.setTransactionDescription("测试交易");
        dto.setFeeAmount(new BigDecimal("10.00"));
        dto.setStatus("1");
        dto.setCreateTime(LocalDateTime.now());
        dto.setUpdateTime(LocalDateTime.now());
        dto.setUpdateBy("TEST_USER");
        dto.setVersionNumber(1L);
        return dto;
    }

    private CardFeeDefinitionReqDTO createMockCardFeeDefinitionReqDTO() {
        CardFeeDefinitionReqDTO dto = new CardFeeDefinitionReqDTO();
        dto.setId("TEST_ID_001");
        dto.setOrganizationNumber("ORG001");
        dto.setFeeCode("FEE001");
        dto.setDescription("测试费用");
        dto.setFeeTypeCode("0001");
        dto.setSubFeeTypeCode("000100");
        dto.setTransactionCode("TXN001");
        dto.setTransactionDescription("测试交易");
        dto.setFeeAmount(new BigDecimal("10.00"));
        dto.setStatus("1");
        dto.setVersionNumber(1L);
        return dto;
    }

    @Test
    void testAdd_Success() {
        // Arrange
        CardFeeDefinitionReqDTO reqDTO = createMockCardFeeDefinitionReqDTO();
        String orgNumber = "ORG001";
        Long generatedId = 1234567890123456L;
        ParmCardFeeDefinition mockEntity = createMockParmCardFeeDefinition();
        ParameterCompare mockParameterCompare = new ParameterCompare();

        orgNumberUtilsMockedStatic.when(() -> OrgNumberUtils.getOrg(reqDTO.getOrganizationNumber()))
                .thenReturn(orgNumber);
        Mockito.lenient().when(parmCardFeeDefinitionSelfMapper.isExistByOrgAndTid(orgNumber, reqDTO.getFeeCode()))
                .thenReturn(false);
        tenantUtilsMockedStatic.when(() -> TenantUtils.getTenantId()).thenReturn("TEST_TENANT");
        Mockito.lenient().when(numberIdGenerator.generateId("TEST_TENANT")).thenReturn(generatedId);
        beanMappingMockedStatic.when(() -> BeanMapping.copy(reqDTO, ParmCardFeeDefinition.class))
                .thenReturn(mockEntity);

        // Act
        ParameterCompare result = parmCardFeeDefiniTionService.add(reqDTO);

        // Assert
        assertNotNull(result);
        verify(parmCardFeeDefinitionSelfMapper).isExistByOrgAndTid(orgNumber, reqDTO.getFeeCode());
        verify(numberIdGenerator).generateId("TEST_TENANT");
    }

    @Test
    void testAdd_AlreadyExists() {
        // Arrange
        CardFeeDefinitionReqDTO reqDTO = createMockCardFeeDefinitionReqDTO();
        String orgNumber = "ORG001";

        orgNumberUtilsMockedStatic.when(() -> OrgNumberUtils.getOrg(reqDTO.getOrganizationNumber()))
                .thenReturn(orgNumber);
        Mockito.lenient().when(parmCardFeeDefinitionSelfMapper.isExistByOrgAndTid(orgNumber, reqDTO.getFeeCode()))
                .thenReturn(true);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> parmCardFeeDefiniTionService.add(reqDTO));

        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_EXIST.getCode(), exception.getErrCode());
        verify(parmCardFeeDefinitionSelfMapper).isExistByOrgAndTid(orgNumber, reqDTO.getFeeCode());
    }

    @Test
    void testModify_Success() {
        // Arrange
        CardFeeDefinitionReqDTO reqDTO = createMockCardFeeDefinitionReqDTO();
        ParmCardFeeDefinition existingEntity = createMockParmCardFeeDefinition();
        ParmCardFeeDefinition updatedEntity = createMockParmCardFeeDefinition();
        ParameterCompare mockParameterCompare = new ParameterCompare();

        Mockito.lenient().when(parmCardFeeDefinitionMapper.selectByPrimaryKey(reqDTO.getId()))
                .thenReturn(existingEntity);
        beanMappingMockedStatic.when(() -> BeanMapping.copy(reqDTO, ParmCardFeeDefinition.class))
                .thenReturn(updatedEntity);

        // Act
        ParameterCompare result = parmCardFeeDefiniTionService.modify(reqDTO);

        // Assert
        assertNotNull(result);
        verify(parmCardFeeDefinitionMapper).selectByPrimaryKey(reqDTO.getId());
    }

    @Test
    void testModify_NotFound() {
        // Arrange
        CardFeeDefinitionReqDTO reqDTO = createMockCardFeeDefinitionReqDTO();

        Mockito.lenient().when(parmCardFeeDefinitionMapper.selectByPrimaryKey(reqDTO.getId()))
                .thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> parmCardFeeDefiniTionService.modify(reqDTO));

        assertEquals(AnyTxnParameterRespCodeEnum.D_GET_CARD_FEE_DEFINITION_BY_ID_FAULT.getCode(), exception.getErrCode());
        verify(parmCardFeeDefinitionMapper).selectByPrimaryKey(reqDTO.getId());
    }

    @Test
    void testRemove_Success() {
        // Arrange
        String id = "TEST_ID_001";
        ParmCardFeeDefinition existingEntity = createMockParmCardFeeDefinition();
        ParameterCompare mockParameterCompare = new ParameterCompare();

        Mockito.lenient().when(parmCardFeeDefinitionMapper.selectByPrimaryKey(id))
                .thenReturn(existingEntity);

        // Act
        ParameterCompare result = parmCardFeeDefiniTionService.remove(id);

        // Assert
        assertNotNull(result);
        verify(parmCardFeeDefinitionMapper).selectByPrimaryKey(id);
    }

    @Test
    void testRemove_NotFound() {
        // Arrange
        String id = "NOT_EXIST_ID";

        Mockito.lenient().when(parmCardFeeDefinitionMapper.selectByPrimaryKey(id))
                .thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> parmCardFeeDefiniTionService.remove(id));

        assertEquals(AnyTxnParameterRespCodeEnum.D_GET_CARD_FEE_DEFINITION_BY_ID_FAULT.getCode(), exception.getErrCode());
        verify(parmCardFeeDefinitionMapper).selectByPrimaryKey(id);
    }

    @Test
    void testFindByOrgAndSubFeeType_Success() {
        // Arrange
        String orgNumber = "ORG001";
        String subFeeTypeCode = "000100";
        List<ParmCardFeeDefinition> mockList = createMockParmCardFeeDefinitionList();
        List<CardFeeDefinitionResDTO> mockResList = createMockCardFeeDefinitionResDTOList();

        Mockito.lenient().when(parmCardFeeDefinitionSelfMapper.selectByOrgAndSubFeeType(orgNumber, subFeeTypeCode))
                .thenReturn(mockList);
        beanMappingMockedStatic.when(() -> BeanMapping.copyList(mockList, CardFeeDefinitionResDTO.class))
                .thenReturn(mockResList);

        // Act
        List<CardFeeDefinitionResDTO> result = parmCardFeeDefiniTionService.findByOrgAndSubFeeType(orgNumber, subFeeTypeCode);

        // Assert
        assertNotNull(result);
        assertEquals(mockResList.size(), result.size());
        verify(parmCardFeeDefinitionSelfMapper).selectByOrgAndSubFeeType(orgNumber, subFeeTypeCode);
    }

    @Test
    void testFindByOrgFeeTypeAndSubFeeType_Success() {
        // Arrange
        String orgNumber = "ORG001";
        String feeTypeCode = "0001";
        String subFeeTypeCode = "000100";
        List<ParmCardFeeDefinition> mockList = createMockParmCardFeeDefinitionList();
        List<CardFeeDefinitionResDTO> mockResList = createMockCardFeeDefinitionResDTOList();

        Mockito.lenient().when(parmCardFeeDefinitionSelfMapper.selectByOrgFeeTypeAndSubFeeType(orgNumber, feeTypeCode, subFeeTypeCode))
                .thenReturn(mockList);
        beanMappingMockedStatic.when(() -> BeanMapping.copyList(mockList, CardFeeDefinitionResDTO.class))
                .thenReturn(mockResList);

        // Act
        List<CardFeeDefinitionResDTO> result = parmCardFeeDefiniTionService.findByOrgFeeTypeAndSubFeeType(orgNumber, feeTypeCode, subFeeTypeCode);

        // Assert
        assertNotNull(result);
        assertEquals(mockResList.size(), result.size());
        verify(parmCardFeeDefinitionSelfMapper).selectByOrgFeeTypeAndSubFeeType(orgNumber, feeTypeCode, subFeeTypeCode);
    }

    @Test
    void testFindByIndex_Success() {
        // Arrange
        String orgNumber = "ORG001";
        String feeTypeCode = "0001";
        String subFeeTypeCode = "000100";
        String feeCode = "FEE001";
        ParmCardFeeDefinition mockEntity = createMockParmCardFeeDefinition();
        CardFeeDefinitionResDTO mockResDTO = createMockCardFeeDefinitionResDTO();

        Mockito.lenient().when(parmCardFeeDefinitionSelfMapper.selectByIndex(orgNumber, feeTypeCode, subFeeTypeCode, feeCode))
                .thenReturn(mockEntity);
        beanMappingMockedStatic.when(() -> BeanMapping.copy(mockEntity, CardFeeDefinitionResDTO.class))
                .thenReturn(mockResDTO);

        // Act
        CardFeeDefinitionResDTO result = parmCardFeeDefiniTionService.findByIndex(orgNumber, feeTypeCode, subFeeTypeCode, feeCode);

        // Assert
        assertNotNull(result);
        assertEquals(mockResDTO.getId(), result.getId());
        verify(parmCardFeeDefinitionSelfMapper).selectByIndex(orgNumber, feeTypeCode, subFeeTypeCode, feeCode);
    }

    @Test
    void testFindByIndex_NotFound() {
        // Arrange
        String orgNumber = "ORG001";
        String feeTypeCode = "0001";
        String subFeeTypeCode = "000100";
        String feeCode = "NOT_EXIST";

        Mockito.lenient().when(parmCardFeeDefinitionSelfMapper.selectByIndex(orgNumber, feeTypeCode, subFeeTypeCode, feeCode))
                .thenReturn(null);

        // Act
        CardFeeDefinitionResDTO result = parmCardFeeDefiniTionService.findByIndex(orgNumber, feeTypeCode, subFeeTypeCode, feeCode);

        // Assert
        assertNull(result);
        verify(parmCardFeeDefinitionSelfMapper).selectByIndex(orgNumber, feeTypeCode, subFeeTypeCode, feeCode);
    }

    @Test
    void testSelectByOrgNumAndFeeCode_Success() {
        // Arrange
        String orgNumber = "ORG001";
        String feeCode = "FEE001";
        ParmCardFeeDefinition mockEntity = createMockParmCardFeeDefinition();
        CardFeeDefinitionResDTO mockResDTO = createMockCardFeeDefinitionResDTO();

        Mockito.lenient().when(parmCardFeeDefinitionSelfMapper.selectByOrgNumAndFeeCode(orgNumber, feeCode))
                .thenReturn(mockEntity);
        beanMappingMockedStatic.when(() -> BeanMapping.copy(mockEntity, CardFeeDefinitionResDTO.class))
                .thenReturn(mockResDTO);

        // Act
        CardFeeDefinitionResDTO result = parmCardFeeDefiniTionService.selectByOrgNumAndFeeCode(orgNumber, feeCode);

        // Assert
        assertNotNull(result);
        assertEquals(mockResDTO.getId(), result.getId());
        verify(parmCardFeeDefinitionSelfMapper).selectByOrgNumAndFeeCode(orgNumber, feeCode);
    }

    @Test
    void testSelectByOrgAndProductNumber_WithFeeTypeCode() {
        // Arrange
        String orgNumber = "ORG001";
        String productNumber = "PROD001";
        String feeTypeCode = "0001";
        List<ParmCardFeeDefinition> mockList = createMockParmCardFeeDefinitionList();
        List<CardFeeDefinitionResDTO> mockResList = createMockCardFeeDefinitionResDTOList();

        stringUtilsMockedStatic.when(() -> StringUtils.isEmpty(orgNumber)).thenReturn(false);
        stringUtilsMockedStatic.when(() -> StringUtils.isEmpty(productNumber)).thenReturn(false);
        stringUtilsMockedStatic.when(() -> StringUtils.isEmpty(feeTypeCode)).thenReturn(false);
        Mockito.lenient().when(parmCardFeeDefinitionSelfMapper.selectByOrgAndProductNumberAndType(orgNumber, productNumber, feeTypeCode))
                .thenReturn(mockList);
        beanMappingMockedStatic.when(() -> BeanMapping.copyList(mockList, CardFeeDefinitionResDTO.class))
                .thenReturn(mockResList);

        // Act
        List<CardFeeDefinitionResDTO> result = parmCardFeeDefiniTionService.selectByOrgAndProductNumber(orgNumber, productNumber, feeTypeCode);

        // Assert
        assertNotNull(result);
        assertEquals(mockResList.size(), result.size());
        verify(parmCardFeeDefinitionSelfMapper).selectByOrgAndProductNumberAndType(orgNumber, productNumber, feeTypeCode);
    }

    @Test
    void testSelectByOrgAndProductNumber_WithoutFeeTypeCode() {
        // Arrange
        String orgNumber = "ORG001";
        String productNumber = "PROD001";
        String feeTypeCode = "";
        List<ParmCardFeeDefinition> mockList = createMockParmCardFeeDefinitionList();
        List<CardFeeDefinitionResDTO> mockResList = createMockCardFeeDefinitionResDTOList();

        stringUtilsMockedStatic.when(() -> StringUtils.isEmpty(orgNumber)).thenReturn(false);
        stringUtilsMockedStatic.when(() -> StringUtils.isEmpty(productNumber)).thenReturn(false);
        stringUtilsMockedStatic.when(() -> StringUtils.isEmpty(feeTypeCode)).thenReturn(true);
        Mockito.lenient().when(parmCardFeeDefinitionSelfMapper.selectByOrgAndProductNumber(orgNumber, productNumber))
                .thenReturn(mockList);
        beanMappingMockedStatic.when(() -> BeanMapping.copyList(mockList, CardFeeDefinitionResDTO.class))
                .thenReturn(mockResList);

        // Act
        List<CardFeeDefinitionResDTO> result = parmCardFeeDefiniTionService.selectByOrgAndProductNumber(orgNumber, productNumber, feeTypeCode);

        // Assert
        assertNotNull(result);
        assertEquals(mockResList.size(), result.size());
        verify(parmCardFeeDefinitionSelfMapper).selectByOrgAndProductNumber(orgNumber, productNumber);
    }

    @Test
    void testSelectByOrgAndProductNumber_EmptyParameters() {
        // Arrange
        String orgNumber = "";
        String productNumber = "PROD001";
        String feeTypeCode = "0001";

        stringUtilsMockedStatic.when(() -> StringUtils.isEmpty(orgNumber)).thenReturn(true);
        stringUtilsMockedStatic.when(() -> StringUtils.isEmpty(productNumber)).thenReturn(false);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> parmCardFeeDefiniTionService.selectByOrgAndProductNumber(orgNumber, productNumber, feeTypeCode));

        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    @Test
    void testSelectAll_Success() {
        // Arrange
        String orgNumber = "ORG001";
        List<ParmCardFeeDefinition> mockList = createMockParmCardFeeDefinitionList();
        List<CardFeeDefinitionResDTO> mockResList = createMockCardFeeDefinitionResDTOList();

        Mockito.lenient().when(parmCardFeeDefinitionSelfMapper.selectAll(orgNumber))
                .thenReturn(mockList);
        beanMappingMockedStatic.when(() -> BeanMapping.copyList(mockList, CardFeeDefinitionResDTO.class))
                .thenReturn(mockResList);

        // Act
        List<CardFeeDefinitionResDTO> result = parmCardFeeDefiniTionService.selectAll(orgNumber);

        // Assert
        assertNotNull(result);
        assertEquals(mockResList.size(), result.size());
        verify(parmCardFeeDefinitionSelfMapper).selectAll(orgNumber);
    }
}
