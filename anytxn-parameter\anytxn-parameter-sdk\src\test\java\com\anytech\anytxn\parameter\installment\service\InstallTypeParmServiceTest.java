package com.anytech.anytxn.parameter.installment.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.parameter.base.common.domain.dto.ParmTransactionCodeResDTO;
import com.anytech.anytxn.parameter.base.common.domain.model.system.ParmTransactionCode;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.base.installment.domain.dto.*;
import com.anytech.anytxn.parameter.base.installment.domain.model.InstallTypeParm;
import com.anytech.anytxn.parameter.base.installment.domain.model.InstallTypeSupportMcc;
import com.anytech.anytxn.parameter.base.installment.domain.model.InstallTypeSupportMerchant;
import com.anytech.anytxn.parameter.base.installment.domain.model.InstallTypeSupportTxn;
import com.anytech.anytxn.parameter.installment.mapper.*;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * InstallTypeParmService 单元测试类
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class InstallTypeParmServiceTest {

    @Mock
    private InstallTypeParmMapper installTypeParmMapper;
    
    @Mock
    private InstallTypeParmSelfMapper installTypeParmSelfMapper;
    
    @Mock
    private InstallTypeSupportTxnSelfMapper installTypeSupportTxnSelfMapper;
    
    @Mock
    private InstallTypeSupportMccSelfMapper installTypeSupportMccSelfMapper;
    
    @Mock
    private InstallTypeSupportMerchantSelfMapper installTypeSupportMerchantSelfMapper;
    
    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private InstallTypeServiceImpl installTypeParmService;

    private MockedStatic<OrgNumberUtils> orgNumberUtilsMockedStatic;
    private MockedStatic<TenantUtils> tenantUtilsMockedStatic;
    private MockedStatic<BeanMapping> beanMappingMockedStatic;
    private MockedStatic<PageHelper> pageHelperMockedStatic;

    @BeforeEach
    void setUp() {
        orgNumberUtilsMockedStatic = mockStatic(OrgNumberUtils.class);
        tenantUtilsMockedStatic = mockStatic(TenantUtils.class);
        beanMappingMockedStatic = mockStatic(BeanMapping.class);
        pageHelperMockedStatic = mockStatic(PageHelper.class);

        // 设置静态方法的默认返回值
        Mockito.lenient().when(OrgNumberUtils.getOrg()).thenReturn("0001");
        Mockito.lenient().when(TenantUtils.getTenantId()).thenReturn("6001");
    }

    @AfterEach
    void tearDown() {
        if (orgNumberUtilsMockedStatic != null) {
            orgNumberUtilsMockedStatic.close();
        }
        if (tenantUtilsMockedStatic != null) {
            tenantUtilsMockedStatic.close();
        }
        if (beanMappingMockedStatic != null) {
            beanMappingMockedStatic.close();
        }
        if (pageHelperMockedStatic != null) {
            pageHelperMockedStatic.close();
        }
    }

    /**
     * 测试添加分期类型参数 - 成功场景
     */
    @Test
    void testAddInstallTypeParm_Success() {
        // Arrange
        InstallTypeParmReqDTO reqDTO = createValidInstallTypeParmReqDTO();
        
        Mockito.lenient().when(installTypeParmSelfMapper.isExists(anyString(), anyString())).thenReturn(0);
        Mockito.lenient().when(installTypeParmSelfMapper.isExistsByType(anyString(), anyString(), anyString())).thenReturn(0);
        Mockito.lenient().when(numberIdGenerator.generateId(anyString())).thenReturn(123456789L);

        // Act
        ParameterCompare result = installTypeParmService.addInstallTypeParm(reqDTO);

        // Assert
        assertNotNull(result);
        assertEquals(reqDTO.getType(), result.getMainParmId());
        verify(installTypeParmSelfMapper).isExists(reqDTO.getOrganizationNumber(), reqDTO.getType());
        verify(installTypeParmSelfMapper).isExistsByType(reqDTO.getAuthTransactionType(), reqDTO.getAuthTransactionTypeDetail(), "0001");
    }

    /**
     * 测试添加分期类型参数 - 必填参数为空
     */
    @Test
    void testAddInstallTypeParm_RequiredParametersEmpty() {
        // Arrange
        InstallTypeParmReqDTO reqDTO = new InstallTypeParmReqDTO();
        reqDTO.setOrganizationNumber("");
        reqDTO.setType("");

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installTypeParmService.addInstallTypeParm(reqDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.D_INSTALL_TYPE_NECESSITY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试添加分期类型参数 - 状态值不合理
     */
    @Test
    void testAddInstallTypeParm_InvalidStatus() {
        // Arrange
        InstallTypeParmReqDTO reqDTO = createValidInstallTypeParmReqDTO();
        reqDTO.setStatus("2"); // 无效状态

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installTypeParmService.addInstallTypeParm(reqDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.D_INSTALL_TYPE_STATUS_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试添加分期类型参数 - 数据已存在
     */
    @Test
    void testAddInstallTypeParm_DataExists() {
        // Arrange
        InstallTypeParmReqDTO reqDTO = createValidInstallTypeParmReqDTO();
        
        Mockito.lenient().when(installTypeParmSelfMapper.isExists(anyString(), anyString())).thenReturn(1);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installTypeParmService.addInstallTypeParm(reqDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.D_EXIST_INSTALL_TYPE_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试删除分期类型参数 - 成功场景
     */
    @Test
    void testRemoveInstallTypeParm_Success() {
        // Arrange
        String id = "123456789";
        InstallTypeParm installTypeParm = createValidInstallTypeParm();
        
        Mockito.lenient().when(installTypeParmMapper.selectByPrimaryKey(id)).thenReturn(installTypeParm);

        // Act
        ParameterCompare result = installTypeParmService.removeInstallTypeParm(id);

        // Assert
        assertNotNull(result);
        assertEquals(installTypeParm.getType(), result.getMainParmId());
        verify(installTypeParmMapper).selectByPrimaryKey(id);
    }

    /**
     * 测试删除分期类型参数 - ID为空
     */
    @Test
    void testRemoveInstallTypeParm_IdEmpty() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installTypeParmService.removeInstallTypeParm(null));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试删除分期类型参数 - 数据不存在
     */
    @Test
    void testRemoveInstallTypeParm_DataNotExists() {
        // Arrange
        String id = "123456789";
        
        Mockito.lenient().when(installTypeParmMapper.selectByPrimaryKey(id)).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installTypeParmService.removeInstallTypeParm(id));
        assertEquals(AnyTxnParameterRespCodeEnum.D_INSERT_INSTALL_TYPE_SUPPORT_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 创建有效的InstallTypeParmReqDTO对象
     */
    private InstallTypeParmReqDTO createValidInstallTypeParmReqDTO() {
        InstallTypeParmReqDTO reqDTO = new InstallTypeParmReqDTO();
        reqDTO.setId("123456789");
        reqDTO.setOrganizationNumber("0001");
        reqDTO.setType("A");
        reqDTO.setStatus(Constants.ENABLED);
        reqDTO.setMaxAuthAmount(new BigDecimal("10000.00"));
        reqDTO.setMinAuthAmount(new BigDecimal("100.00"));
        reqDTO.setCdThreshold(30);
        reqDTO.setCreatMonthCheck(6);
        reqDTO.setTypeDesc("Test Installment Type");
        reqDTO.setAuthTransactionType("01");
        reqDTO.setAuthTransactionTypeDetail("001");
        reqDTO.setUpdateBy("TEST_USER");
        reqDTO.setVersionNumber(1L);
        return reqDTO;
    }

    /**
     * 测试修改分期类型参数 - 成功场景
     */
    @Test
    void testModifyInstallTypeParm_Success() {
        // Arrange
        InstallTypeParmReqDTO reqDTO = createValidInstallTypeParmReqDTO();
        InstallTypeParm existingParm = createValidInstallTypeParm();
        InstallTypeParmResDTO resDTO = createValidInstallTypeParmResDTO();

        Mockito.lenient().when(installTypeParmMapper.selectByPrimaryKey(reqDTO.getId())).thenReturn(existingParm);
        Mockito.lenient().when(installTypeParmSelfMapper.selectByIndex(anyString(), anyString())).thenReturn(null);
        Mockito.lenient().when(installTypeParmSelfMapper.selectByIndexType(anyString(), anyString(), anyString())).thenReturn(null);
        beanMappingMockedStatic.when(() -> BeanMapping.copy(any(InstallTypeParmResDTO.class), eq(InstallTypeParmReqDTO.class))).thenReturn(reqDTO);
        beanMappingMockedStatic.when(() -> BeanMapping.copy(any(InstallTypeParm.class), eq(InstallTypeParmReqDTO.class))).thenReturn(reqDTO);

        // Mock findById method
        mockFindByIdMethod(reqDTO.getId(), resDTO);

        // Act
        ParameterCompare result = installTypeParmService.modifyInstallTypeParm(reqDTO);

        // Assert
        assertNotNull(result);
        assertEquals(reqDTO.getType(), result.getMainParmId());
        verify(installTypeParmMapper).selectByPrimaryKey(reqDTO.getId());
    }

    /**
     * 测试修改分期类型参数 - ID为空
     */
    @Test
    void testModifyInstallTypeParm_IdEmpty() {
        // Arrange
        InstallTypeParmReqDTO reqDTO = createValidInstallTypeParmReqDTO();
        reqDTO.setId(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installTypeParmService.modifyInstallTypeParm(reqDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试修改分期类型参数 - 状态为空
     */
    @Test
    void testModifyInstallTypeParm_StatusEmpty() {
        // Arrange
        InstallTypeParmReqDTO reqDTO = createValidInstallTypeParmReqDTO();
        reqDTO.setStatus("");

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installTypeParmService.modifyInstallTypeParm(reqDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.D_INSTALL_TYPE_NECESSITY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试修改分期类型参数 - 数据不存在
     */
    @Test
    void testModifyInstallTypeParm_DataNotExists() {
        // Arrange
        InstallTypeParmReqDTO reqDTO = createValidInstallTypeParmReqDTO();
        InstallTypeParmResDTO resDTO = createValidInstallTypeParmResDTO();

        Mockito.lenient().when(installTypeParmMapper.selectByPrimaryKey(reqDTO.getId())).thenReturn(null);

        // Mock findById method
        mockFindByIdMethod(reqDTO.getId(), resDTO);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installTypeParmService.modifyInstallTypeParm(reqDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_TYPE_BY_ID_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试根据ID查询分期类型参数 - 成功场景
     */
    @Test
    void testFindById_Success() {
        // Arrange
        String id = "123456789";
        InstallTypeParm installTypeParm = createValidInstallTypeParm();
        InstallTypeParmResDTO expectedResult = createValidInstallTypeParmResDTO();

        Mockito.lenient().when(installTypeParmMapper.selectByPrimaryKey(id)).thenReturn(installTypeParm);
        Mockito.lenient().when(installTypeSupportTxnSelfMapper.selectByTypeAndOrgNum(anyString(), anyString())).thenReturn(new ArrayList<>());
        Mockito.lenient().when(installTypeSupportMccSelfMapper.selectByTypeAndOrgNum(anyString(), anyString())).thenReturn(new ArrayList<>());
        Mockito.lenient().when(installTypeSupportMerchantSelfMapper.selectByTypeAndOrgNum(anyString(), anyString())).thenReturn(new ArrayList<>());
        beanMappingMockedStatic.when(() -> BeanMapping.copy(installTypeParm, InstallTypeParmResDTO.class)).thenReturn(expectedResult);

        // Act
        InstallTypeParmResDTO result = installTypeParmService.findById(id);

        // Assert
        assertNotNull(result);
        assertEquals(expectedResult.getId(), result.getId());
        verify(installTypeParmMapper).selectByPrimaryKey(id);
    }

    /**
     * 测试根据ID查询分期类型参数 - ID为空
     */
    @Test
    void testFindById_IdNull() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installTypeParmService.findById(null));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试根据ID查询分期类型参数 - 数据不存在
     */
    @Test
    void testFindById_DataNotExists() {
        // Arrange
        String id = "123456789";

        Mockito.lenient().when(installTypeParmMapper.selectByPrimaryKey(id)).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installTypeParmService.findById(id));
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_TYPE_BY_ID_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 创建有效的InstallTypeParm对象
     */
    private InstallTypeParm createValidInstallTypeParm() {
        InstallTypeParm installTypeParm = new InstallTypeParm();
        installTypeParm.setId("123456789");
        installTypeParm.setOrganizationNumber("0001");
        installTypeParm.setType("A");
        installTypeParm.setStatus(Constants.ENABLED);
        installTypeParm.setMaxAuthAmount(new BigDecimal("10000.00"));
        installTypeParm.setMinAuthAmount(new BigDecimal("100.00"));
        installTypeParm.setCdThreshold(30);
        installTypeParm.setCreatMonthCheck(6);
        installTypeParm.setTypeDesc("Test Installment Type");
        installTypeParm.setAuthTransactionType("01");
        installTypeParm.setAuthTransactionTypeDetail("001");
        installTypeParm.setVersionNumber(1L);
        installTypeParm.setCreateTime(LocalDateTime.now());
        installTypeParm.setUpdateTime(LocalDateTime.now());
        installTypeParm.setUpdateBy("TEST_USER");
        return installTypeParm;
    }

    /**
     * 创建有效的InstallTypeParmResDTO对象
     */
    private InstallTypeParmResDTO createValidInstallTypeParmResDTO() {
        InstallTypeParmResDTO resDTO = new InstallTypeParmResDTO();
        resDTO.setId("123456789");
        resDTO.setOrganizationNumber("0001");
        resDTO.setType("A");
        resDTO.setStatus(Constants.ENABLED);
        resDTO.setMaxAuthAmount(new BigDecimal("10000.00"));
        resDTO.setMinAuthAmount(new BigDecimal("100.00"));
        resDTO.setCdThreshold(30);
        resDTO.setCreatMonthCheck(6);
        resDTO.setTypeDesc("Test Installment Type");
        resDTO.setAuthTransactionType("01");
        resDTO.setAuthTransactionTypeDetail("001");
        resDTO.setVersionNumber(1L);
        resDTO.setCreateTime(LocalDateTime.now());
        resDTO.setUpdateTime(LocalDateTime.now());
        resDTO.setUpdateBy("TEST_USER");
        resDTO.setInstallTypeSupportTxnResList(new ArrayList<>());
        resDTO.setInstallTypeMccResList(new ArrayList<>());
        resDTO.setInstallTypeMerchantResList(new ArrayList<>());
        return resDTO;
    }

    /**
     * 测试分页查询分期类型参数 - 成功场景
     */
    @Test
    void testFindAll_Success() {
        // Arrange
        Integer pageNum = 1;
        Integer pageSize = 10;
        InstallTypeParmReqDTO reqDTO = new InstallTypeParmReqDTO();
        reqDTO.setOrganizationNumber("0001");

        List<InstallTypeParm> installTypeParms = Collections.singletonList(createValidInstallTypeParm());
        List<InstallTypeParmResDTO> expectedResults = Collections.singletonList(createValidInstallTypeParmResDTO());
        Page<InstallTypeParm> page = new Page<>(pageNum, pageSize);
        page.setTotal(1);
        page.setPages(1);

        pageHelperMockedStatic.when(() -> PageHelper.startPage(pageNum, pageSize)).thenReturn(page);
        Mockito.lenient().when(installTypeParmSelfMapper.selectByCondition(any(InstallTypeParmReqDTO.class))).thenReturn(installTypeParms);
        beanMappingMockedStatic.when(() -> BeanMapping.copyList(installTypeParms, InstallTypeParmResDTO.class)).thenReturn(expectedResults);

        // Act
        PageResultDTO<InstallTypeParmResDTO> result = installTypeParmService.findAll(pageNum, pageSize, reqDTO);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getData().size());
        verify(installTypeParmSelfMapper).selectByCondition(any(InstallTypeParmReqDTO.class));
    }

    /**
     * 测试分页查询分期类型参数 - 请求参数为空
     */
    @Test
    void testFindAll_ReqDTONull() {
        // Arrange
        Integer pageNum = 1;
        Integer pageSize = 10;

        List<InstallTypeParm> installTypeParms = new ArrayList<>();
        List<InstallTypeParmResDTO> expectedResults = new ArrayList<>();
        Page<InstallTypeParm> page = new Page<>(pageNum, pageSize);
        page.setTotal(0);
        page.setPages(0);

        pageHelperMockedStatic.when(() -> PageHelper.startPage(pageNum, pageSize)).thenReturn(page);
        Mockito.lenient().when(installTypeParmSelfMapper.selectByCondition(any(InstallTypeParmReqDTO.class))).thenReturn(installTypeParms);
        beanMappingMockedStatic.when(() -> BeanMapping.copyList(installTypeParms, InstallTypeParmResDTO.class)).thenReturn(expectedResults);

        // Act
        PageResultDTO<InstallTypeParmResDTO> result = installTypeParmService.findAll(pageNum, pageSize, null);

        // Assert
        assertNotNull(result);
        assertEquals(0, result.getTotal());
        assertEquals(0, result.getData().size());
        verify(installTypeParmSelfMapper).selectByCondition(any(InstallTypeParmReqDTO.class));
    }

    /**
     * 测试根据机构号和类型查询分期类型参数 - 成功场景
     */
    @Test
    void testFindByOrgNumAndType_Success() {
        // Arrange
        String organizationNumber = "0001";
        String type = "A";
        InstallTypeParm installTypeParm = createValidInstallTypeParm();
        InstallTypeParmResDTO expectedResult = createValidInstallTypeParmResDTO();

        Mockito.lenient().when(installTypeParmSelfMapper.selectByIndex(organizationNumber, type)).thenReturn(installTypeParm);
        beanMappingMockedStatic.when(() -> BeanMapping.copy(installTypeParm, InstallTypeParmResDTO.class)).thenReturn(expectedResult);

        // Act
        InstallTypeParmResDTO result = installTypeParmService.findByOrgNumAndType(organizationNumber, type);

        // Assert
        assertNotNull(result);
        assertEquals(expectedResult.getId(), result.getId());
        verify(installTypeParmSelfMapper).selectByIndex(organizationNumber, type);
    }

    /**
     * 测试根据机构号和类型查询分期类型参数 - 参数为空
     */
    @Test
    void testFindByOrgNumAndType_ParametersEmpty() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installTypeParmService.findByOrgNumAndType("", ""));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试根据机构号和类型查询分期类型参数 - 数据不存在
     */
    @Test
    void testFindByOrgNumAndType_DataNotExists() {
        // Arrange
        String organizationNumber = "0001";
        String type = "A";

        Mockito.lenient().when(installTypeParmSelfMapper.selectByIndex(organizationNumber, type)).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installTypeParmService.findByOrgNumAndType(organizationNumber, type));
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_TYPE_NULL_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试根据类型删除分期类型参数 - 成功场景
     */
    @Test
    void testRemoveByType_Success() {
        // Arrange
        String type = "A";
        InstallTypeParm installTypeParm = createValidInstallTypeParm();

        Mockito.lenient().when(installTypeParmSelfMapper.selectByIndex("0001", type)).thenReturn(installTypeParm);
        Mockito.lenient().when(installTypeParmSelfMapper.deleteByType(type, "0001")).thenReturn(1);

        // Act
        Boolean result = installTypeParmService.removeByType(type);

        // Assert
        assertTrue(result);
        verify(installTypeParmSelfMapper).selectByIndex("0001", type);
        verify(installTypeParmSelfMapper).deleteByType(type, "0001");
    }

    /**
     * 测试根据类型删除分期类型参数 - 类型为空
     */
    @Test
    void testRemoveByType_TypeEmpty() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installTypeParmService.removeByType(""));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试根据类型删除分期类型参数 - 数据不存在
     */
    @Test
    void testRemoveByType_DataNotExists() {
        // Arrange
        String type = "A";

        Mockito.lenient().when(installTypeParmSelfMapper.selectByIndex("0001", type)).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installTypeParmService.removeByType(type));
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_TYPE_NULL_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试根据机构号、大类、细类查询分期类型参数 - 成功场景
     */
    @Test
    void testFindByOrgNumAndTypeAndDetail_Success() {
        // Arrange
        String organizationNumber = "0001";
        String authTransactionType = "01";
        String authTransactionTypeDetail = "001";
        InstallTypeParm installTypeParm = createValidInstallTypeParm();
        InstallTypeParmResDTO expectedResult = createValidInstallTypeParmResDTO();

        Mockito.lenient().when(installTypeParmSelfMapper.selectByIndexType(organizationNumber, authTransactionType, authTransactionTypeDetail)).thenReturn(installTypeParm);
        beanMappingMockedStatic.when(() -> BeanMapping.copy(installTypeParm, InstallTypeParmResDTO.class)).thenReturn(expectedResult);

        // Act
        InstallTypeParmResDTO result = installTypeParmService.findByOrgNumAndTypeAndDetail(organizationNumber, authTransactionType, authTransactionTypeDetail);

        // Assert
        assertNotNull(result);
        assertEquals(expectedResult.getId(), result.getId());
        verify(installTypeParmSelfMapper).selectByIndexType(organizationNumber, authTransactionType, authTransactionTypeDetail);
    }

    /**
     * 测试根据机构号、大类、细类查询分期类型参数 - 机构号为空
     */
    @Test
    void testFindByOrgNumAndTypeAndDetail_OrgNumberEmpty() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installTypeParmService.findByOrgNumAndTypeAndDetail("", "01", "001"));
        assertEquals(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_ORG_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试根据机构号、大类、细类查询分期类型参数 - 大类为空
     */
    @Test
    void testFindByOrgNumAndTypeAndDetail_AuthTransactionTypeEmpty() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installTypeParmService.findByOrgNumAndTypeAndDetail("0001", "", "001"));
        assertEquals(AnyTxnParameterRespCodeEnum.D_INSTALL_TYPE_BIG_TYPE_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试根据机构号、大类、细类查询分期类型参数 - 细类为空
     */
    @Test
    void testFindByOrgNumAndTypeAndDetail_AuthTransactionTypeDetailEmpty() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installTypeParmService.findByOrgNumAndTypeAndDetail("0001", "01", ""));
        assertEquals(AnyTxnParameterRespCodeEnum.D_INSTALL_TYPE_BIG_SMALL_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试根据机构号、大类、细类查询分期类型参数 - 数据不存在
     */
    @Test
    void testFindByOrgNumAndTypeAndDetail_DataNotExists() {
        // Arrange
        String organizationNumber = "0001";
        String authTransactionType = "01";
        String authTransactionTypeDetail = "001";

        Mockito.lenient().when(installTypeParmSelfMapper.selectByIndexType(organizationNumber, authTransactionType, authTransactionTypeDetail)).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installTypeParmService.findByOrgNumAndTypeAndDetail(organizationNumber, authTransactionType, authTransactionTypeDetail));
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_TYPE_NULL_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试查询交易码和描述 - 成功场景
     */
    @Test
    void testFindTransCodeAndDesc_Success() {
        // Arrange
        String organizationNumber = "0001";
        List<ParmTransactionCode> transactionCodeList = Collections.singletonList(createValidParmTransactionCode());
        List<ParmTransactionCodeResDTO> expectedResults = Collections.singletonList(createValidParmTransactionCodeResDTO());

        Mockito.lenient().when(installTypeSupportTxnSelfMapper.selectSupportTransCodeAndDesc(organizationNumber)).thenReturn(transactionCodeList);
        beanMappingMockedStatic.when(() -> BeanMapping.copyList(transactionCodeList, ParmTransactionCodeResDTO.class)).thenReturn(expectedResults);

        // Act
        List<ParmTransactionCodeResDTO> result = installTypeParmService.findTransCodeAndDesc(organizationNumber);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(installTypeSupportTxnSelfMapper).selectSupportTransCodeAndDesc(organizationNumber);
    }

    /**
     * 测试查询交易码和描述 - 数据为空
     */
    @Test
    void testFindTransCodeAndDesc_DataEmpty() {
        // Arrange
        String organizationNumber = "0001";

        Mockito.lenient().when(installTypeSupportTxnSelfMapper.selectSupportTransCodeAndDesc(organizationNumber)).thenReturn(new ArrayList<>());

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installTypeParmService.findTransCodeAndDesc(organizationNumber));
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_TYPE_NULL_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 创建有效的ParmTransactionCode对象
     */
    private ParmTransactionCode createValidParmTransactionCode() {
        ParmTransactionCode transactionCode = new ParmTransactionCode();
        transactionCode.setOrganizationNumber("0001");
        transactionCode.setTransactionCode("001");
        transactionCode.setDescription("Test Transaction Code");
        return transactionCode;
    }

    /**
     * 创建有效的ParmTransactionCodeResDTO对象
     */
    private ParmTransactionCodeResDTO createValidParmTransactionCodeResDTO() {
        ParmTransactionCodeResDTO resDTO = new ParmTransactionCodeResDTO();
        resDTO.setOrganizationNumber("0001");
        resDTO.setTransactionCode("001");
        resDTO.setDescription("Test Transaction Code");
        return resDTO;
    }

    /**
     * Mock findById方法
     */
    private void mockFindByIdMethod(String id, InstallTypeParmResDTO resDTO) {
        InstallTypeParm installTypeParm = createValidInstallTypeParm();
        installTypeParm.setId(id);

        Mockito.lenient().when(installTypeParmMapper.selectByPrimaryKey(id)).thenReturn(installTypeParm);
        Mockito.lenient().when(installTypeSupportTxnSelfMapper.selectByTypeAndOrgNum(anyString(), anyString())).thenReturn(new ArrayList<>());
        Mockito.lenient().when(installTypeSupportMccSelfMapper.selectByTypeAndOrgNum(anyString(), anyString())).thenReturn(new ArrayList<>());
        Mockito.lenient().when(installTypeSupportMerchantSelfMapper.selectByTypeAndOrgNum(anyString(), anyString())).thenReturn(new ArrayList<>());
        beanMappingMockedStatic.when(() -> BeanMapping.copy(installTypeParm, InstallTypeParmResDTO.class)).thenReturn(resDTO);
    }
}
