package com.anytech.anytxn.parameter.authorization.service;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.authorization.mapper.MerchantFraudMapper;
import com.anytech.anytxn.parameter.authorization.mapper.MerchantFraudSelfMapper;
import com.anytech.anytxn.parameter.authorization.service.MerchantFraudServiceImpl;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.MerchantFraudDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.model.MerchantFraud;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * MerchantFraudService测试类
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class MerchantFraudServiceTest {

    @Mock
    private MerchantFraudMapper merchantFraudMapper;

    @Mock
    private MerchantFraudSelfMapper merchantFraudSelfMapper;

    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private MerchantFraudServiceImpl merchantFraudService;

    private MerchantFraudDTO merchantFraudDTO;
    private MerchantFraud merchantFraud;
    
    private static MockedStatic<OrgNumberUtils> mockOrgNumberUtils;

    @BeforeAll
    static void setUpAll() {
        // 在测试类级别Mock OrgNumberUtils
        mockOrgNumberUtils = mockStatic(OrgNumberUtils.class);
        mockOrgNumberUtils.when(OrgNumberUtils::getOrg).thenReturn("1001");
    }

    @AfterAll
    static void tearDownAll() {
        // 清理MockedStatic
        if (mockOrgNumberUtils != null) {
            mockOrgNumberUtils.close();
        }
    }

    @BeforeEach
    void setUp() {
        // 创建测试数据，现在可以安全调用构造函数
        merchantFraudDTO = createMerchantFraudDTO();
        merchantFraud = createMerchantFraud();
    }

    /**
     * 创建MerchantFraudDTO测试对象，手动设置属性避免BaseParam构造函数调用
     */
    private MerchantFraudDTO createMerchantFraudDTO() {
        MerchantFraudDTO dto = new MerchantFraudDTO();
        // 手动设置属性，避免调用BaseParam构造函数
        dto.setId(1L);
        dto.setOrganizationNumber("1001");
        dto.setMerchantId("MERCHANT001");
        dto.setMerchantName("Test Merchant");
        dto.setCapturedDate(LocalDate.now());
        dto.setReleasedDate(LocalDate.now().plusDays(30));
        dto.setControlIndicatorVisa("1");
        dto.setAuthSingleLimitVisa(new BigDecimal("10000.00"));
        dto.setControlIndicatorMc("0");
        dto.setAuthSingleLimitMc(new BigDecimal("5000.00"));
        return dto;
    }

    /**
     * 创建MerchantFraud测试对象，手动设置属性避免BaseParam构造函数调用
     */
    private MerchantFraud createMerchantFraud() {
        MerchantFraud fraud = new MerchantFraud();
        // 手动设置属性，避免调用BaseParam构造函数
        fraud.setId(1L);
        fraud.setOrganizationNumber("1001");
        fraud.setMerchantId("MERCHANT001");
        fraud.setMerchantName("Test Merchant");
        fraud.setCapturedDate(LocalDate.now());
        fraud.setReleasedDate(LocalDate.now().plusDays(30));
        fraud.setControlIndicatorVisa("1");
        fraud.setAuthSingleLimitVisa(new BigDecimal("10000.00"));
        fraud.setControlIndicatorMc("0");
        fraud.setAuthSingleLimitMc(new BigDecimal("5000.00"));
        fraud.setCreateTime(LocalDateTime.now());
        fraud.setUpdateTime(LocalDateTime.now());
        fraud.setUpdateBy("admin");
        return fraud;
    }

    @Test
    void testAdd_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class);
             MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class)) {
            
            mockBeanMapping.when(() -> BeanMapping.copy(merchantFraudDTO, MerchantFraud.class))
                    .thenReturn(merchantFraud);
            mockTenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            
            when(merchantFraudSelfMapper.isExists(anyString(), anyString())).thenReturn(0);
            when(numberIdGenerator.generateId(anyString())).thenReturn(123456L);
            when(merchantFraudMapper.insertSelective(any(MerchantFraud.class))).thenReturn(1);

            // Act
            int result = merchantFraudService.add(merchantFraudDTO);

            // Assert
            assertEquals(1, result);
            verify(merchantFraudSelfMapper).isExists("1001", "MERCHANT001");
            verify(numberIdGenerator).generateId("tenant001");
            verify(merchantFraudMapper).insertSelective(any(MerchantFraud.class));
        }
    }

    @Test
    void testAdd_NullParameter() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
                () -> merchantFraudService.add(null));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    @Test
    void testAdd_AlreadyExists() {
        // Arrange
        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {
            mockBeanMapping.when(() -> BeanMapping.copy(merchantFraudDTO, MerchantFraud.class))
                    .thenReturn(merchantFraud);
            when(merchantFraudSelfMapper.isExists(anyString(), anyString())).thenReturn(1);

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
                    () -> merchantFraudService.add(merchantFraudDTO));
            assertEquals(AnyTxnParameterRespCodeEnum.D_EXIST_MERCHAANT_FRAUD.getCode(), exception.getErrCode());
        }
    }

    @Test
    void testAdd_DatabaseException() {
        // Arrange
        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class);
             MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class)) {
            
            mockBeanMapping.when(() -> BeanMapping.copy(merchantFraudDTO, MerchantFraud.class))
                    .thenReturn(merchantFraud);
            mockTenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            
            when(merchantFraudSelfMapper.isExists(anyString(), anyString())).thenReturn(0);
            when(numberIdGenerator.generateId(anyString())).thenReturn(123456L);
            when(merchantFraudMapper.insertSelective(any(MerchantFraud.class)))
                    .thenThrow(new RuntimeException("Database error"));

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
                    () -> merchantFraudService.add(merchantFraudDTO));
            assertEquals(AnyTxnParameterRespCodeEnum.D_INSERT_AUTH_RULE_FAULT.getCode(), exception.getErrCode());
        }
    }

    @Test
    void testModify_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {
            mockBeanMapping.when(() -> BeanMapping.copy(merchantFraudDTO, MerchantFraud.class))
                    .thenReturn(merchantFraud);
            when(merchantFraudMapper.updateByPrimaryKeySelective(any(MerchantFraud.class))).thenReturn(1);

            // Act
            int result = merchantFraudService.modify(merchantFraudDTO);

            // Assert
            assertEquals(1, result);
            verify(merchantFraudMapper).updateByPrimaryKeySelective(any(MerchantFraud.class));
        }
    }

    @Test
    void testModify_NullParameter() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
                () -> merchantFraudService.modify(null));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    @Test
    void testModify_DatabaseException() {
        // Arrange
        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {
            mockBeanMapping.when(() -> BeanMapping.copy(merchantFraudDTO, MerchantFraud.class))
                    .thenReturn(merchantFraud);
            when(merchantFraudMapper.updateByPrimaryKeySelective(any(MerchantFraud.class)))
                    .thenThrow(new RuntimeException("Database error"));

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
                    () -> merchantFraudService.modify(merchantFraudDTO));
            assertEquals(AnyTxnParameterRespCodeEnum.D_DATABASE_UPDATE_ERROR.getCode(), exception.getErrCode());
        }
    }

    @Test
    void testDelete_Success() {
        // Arrange
        when(merchantFraudMapper.selectByPrimaryKey(1L)).thenReturn(merchantFraud);
        when(merchantFraudMapper.deleteByPrimaryKey(1L)).thenReturn(1);

        // Act
        int result = merchantFraudService.delete(1L);

        // Assert
        assertEquals(1, result);
        verify(merchantFraudMapper).selectByPrimaryKey(1L);
        verify(merchantFraudMapper).deleteByPrimaryKey(1L);
    }

    @Test
    void testDelete_NotExists() {
        // Arrange
        when(merchantFraudMapper.selectByPrimaryKey(1L)).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
                () -> merchantFraudService.delete(1L));
        assertEquals(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT.getCode(), exception.getErrCode());
        verify(merchantFraudMapper).selectByPrimaryKey(1L);
        verify(merchantFraudMapper, never()).deleteByPrimaryKey(anyLong());
    }

    @Test
    void testDelete_DatabaseException() {
        // Arrange
        when(merchantFraudMapper.selectByPrimaryKey(1L)).thenReturn(merchantFraud);
        when(merchantFraudMapper.deleteByPrimaryKey(1L))
                .thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
                () -> merchantFraudService.delete(1L));
        assertEquals(AnyTxnParameterRespCodeEnum.D_DATABASE_UPDATE_ERROR.getCode(), exception.getErrCode());
    }

    @Test
    void testSelectById_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {
            when(merchantFraudMapper.selectByPrimaryKey(1L)).thenReturn(merchantFraud);
            mockBeanMapping.when(() -> BeanMapping.copy(merchantFraud, MerchantFraudDTO.class))
                    .thenReturn(merchantFraudDTO);

            // Act
            MerchantFraudDTO result = merchantFraudService.selectById(1L);

            // Assert
            assertNotNull(result);
            assertEquals(merchantFraudDTO.getId(), result.getId());
            assertEquals(merchantFraudDTO.getMerchantId(), result.getMerchantId());
            verify(merchantFraudMapper).selectByPrimaryKey(1L);
        }
    }

    @Test
    void testSelectById_NotFound() {
        // Arrange
        when(merchantFraudMapper.selectByPrimaryKey(1L)).thenReturn(null);

        // Act
        MerchantFraudDTO result = merchantFraudService.selectById(1L);

        // Assert
        assertNull(result);
        verify(merchantFraudMapper).selectByPrimaryKey(1L);
    }

    @Test
    void testSelectById_DatabaseException() {
        // Arrange
        when(merchantFraudMapper.selectByPrimaryKey(1L))
                .thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
                () -> merchantFraudService.selectById(1L));
        assertEquals(AnyTxnParameterRespCodeEnum.D_DATABASE_UPDATE_ERROR.getCode(), exception.getErrCode());
    }

    @Test
    void testGetPage_Success() {
        // Arrange
        List<MerchantFraud> merchantFrauds = new ArrayList<>();
        merchantFrauds.add(merchantFraud);

        List<MerchantFraudDTO> merchantFraudDTOs = new ArrayList<>();
        merchantFraudDTOs.add(merchantFraudDTO);

        Page<MerchantFraud> pageInfo = new Page<>(1, 10);
        pageInfo.setTotal(1L);
        pageInfo.setPages(1);

        try (MockedStatic<PageHelper> mockPageHelper = mockStatic(PageHelper.class);
             MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {
            
            // Mock所有方法调用
            when(merchantFraudSelfMapper.selectAll("1001")).thenReturn(merchantFrauds);
            mockPageHelper.when(() -> PageHelper.startPage(1, 10)).thenReturn(pageInfo);
            mockBeanMapping.when(() -> BeanMapping.copyList(merchantFrauds, MerchantFraudDTO.class))
                    .thenReturn(merchantFraudDTOs);

            // Act
            PageResultDTO<MerchantFraudDTO> result = merchantFraudService.getPage(1, 10);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getPage());
            assertEquals(10, result.getRows());
            assertEquals(1L, result.getTotal());
            assertEquals(1, result.getTotalPage());
            assertEquals(1, result.getData().size());
            verify(merchantFraudSelfMapper).selectAll("1001");
        }
    }

    @Test
    void testGetPage_EmptyResult() {
        // Arrange
        List<MerchantFraud> emptyList = new ArrayList<>();
        List<MerchantFraudDTO> emptyDTOList = new ArrayList<>();

        Page<MerchantFraud> pageInfo = new Page<>(1, 10);
        pageInfo.setTotal(0L);
        pageInfo.setPages(0);

        try (MockedStatic<PageHelper> mockPageHelper = mockStatic(PageHelper.class);
             MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {
            
            // Mock所有方法调用
            when(merchantFraudSelfMapper.selectAll("1001")).thenReturn(emptyList);
            mockPageHelper.when(() -> PageHelper.startPage(1, 10)).thenReturn(pageInfo);
            mockBeanMapping.when(() -> BeanMapping.copyList(emptyList, MerchantFraudDTO.class))
                    .thenReturn(emptyDTOList);

            // Act
            PageResultDTO<MerchantFraudDTO> result = merchantFraudService.getPage(1, 10);

            // Assert
            assertNotNull(result);
            assertEquals(1, result.getPage());
            assertEquals(10, result.getRows());
            assertEquals(0L, result.getTotal());
            assertEquals(0, result.getTotalPage());
            assertEquals(0, result.getData().size());
        }
    }

    @Test
    void testGetPage_DatabaseException() {
        // Arrange
        Page<MerchantFraud> pageInfo = new Page<>(1, 10);

        try (MockedStatic<PageHelper> mockPageHelper = mockStatic(PageHelper.class)) {
            
            // Mock所有方法调用
            when(merchantFraudSelfMapper.selectAll("1001"))
                    .thenThrow(new RuntimeException("Database error"));
            mockPageHelper.when(() -> PageHelper.startPage(1, 10)).thenReturn(pageInfo);

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
                    () -> merchantFraudService.getPage(1, 10));
            assertEquals(AnyTxnParameterRespCodeEnum.D_PAGE_AUTH_RULE_FAULT.getCode(), exception.getErrCode());
        }
    }
} 
