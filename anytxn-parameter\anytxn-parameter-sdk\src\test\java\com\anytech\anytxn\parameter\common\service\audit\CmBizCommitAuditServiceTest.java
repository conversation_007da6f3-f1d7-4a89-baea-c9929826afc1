package com.anytech.anytxn.parameter.common.service.audit;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.enums.AnyTxnCommonRespCodeEnum;
import com.anytech.anytxn.common.core.exception.AnyTxnCommonException;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.HttpServletRequestUtils;
import com.anytech.anytxn.common.core.utils.LoginUserUtils;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.SequenceIdGen;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.CmBizAuditRecordDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.CmBizCommitAuditDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.CmBizCommitAuditPageDTO;
import com.anytech.anytxn.parameter.base.common.domain.model.audit.CmBizCommitAudit;
import com.anytech.anytxn.parameter.base.common.domain.model.audit.CmBizCommitAuditExp;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.enums.AuthStatusEnum;
import com.anytech.anytxn.parameter.base.common.enums.TransactionTypeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.common.mapper.unicast.audit.CmBizCommitAuditMapper;
import com.anytech.anytxn.parameter.common.mapper.unicast.audit.CmBizCommitAuditSelfMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * CmBizCommitAuditService 单元测试类
 *
 * <AUTHOR> Assistant
 * @date 2025-06-26
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class CmBizCommitAuditServiceTest {

    @Mock
    private CmBizCommitAuditMapper cmBizCommitAuditMapper;

    @Mock
    private CmBizCommitAuditSelfMapper cmBizCommitAuditSelfMapper;

    @Mock
    private SequenceIdGen sequenceIdGen;

    @InjectMocks
    private CmBizCommitAuditServiceImpl cmBizCommitAuditService;

    private CmBizCommitAuditDTO cmBizCommitAuditDTO;
    private CmBizCommitAudit cmBizCommitAudit;
    private CmBizCommitAuditPageDTO cmBizCommitAuditPageDTO;
    private CmBizAuditRecordDTO cmBizAuditRecordDTO;

    @BeforeEach
    void setUp() {
        // 创建测试用的DTO对象
        try (MockedStatic<OrgNumberUtils> mockOrgNumberUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<LoginUserUtils> mockLoginUserUtils = mockStatic(LoginUserUtils.class)) {
            
            mockOrgNumberUtils.when(OrgNumberUtils::getOrg).thenReturn("1001");
            mockTenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            mockLoginUserUtils.when(LoginUserUtils::getLoginUserName).thenReturn("testUser");

            // 创建测试DTO对象
            cmBizCommitAuditDTO = new CmBizCommitAuditDTO();
            cmBizCommitAuditDTO.setId("audit001");
            cmBizCommitAuditDTO.setOrganizationNumber("1001");
            cmBizCommitAuditDTO.setDataDesc("测试审核描述");
            cmBizCommitAuditDTO.setJsonData("{\"test\":\"data\"}");
            cmBizCommitAuditDTO.setDataType("CREATE");
            cmBizCommitAuditDTO.setAuditStatus(AuthStatusEnum.WAIT_AUTH.getCode());
            cmBizCommitAuditDTO.setAuditResult("");
            cmBizCommitAuditDTO.setAuditRemark("");
            cmBizCommitAuditDTO.setUrl("/api/test");
            cmBizCommitAuditDTO.setApplicationBy("testUser");
            cmBizCommitAuditDTO.setApplicationTime(LocalDateTime.now());
            cmBizCommitAuditDTO.setAuditBy("testUser");
            cmBizCommitAuditDTO.setUpdateTime(LocalDateTime.now());
            cmBizCommitAuditDTO.setCardNumber("1234567890123456");
            cmBizCommitAuditDTO.setVersionNumber(1L);

            // 创建测试实体对象
            cmBizCommitAudit = new CmBizCommitAudit();
            cmBizCommitAudit.setId("audit001");
            cmBizCommitAudit.setOrganizationNumber("1001");
            cmBizCommitAudit.setDataDesc("测试审核描述");
            cmBizCommitAudit.setJsonData("{\"test\":\"data\"}");
            cmBizCommitAudit.setDataType("CREATE");
            cmBizCommitAudit.setAuditStatus(AuthStatusEnum.WAIT_AUTH.getCode());
            cmBizCommitAudit.setAuditResult("");
            cmBizCommitAudit.setAuditRemark("");
            cmBizCommitAudit.setUrl("/api/test");
            cmBizCommitAudit.setApplicationBy("testUser");
            cmBizCommitAudit.setApplicationTime(LocalDateTime.now());
            cmBizCommitAudit.setAuditBy("testUser");
            cmBizCommitAudit.setUpdateTime(LocalDateTime.now());
            cmBizCommitAudit.setCardNumber("1234567890123456");
            cmBizCommitAudit.setVersionNumber(1L);

            // 创建分页查询DTO对象
            cmBizCommitAuditPageDTO = new CmBizCommitAuditPageDTO();
            cmBizCommitAuditPageDTO.setPageNum(1);
            cmBizCommitAuditPageDTO.setPageSize(10);
            cmBizCommitAuditPageDTO.setDataType("CREATE");
            cmBizCommitAuditPageDTO.setDataDesc("测试");
            cmBizCommitAuditPageDTO.setApplicationBy("testUser");
            cmBizCommitAuditPageDTO.setAuditStatus(AuthStatusEnum.WAIT_AUTH.getCode());

            // 创建审核记录DTO对象
            cmBizAuditRecordDTO = new CmBizAuditRecordDTO();
            cmBizAuditRecordDTO.setObject(new TestObject());
            cmBizAuditRecordDTO.setCardNumber("1234567890123456");
            cmBizAuditRecordDTO.setTransactionTypeEnum(TransactionTypeEnum.TRANSACTION_ENTRY);
            cmBizAuditRecordDTO.setTransDesc("测试交易");
            cmBizAuditRecordDTO.setApplyUser("testUser");
        }
    }

    @Test
    void testInsertAuditRecord_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class);
             MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class)) {
            
            mockBeanMapping.when(() -> BeanMapping.copy(any(CmBizCommitAuditDTO.class), eq(CmBizCommitAudit.class)))
                           .thenReturn(cmBizCommitAudit);
            mockTenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            
            when(sequenceIdGen.generateId("tenant001")).thenReturn("audit001");
            when(cmBizCommitAuditMapper.insertSelective(any(CmBizCommitAudit.class))).thenReturn(1);

            // Act
            int result = cmBizCommitAuditService.insertAuditRecord(cmBizCommitAuditDTO);

            // Assert
            assertEquals(1, result);
            verify(cmBizCommitAuditMapper).insertSelective(any(CmBizCommitAudit.class));
            verify(sequenceIdGen).generateId("tenant001");
        }
    }

    @Test
    void testFindByPage_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {
            mockBeanMapping.when(() -> BeanMapping.copyList(anyList(), eq(CmBizCommitAuditDTO.class)))
                           .thenReturn(Arrays.asList(cmBizCommitAuditDTO));
            
            when(cmBizCommitAuditSelfMapper.selectAll(any(CmBizCommitAuditPageDTO.class)))
                    .thenReturn(Arrays.asList(cmBizCommitAudit));

            // Act
            PageResultDTO<CmBizCommitAuditDTO> result = cmBizCommitAuditService.findByPage(1, 10, cmBizCommitAuditPageDTO);

            // Assert
            assertNotNull(result);
            // Note: The actual page and rows values will depend on the service implementation
            assertNotNull(result.getData());
            assertEquals(1, result.getData().size());
            assertEquals("audit001", result.getData().get(0).getId());
        }
    }

    @Test
    void testFindByPage_EmptyResult() {
        // Arrange
        when(cmBizCommitAuditSelfMapper.selectAll(any(CmBizCommitAuditPageDTO.class)))
                .thenReturn(Collections.emptyList());

        // Act
        PageResultDTO<CmBizCommitAuditDTO> result = cmBizCommitAuditService.findByPage(1, 10, cmBizCommitAuditPageDTO);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getData());
        assertTrue(result.getData().isEmpty());
    }

    @Test
    void testAuditDetail_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {
            mockBeanMapping.when(() -> BeanMapping.copy(any(CmBizCommitAudit.class), eq(CmBizCommitAuditDTO.class)))
                           .thenReturn(cmBizCommitAuditDTO);
            
            when(cmBizCommitAuditMapper.selectByPrimaryKey("audit001")).thenReturn(cmBizCommitAudit);

            // Act
            CmBizCommitAuditDTO result = cmBizCommitAuditService.auditDetail("audit001");

            // Assert
            assertNotNull(result);
            assertEquals("audit001", result.getId());
            assertEquals("测试审核描述", result.getDataDesc());
        }
    }

    @Test
    void testAuditDetail_NotFound() {
        // Arrange
        when(cmBizCommitAuditMapper.selectByPrimaryKey("nonexistent")).thenReturn(null);

        // Act
        CmBizCommitAuditDTO result = cmBizCommitAuditService.auditDetail("nonexistent");

        // Assert
        assertNull(result);
    }

    @Test
    void testAuditHandle_Success_Approve() {
        // Arrange
        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class);
             MockedStatic<LoginUserUtils> mockLoginUserUtils = mockStatic(LoginUserUtils.class)) {
            
            mockBeanMapping.when(() -> BeanMapping.copy(any(CmBizCommitAudit.class), eq(CmBizCommitAuditExp.class)))
                           .thenReturn(new CmBizCommitAuditExp());
            mockLoginUserUtils.when(LoginUserUtils::getLoginUserName).thenReturn("auditor");
            
            cmBizCommitAudit.setAuditResult(AuthStatusEnum.WAIT_AUTH.getCode()); // 设置为待审核状态
            when(cmBizCommitAuditMapper.selectByPrimaryKey("audit001")).thenReturn(cmBizCommitAudit);
            when(cmBizCommitAuditMapper.updateByPrimaryKeySelectiveExp(any(CmBizCommitAuditExp.class))).thenReturn(1);

            // Act
            boolean result = cmBizCommitAuditService.auditHandle("audit001", AuthStatusEnum.HAS_AUTH.getCode(), "审核通过", 1L);

            // Assert
            assertTrue(result);
            verify(cmBizCommitAuditMapper).updateByPrimaryKeySelectiveExp(any(CmBizCommitAuditExp.class));
        }
    }

    @Test
    void testAuditHandle_Success_Reject() {
        // Arrange
        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class);
             MockedStatic<LoginUserUtils> mockLoginUserUtils = mockStatic(LoginUserUtils.class)) {
            
            mockBeanMapping.when(() -> BeanMapping.copy(any(CmBizCommitAudit.class), eq(CmBizCommitAuditExp.class)))
                           .thenReturn(new CmBizCommitAuditExp());
            mockLoginUserUtils.when(LoginUserUtils::getLoginUserName).thenReturn("auditor");
            
            cmBizCommitAudit.setAuditResult(AuthStatusEnum.WAIT_AUTH.getCode()); // 设置为待审核状态
            when(cmBizCommitAuditMapper.selectByPrimaryKey("audit001")).thenReturn(cmBizCommitAudit);
            when(cmBizCommitAuditMapper.updateByPrimaryKeySelectiveExp(any(CmBizCommitAuditExp.class))).thenReturn(1);

            // Act
            boolean result = cmBizCommitAuditService.auditHandle("audit001", "REJECT", "审核拒绝", 1L);

            // Assert
            assertTrue(result);
            verify(cmBizCommitAuditMapper).updateByPrimaryKeySelectiveExp(any(CmBizCommitAuditExp.class));
        }
    }

    @Test
    void testAuditHandle_EmptyId_ThrowsException() {
        // Act & Assert
        AnyTxnCommonException exception = assertThrows(AnyTxnCommonException.class, () -> {
            cmBizCommitAuditService.auditHandle("", "APPROVE", "测试", 1L);
        });
        assertEquals(AnyTxnCommonRespCodeEnum.D_ERR.getCode(), exception.getErrCode());
    }

    @Test
    void testAuditGetUrl_Success() {
        // Arrange
        when(cmBizCommitAuditMapper.selectByPrimaryKey("audit001")).thenReturn(cmBizCommitAudit);

        // Act
        String result = cmBizCommitAuditService.auditGetUrl("audit001");

        // Assert
        assertEquals("/api/test", result);
    }

    @Test
    void testAuditGetUrl_NotFound() {
        // Arrange
        when(cmBizCommitAuditMapper.selectByPrimaryKey("nonexistent")).thenReturn(null);

        // Act
        String result = cmBizCommitAuditService.auditGetUrl("nonexistent");

        // Assert
        assertNull(result);
    }

    @Test
    void testSelectByCardAndStatus_Success() {
        // Arrange
        List<String> cardNumbers = Arrays.asList("1234567890123456", "1234567890123457");
        
        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {
            mockBeanMapping.when(() -> BeanMapping.copyList(anyList(), eq(CmBizCommitAuditDTO.class)))
                           .thenReturn(Arrays.asList(cmBizCommitAuditDTO));
            
            when(cmBizCommitAuditSelfMapper.selectByCardAndStatus(cardNumbers, 
                    AuthStatusEnum.WAIT_AUTH.getCode(), AuthStatusEnum.AUTH_REJECT.getCode()))
                    .thenReturn(Arrays.asList(cmBizCommitAudit));

            // Act
            List<CmBizCommitAuditDTO> result = cmBizCommitAuditService.selectByCardAndStatus(
                    cardNumbers, AuthStatusEnum.WAIT_AUTH.getCode(), AuthStatusEnum.AUTH_REJECT.getCode());

            // Assert
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals("audit001", result.get(0).getId());
        }
    }

    @Test
    void testSelectByCardAndStatus_EmptyResult() {
        // Arrange
        List<String> cardNumbers = Arrays.asList("1234567890123456");
        when(cmBizCommitAuditSelfMapper.selectByCardAndStatus(cardNumbers, 
                AuthStatusEnum.WAIT_AUTH.getCode(), AuthStatusEnum.AUTH_REJECT.getCode()))
                .thenReturn(Collections.emptyList());

        // Act
        List<CmBizCommitAuditDTO> result = cmBizCommitAuditService.selectByCardAndStatus(
                cardNumbers, AuthStatusEnum.WAIT_AUTH.getCode(), AuthStatusEnum.AUTH_REJECT.getCode());

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testHandleFirstTransaction_FirstTime() {
        // Arrange
        TestObject testObject = new TestObject();
        testObject.commitAuditId = null; // 首次提交
        
        try (MockedStatic<OrgNumberUtils> mockOrgNumberUtils = mockStatic(OrgNumberUtils.class);
             MockedStatic<TenantUtils> mockTenantUtils = mockStatic(TenantUtils.class);
             MockedStatic<LoginUserUtils> mockLoginUserUtils = mockStatic(LoginUserUtils.class);
             MockedStatic<HttpServletRequestUtils> mockHttpServletRequestUtils = mockStatic(HttpServletRequestUtils.class);
             MockedStatic<JSON> mockJSON = mockStatic(JSON.class)) {
            
            mockOrgNumberUtils.when(OrgNumberUtils::getOrg).thenReturn("1001");
            mockTenantUtils.when(TenantUtils::getTenantId).thenReturn("tenant001");
            mockLoginUserUtils.when(LoginUserUtils::getLoginUserName).thenReturn("testUser");
            mockHttpServletRequestUtils.when(HttpServletRequestUtils::getRequestURI).thenReturn("/api/test");
            mockJSON.when(() -> JSON.toJSONString(any())).thenReturn("{\"test\":\"data\"}");
            
            cmBizAuditRecordDTO.setObject(testObject);
            when(sequenceIdGen.generateId("tenant001")).thenReturn("audit001");
            when(cmBizCommitAuditMapper.insertSelective(any(CmBizCommitAudit.class))).thenReturn(1);

            // Act
            String result = cmBizCommitAuditService.handleFirstTransaction(cmBizAuditRecordDTO);

            // Assert
            assertNull(result); // 首次提交返回null
            verify(cmBizCommitAuditMapper).insertSelective(any(CmBizCommitAudit.class));
        }
    }

    @Test
    void testHandleFirstTransaction_SecondTime() {
        // Arrange
        TestObject testObject = new TestObject();
        testObject.commitAuditId = "audit001"; // 非首次提交
        
        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {
            mockBeanMapping.when(() -> BeanMapping.copy(any(CmBizCommitAudit.class), eq(CmBizCommitAuditDTO.class)))
                           .thenReturn(cmBizCommitAuditDTO);
            
            cmBizAuditRecordDTO.setObject(testObject);
            when(cmBizCommitAuditMapper.selectByPrimaryKey("audit001")).thenReturn(cmBizCommitAudit);

            // Act
            String result = cmBizCommitAuditService.handleFirstTransaction(cmBizAuditRecordDTO);

            // Assert
            assertEquals("{\"test\":\"data\"}", result);
            verify(cmBizCommitAuditMapper).selectByPrimaryKey("audit001");
        }
    }

    @Test
    void testHandleFirstTransaction_NullObject_ThrowsException() {
        // Arrange
        cmBizAuditRecordDTO.setObject(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            cmBizCommitAuditService.handleFirstTransaction(cmBizAuditRecordDTO);
        });
        assertEquals(AnyTxnParameterRespCodeEnum.PARAMETER_RETURN_NULL.getCode(), exception.getErrCode());
    }

    // 辅助测试类
    private static class TestObject {
        public String commitAuditId;
    }
} 