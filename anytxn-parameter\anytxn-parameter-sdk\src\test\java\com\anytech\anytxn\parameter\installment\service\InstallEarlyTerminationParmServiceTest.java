package com.anytech.anytxn.parameter.installment.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallEarlyTerminationParmReqDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallEarlyTerminationParmResDTO;
import com.anytech.anytxn.parameter.base.installment.domain.model.InstallEarlyTerminationParm;
import com.anytech.anytxn.parameter.installment.mapper.InstallEarlyTerminationParmMapper;
import com.anytech.anytxn.parameter.installment.mapper.InstallEarlyTerminationParmSelfMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * InstallEarlyTerminationParmService 单元测试类
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class InstallEarlyTerminationParmServiceTest {

    @Mock
    private InstallEarlyTerminationParmMapper installEarlyTerminationParmMapper;
    
    @Mock
    private InstallEarlyTerminationParmSelfMapper installEarlyTerminationParmSelfMapper;
    
    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private InstallEarlyTerminationParmServiceImpl installEarlyTerminationParmService;

    private MockedStatic<OrgNumberUtils> orgNumberUtilsMockedStatic;
    private MockedStatic<TenantUtils> tenantUtilsMockedStatic;
    private MockedStatic<BeanMapping> beanMappingMockedStatic;
    private MockedStatic<PageHelper> pageHelperMockedStatic;

    @BeforeEach
    void setUp() {
        orgNumberUtilsMockedStatic = mockStatic(OrgNumberUtils.class);
        tenantUtilsMockedStatic = mockStatic(TenantUtils.class);
        beanMappingMockedStatic = mockStatic(BeanMapping.class);
        pageHelperMockedStatic = mockStatic(PageHelper.class);

        // 设置静态方法的默认返回值
        Mockito.lenient().when(OrgNumberUtils.getOrg()).thenReturn("0001");
        Mockito.lenient().when(TenantUtils.getTenantId()).thenReturn("6001");
    }

    @AfterEach
    void tearDown() {
        if (orgNumberUtilsMockedStatic != null) {
            orgNumberUtilsMockedStatic.close();
        }
        if (tenantUtilsMockedStatic != null) {
            tenantUtilsMockedStatic.close();
        }
        if (beanMappingMockedStatic != null) {
            beanMappingMockedStatic.close();
        }
        if (pageHelperMockedStatic != null) {
            pageHelperMockedStatic.close();
        }
    }

    /**
     * 测试添加分期提前终止参数 - 成功场景
     */
    @Test
    void testAdd_Success() {
        // Arrange
        InstallEarlyTerminationParmReqDTO reqDTO = createValidInstallEarlyTerminationParmReqDTO();
        InstallEarlyTerminationParm parm = createValidInstallEarlyTerminationParm();
        
        Mockito.lenient().when(installEarlyTerminationParmSelfMapper.isExists(reqDTO.getOrganizationNumber(), reqDTO.getTableId())).thenReturn(0);
        Mockito.lenient().when(numberIdGenerator.generateId(anyString())).thenReturn(123456789L);
        beanMappingMockedStatic.when(() -> BeanMapping.copy(reqDTO, InstallEarlyTerminationParm.class)).thenReturn(parm);

        // Act
        ParameterCompare result = installEarlyTerminationParmService.add(reqDTO);

        // Assert
        assertNotNull(result);
        verify(installEarlyTerminationParmSelfMapper).isExists(reqDTO.getOrganizationNumber(), reqDTO.getTableId());
    }

    /**
     * 测试添加分期提前终止参数 - 请求参数为空
     */
    @Test
    void testAdd_ReqDTONull() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> installEarlyTerminationParmService.add(null));
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_EARLY_TERMINATION_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试添加分期提前终止参数 - 数据已存在
     */
    @Test
    void testAdd_DataExists() {
        // Arrange
        InstallEarlyTerminationParmReqDTO reqDTO = createValidInstallEarlyTerminationParmReqDTO();
        
        Mockito.lenient().when(installEarlyTerminationParmSelfMapper.isExists(reqDTO.getOrganizationNumber(), reqDTO.getTableId())).thenReturn(1);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> installEarlyTerminationParmService.add(reqDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.D_EXIST_INSTALL_EARLY_TERMINATION_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试修改分期提前终止参数 - 成功场景
     */
    @Test
    void testModify_Success() {
        // Arrange
        InstallEarlyTerminationParmReqDTO reqDTO = createValidInstallEarlyTerminationParmReqDTO();
        InstallEarlyTerminationParm existingParm = createValidInstallEarlyTerminationParm();
        InstallEarlyTerminationParm updatedParm = createValidInstallEarlyTerminationParm();
        
        Mockito.lenient().when(installEarlyTerminationParmMapper.selectByPrimaryKey(reqDTO.getId())).thenReturn(existingParm);
        Mockito.lenient().when(installEarlyTerminationParmSelfMapper.isExists(reqDTO.getOrganizationNumber(), reqDTO.getTableId())).thenReturn(0);
        beanMappingMockedStatic.when(() -> BeanMapping.copy(reqDTO, InstallEarlyTerminationParm.class)).thenReturn(updatedParm);

        // Act
        ParameterCompare result = installEarlyTerminationParmService.modify(reqDTO);

        // Assert
        assertNotNull(result);
        verify(installEarlyTerminationParmMapper).selectByPrimaryKey(reqDTO.getId());
    }

    /**
     * 测试修改分期提前终止参数 - ID为空
     */
    @Test
    void testModify_IdNull() {
        // Arrange
        InstallEarlyTerminationParmReqDTO reqDTO = createValidInstallEarlyTerminationParmReqDTO();
        reqDTO.setId(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> installEarlyTerminationParmService.modify(reqDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试删除分期提前终止参数 - 成功场景
     */
    @Test
    void testRemove_Success() {
        // Arrange
        String id = "123456789";
        InstallEarlyTerminationParm parm = createValidInstallEarlyTerminationParm();
        
        Mockito.lenient().when(installEarlyTerminationParmMapper.selectByPrimaryKey(id)).thenReturn(parm);

        // Act
        ParameterCompare result = installEarlyTerminationParmService.remove(id);

        // Assert
        assertNotNull(result);
        verify(installEarlyTerminationParmMapper).selectByPrimaryKey(id);
    }

    /**
     * 测试删除分期提前终止参数 - ID为空
     */
    @Test
    void testRemove_IdNull() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> installEarlyTerminationParmService.remove(null));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试删除分期提前终止参数 - 数据不存在
     */
    @Test
    void testRemove_DataNotExists() {
        // Arrange
        String id = "123456789";
        
        Mockito.lenient().when(installEarlyTerminationParmMapper.selectByPrimaryKey(id)).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> installEarlyTerminationParmService.remove(id));
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_EARLY_TERMINATION_BY_ID_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 创建有效的InstallEarlyTerminationParmReqDTO对象
     */
    private InstallEarlyTerminationParmReqDTO createValidInstallEarlyTerminationParmReqDTO() {
        InstallEarlyTerminationParmReqDTO reqDTO = new InstallEarlyTerminationParmReqDTO();
        reqDTO.setId("123456789");
        reqDTO.setOrganizationNumber("0001");
        reqDTO.setTableId("T00001");
        reqDTO.setReturnFeeFlag("1");
        reqDTO.setPrepaymentFeeFlag("1");
        reqDTO.setPenaltyFlag("1");
        reqDTO.setPenaltyValue(new BigDecimal("100.00"));
        reqDTO.setForcePrepaymentFeeFlag("1");
        reqDTO.setForcePenaltyFlag("1");
        reqDTO.setForcePenaltyValue(new BigDecimal("200.00"));
        reqDTO.setDishonourFeeFlag("1");
        reqDTO.setStatus(Constants.ENABLED);
        reqDTO.setFeeDesc("Test Early Termination Fee");
        reqDTO.setReversalFeeFlag("1");
        reqDTO.setUpdateBy("TEST_USER");
        reqDTO.setVersionNumber(1L);
        return reqDTO;
    }

    /**
     * 创建有效的InstallEarlyTerminationParm对象
     */
    private InstallEarlyTerminationParm createValidInstallEarlyTerminationParm() {
        InstallEarlyTerminationParm parm = new InstallEarlyTerminationParm();
        parm.setId("123456789");
        parm.setOrganizationNumber("0001");
        parm.setTableId("T00001");
        parm.setReturnFeeFlag("1");
        parm.setPrepaymentFeeFlag("1");
        parm.setPenaltyFlag("1");
        parm.setPenaltyValue(new BigDecimal("100.00"));
        parm.setForcePrepaymentFeeFlag("1");
        parm.setForcePenaltyFlag("1");
        parm.setForcePenaltyValue(new BigDecimal("200.00"));
        parm.setDishonourFeeFlag("1");
        parm.setStatus(Constants.ENABLED);
        parm.setFeeDesc("Test Early Termination Fee");
        parm.setReversalFeeFlag("1");
        parm.setVersionNumber(1L);
        parm.setCreateTime(LocalDateTime.now());
        parm.setUpdateTime(LocalDateTime.now());
        parm.setUpdateBy("TEST_USER");
        return parm;
    }

    /**
     * 创建有效的InstallEarlyTerminationParmResDTO对象
     */
    private InstallEarlyTerminationParmResDTO createValidInstallEarlyTerminationParmResDTO() {
        InstallEarlyTerminationParmResDTO resDTO = new InstallEarlyTerminationParmResDTO();
        resDTO.setId("123456789");
        resDTO.setOrganizationNumber("0001");
        resDTO.setTableId("T00001");
        resDTO.setReturnFeeFlag("1");
        resDTO.setPrepaymentFeeFlag("1");
        resDTO.setPenaltyFlag("1");
        resDTO.setPenaltyValue(new BigDecimal("100.00"));
        resDTO.setForcePrepaymentFeeFlag("1");
        resDTO.setForcePenaltyFlag("1");
        resDTO.setForcePenaltyValue(new BigDecimal("200.00"));
        resDTO.setDishonourFeeFlag("1");
        resDTO.setStatus(Constants.ENABLED);
        resDTO.setFeeDesc("Test Early Termination Fee");
        resDTO.setReversalFeeFlag("1");
        resDTO.setVersionNumber(1L);
        resDTO.setCreateTime(LocalDateTime.now());
        resDTO.setUpdateTime(LocalDateTime.now());
        resDTO.setUpdateBy("TEST_USER");
        return resDTO;
    }

    /**
     * 测试根据ID查询分期提前终止参数 - 成功场景
     */
    @Test
    void testGetById_Success() {
        // Arrange
        String id = "123456789";
        InstallEarlyTerminationParm parm = createValidInstallEarlyTerminationParm();
        InstallEarlyTerminationParmResDTO expectedResult = createValidInstallEarlyTerminationParmResDTO();

        Mockito.lenient().when(installEarlyTerminationParmMapper.selectByPrimaryKey(id)).thenReturn(parm);
        beanMappingMockedStatic.when(() -> BeanMapping.copy(parm, InstallEarlyTerminationParmResDTO.class)).thenReturn(expectedResult);

        // Act
        InstallEarlyTerminationParmResDTO result = installEarlyTerminationParmService.getById(id);

        // Assert
        assertNotNull(result);
        assertEquals(expectedResult.getId(), result.getId());
        verify(installEarlyTerminationParmMapper).selectByPrimaryKey(id);
    }

    /**
     * 测试根据ID查询分期提前终止参数 - ID为空
     */
    @Test
    void testGetById_IdNull() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installEarlyTerminationParmService.getById(null));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试根据ID查询分期提前终止参数 - 数据不存在
     */
    @Test
    void testGetById_DataNotExists() {
        // Arrange
        String id = "123456789";

        Mockito.lenient().when(installEarlyTerminationParmMapper.selectByPrimaryKey(id)).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installEarlyTerminationParmService.getById(id));
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_EARLY_TERMINATION_BY_ID_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试分页查询分期提前终止参数 - 成功场景
     */
    @Test
    void testFindPage_Success() {
        // Arrange
        Integer pageNum = 1;
        Integer pageSize = 10;
        InstallEarlyTerminationParmReqDTO reqDTO = new InstallEarlyTerminationParmReqDTO();
        reqDTO.setOrganizationNumber("0001");

        List<InstallEarlyTerminationParm> parmList = Collections.singletonList(createValidInstallEarlyTerminationParm());
        List<InstallEarlyTerminationParmResDTO> expectedResults = Collections.singletonList(createValidInstallEarlyTerminationParmResDTO());
        Page<InstallEarlyTerminationParm> page = new Page<>(pageNum, pageSize);
        page.setTotal(1);
        page.setPages(1);

        pageHelperMockedStatic.when(() -> PageHelper.startPage(pageNum, pageSize)).thenReturn(page);
        Mockito.lenient().when(installEarlyTerminationParmSelfMapper.selectByCondition(any(InstallEarlyTerminationParmReqDTO.class))).thenReturn(parmList);
        beanMappingMockedStatic.when(() -> BeanMapping.copyList(parmList, InstallEarlyTerminationParmResDTO.class)).thenReturn(expectedResults);

        // Act
        PageResultDTO<InstallEarlyTerminationParmResDTO> result = installEarlyTerminationParmService.findPage(pageNum, pageSize, reqDTO);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getData().size());
        verify(installEarlyTerminationParmSelfMapper).selectByCondition(any(InstallEarlyTerminationParmReqDTO.class));
    }

    /**
     * 测试根据机构号和表ID查询分期提前终止参数 - 成功场景
     */
    @Test
    void testFindByIndex_Success() {
        // Arrange
        String organizationNumber = "0001";
        String tableId = "T00001";
        InstallEarlyTerminationParm parm = createValidInstallEarlyTerminationParm();
        InstallEarlyTerminationParmResDTO expectedResult = createValidInstallEarlyTerminationParmResDTO();

        Mockito.lenient().when(installEarlyTerminationParmSelfMapper.selectByIndex(organizationNumber, tableId)).thenReturn(parm);
        beanMappingMockedStatic.when(() -> BeanMapping.copy(parm, InstallEarlyTerminationParmResDTO.class)).thenReturn(expectedResult);

        // Act
        InstallEarlyTerminationParmResDTO result = installEarlyTerminationParmService.findByIndex(organizationNumber, tableId);

        // Assert
        assertNotNull(result);
        assertEquals(expectedResult.getId(), result.getId());
        verify(installEarlyTerminationParmSelfMapper).selectByIndex(organizationNumber, tableId);
    }

    /**
     * 测试根据机构号和表ID查询分期提前终止参数 - 机构号为空
     */
    @Test
    void testFindByIndex_OrganizationNumberEmpty() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installEarlyTerminationParmService.findByIndex("", "T00001"));
        assertEquals(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_ORG_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试根据机构号和表ID查询分期提前终止参数 - 表ID为空
     */
    @Test
    void testFindByIndex_TableIdEmpty() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installEarlyTerminationParmService.findByIndex("0001", ""));
        assertEquals(AnyTxnParameterRespCodeEnum.D_INSTALL_EARLY_TERMINATION_TABLE_ID_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试根据机构号和表ID查询分期提前终止参数 - 数据不存在
     */
    @Test
    void testFindByIndex_DataNotExists() {
        // Arrange
        String organizationNumber = "0001";
        String tableId = "T00001";

        Mockito.lenient().when(installEarlyTerminationParmSelfMapper.selectByIndex(organizationNumber, tableId)).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installEarlyTerminationParmService.findByIndex(organizationNumber, tableId));
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_EARLY_TERMINATION_FAULT.getCode(), exception.getErrCode());
    }
}
