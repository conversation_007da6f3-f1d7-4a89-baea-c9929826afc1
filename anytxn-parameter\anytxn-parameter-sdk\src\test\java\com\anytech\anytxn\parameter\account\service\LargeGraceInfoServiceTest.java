package com.anytech.anytxn.parameter.account.service;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.account.mapper.ParmLargeGraceInfoMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmLargeGraceInfoSelfMapper;
import com.anytech.anytxn.parameter.account.service.LargeGraceInfoServiceImpl;
import com.anytech.anytxn.parameter.base.account.domain.dto.LargeGraceInfoReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.LargeGraceInfoResDTO;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmLargeGraceInfo;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * LargeGraceInfoServiceTest 测试类
 * 测试超长免息期参数服务的核心功能
 * 
 * <AUTHOR>
 * @date 2025-06-27
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class LargeGraceInfoServiceTest {

    @Mock
    private ParmLargeGraceInfoSelfMapper parmLargeGraceInfoSelfMapper;
    
    @Mock
    private ParmLargeGraceInfoMapper parmLargeGraceInfoMapper;
    
    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private LargeGraceInfoServiceImpl largeGraceInfoService;

    private LargeGraceInfoReqDTO testReqDTO;
    private LargeGraceInfoResDTO testResDTO;
    private ParmLargeGraceInfo testEntity;

    @BeforeEach
    void setUp() {
        // 在@BeforeEach中使用try-with-resources确保OrgNumberUtils静态Mock生效
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class)) {
            
            orgNumberUtilsMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("tenant001");
            
            // 创建测试数据
            testReqDTO = new LargeGraceInfoReqDTO();
            testReqDTO.setId("1");
            testReqDTO.setTableId("LGRACE001");
            testReqDTO.setDelayedGraceCycle((short) 30);
            testReqDTO.setDescription("超长免息期参数测试");
            testReqDTO.setVersionNumber(1L);
            
            testResDTO = new LargeGraceInfoResDTO();
            testResDTO.setId("1");
            testResDTO.setTableId("LGRACE001");
            testResDTO.setDelayedGraceCycle((short) 30);
            testResDTO.setDescription("超长免息期参数测试");
            testResDTO.setVersionNumber(1L);
            testResDTO.setCreateTime(LocalDateTime.now());
            testResDTO.setUpdateTime(LocalDateTime.now());
            
            testEntity = new ParmLargeGraceInfo();
            testEntity.setId("1");
            testEntity.setOrganizationNumber("001");
            testEntity.setTableId("LGRACE001");
            testEntity.setDelayedGraceCycle((short) 30);
            testEntity.setDescription("超长免息期参数测试");
            testEntity.setVersionNumber(1L);
            testEntity.setCreateTime(LocalDateTime.now());
            testEntity.setUpdateTime(LocalDateTime.now());
        }
    }

    /**
     * 测试添加超长免息期参数 - 成功场景
     * 测试方法：LargeGraceInfoServiceImpl.add(LargeGraceInfoReqDTO)
     */
    @Test
    void testAdd_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class)) {
            
            tenantUtilsMock.when(TenantUtils::getTenantId).thenReturn("tenant001");
            when(parmLargeGraceInfoSelfMapper.selectByIndex(anyString(), anyString())).thenReturn(null);
            beanMappingMock.when(() -> BeanMapping.copy(any(LargeGraceInfoReqDTO.class), eq(ParmLargeGraceInfo.class)))
                    .thenReturn(testEntity);
            when(numberIdGenerator.generateId(anyString())).thenReturn(12345L);

            // Act
            ParameterCompare result = largeGraceInfoService.add(testReqDTO);

            // Assert
            assertThat(result).isNotNull();
            verify(parmLargeGraceInfoSelfMapper).selectByIndex("001", "LGRACE001");
            verify(numberIdGenerator).generateId("tenant001");
        }
    }

    /**
     * 测试添加超长免息期参数 - 数据已存在异常
     * 测试方法：LargeGraceInfoServiceImpl.add(LargeGraceInfoReqDTO)
     */
    @Test
    void testAdd_AlreadyExists_ThrowsException() {
        // Arrange
        when(parmLargeGraceInfoSelfMapper.selectByIndex(anyString(), anyString())).thenReturn(testEntity);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> largeGraceInfoService.add(testReqDTO));

        assertThat(exception.getErrCode()).isEqualTo(AnyTxnParameterRespCodeEnum.D_EXIST_PARM_LARGE_GRACE_INFO_FAULT.getCode());
        verify(parmLargeGraceInfoSelfMapper).selectByIndex("001", "LGRACE001");
    }

    /**
     * 测试修改超长免息期参数 - 成功场景
     * 测试方法：LargeGraceInfoServiceImpl.update(LargeGraceInfoReqDTO)
     */
    @Test
    void testUpdate_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            when(parmLargeGraceInfoMapper.selectByPrimaryKey("1")).thenReturn(testEntity);
            beanMappingMock.when(() -> BeanMapping.copy(any(LargeGraceInfoReqDTO.class), eq(ParmLargeGraceInfo.class)))
                    .thenReturn(testEntity);

            // Act
            ParameterCompare result = largeGraceInfoService.update(testReqDTO);

            // Assert
            assertThat(result).isNotNull();
            verify(parmLargeGraceInfoMapper).selectByPrimaryKey("1");
        }
    }

    /**
     * 测试修改超长免息期参数 - 数据不存在异常
     * 测试方法：LargeGraceInfoServiceImpl.update(LargeGraceInfoReqDTO)
     */
    @Test
    void testUpdate_DataNotExist_ThrowsException() {
        // Arrange
        when(parmLargeGraceInfoMapper.selectByPrimaryKey("1")).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
                () -> largeGraceInfoService.update(testReqDTO));

        assertThat(exception.getErrCode()).isEqualTo(AnyTxnParameterRespCodeEnum.D_QUERY_PARM_LARGE_GRACE_INFO_BY_ID_FAULT.getCode());
        verify(parmLargeGraceInfoMapper).selectByPrimaryKey("1");
    }

    /**
     * 测试删除超长免息期参数 - 成功场景
     * 测试方法：LargeGraceInfoServiceImpl.delete(String)
     */
    @Test
    void testDelete_Success() {
        // Arrange
        when(parmLargeGraceInfoMapper.selectByPrimaryKey("1")).thenReturn(testEntity);

        // Act
        ParameterCompare result = largeGraceInfoService.delete("1");

        // Assert
        assertThat(result).isNotNull();
        verify(parmLargeGraceInfoMapper).selectByPrimaryKey("1");
    }

    /**
     * 测试根据ID查询超长免息期参数 - 成功场景
     * 测试方法：LargeGraceInfoServiceImpl.findById(String)
     */
    @Test
    void testFindById_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            when(parmLargeGraceInfoMapper.selectByPrimaryKey("1")).thenReturn(testEntity);
            beanMappingMock.when(() -> BeanMapping.copy(any(ParmLargeGraceInfo.class), eq(LargeGraceInfoResDTO.class)))
                    .thenReturn(testResDTO);

            // Act
            LargeGraceInfoResDTO result = largeGraceInfoService.findById("1");

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getId()).isEqualTo("1");
            assertThat(result.getTableId()).isEqualTo("LGRACE001");
            verify(parmLargeGraceInfoMapper).selectByPrimaryKey("1");
        }
    }

    /**
     * 测试根据机构号和表ID查询超长免息期参数 - 成功场景
     * 测试方法：LargeGraceInfoServiceImpl.findByOrgAndTableId(String, String)
     */
    @Test
    void testFindByOrgAndTableId_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            when(parmLargeGraceInfoSelfMapper.selectByIndex("001", "LGRACE001")).thenReturn(testEntity);
            beanMappingMock.when(() -> BeanMapping.copy(any(ParmLargeGraceInfo.class), eq(LargeGraceInfoResDTO.class)))
                    .thenReturn(testResDTO);

            // Act
            LargeGraceInfoResDTO result = largeGraceInfoService.findByOrgAndTableId("001", "LGRACE001");

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getId()).isEqualTo("1");
            assertThat(result.getTableId()).isEqualTo("LGRACE001");
            verify(parmLargeGraceInfoSelfMapper).selectByIndex("001", "LGRACE001");
        }
    }

    /**
     * 测试根据机构号和表ID查询超长免息期参数 - 未找到数据
     * 测试方法：LargeGraceInfoServiceImpl.findByOrgAndTableId(String, String)
     */
    @Test
    void testFindByOrgAndTableId_NotFound() {
        // Arrange
        when(parmLargeGraceInfoSelfMapper.selectByIndex("001", "LGRACE001")).thenReturn(null);

        // Act
        LargeGraceInfoResDTO result = largeGraceInfoService.findByOrgAndTableId("001", "LGRACE001");

        // Assert
        assertThat(result).isNull();
        verify(parmLargeGraceInfoSelfMapper).selectByIndex("001", "LGRACE001");
    }
} 
