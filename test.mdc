---
description: 
globs: 
alwaysApply: false
---
所有涉及到单元测试用例的都按照下面的规范去执行：
你必须像一个追求“代码零缺陷”的测试工程师一样思考，并遵守以下所有铁律。在生成任何测试代码之前，请在“脑海”中完整地模拟被测方法的执行路径，并基于此设计测试用例。

第一部分：核心原则 (Must-Do)
操作范围铁律: 你的世界里只有 *Test.java 文件。绝对禁止以任何理由触碰或修改任何业务实现类的代码。

技术栈铁律:

只准使用纯 Mockito。严禁使用 @SpringBootTest。
测试类必须以 @ExtendWith(MockitoExtension.class) 开头。
依赖注入必须使用 @Mock 声明依赖，@InjectMocks 声明被测对象。
如果业务类太大，单元测试可以分成多个

质量与覆盖度铁律:

命名: 测试类命名为 被测类名 + Test。
全方法覆盖: 必须为被测类中的每一个 public 方法编写测试。
高深度覆盖 (冲击80%+): 必须为每个方法设计全面的测试用例。这意味着你需要：
路径分析: 深入理解被测方法的内部逻辑，识别所有可能的执行路径（例如，if/else 分支、switch 语句、循环体、异常抛出点）。每个路径都应至少有一个测试用例。
用例类型: 至少覆盖以下四种类型的场景，并确保能触发不同的代码路径：
成功路径 (Happy Path): 模拟所有输入合法、依赖正常返回、业务流程顺利完成的场景。这应覆盖最常见、最期望的执行流程。
非法参数 (Invalid Arguments): 针对所有方法参数，考虑 null、空字符串/集合、负数、超出范围的数值、不符合业务规则的枚举值等，验证方法是否能正确处理并抛出预期异常。
业务异常 (Business Exceptions): 模拟被测方法或其内部调用的依赖方法抛出业务自定义异常的场景，验证被测方法是否能正确捕获、处理或转发这些异常。
边界条件 (Boundary Conditions): 针对数值、集合大小、字符串长度等，测试其最小值、最大值，以及刚好在边界点的值（例如，0、1、Integer.MAX_VALUE、列表刚好为空、列表只有一个元素等）。
Mock 行为多样性: 不要只为依赖设置一种 thenReturn 行为。根据不同的测试用例，为同一个依赖方法设置不同的 Mock 返回值或抛出不同的异常，以驱动被测方法进入不同的执行路径。
第二部分：深度思考与交付前自检清单 (Deep Thought & Self-Checklist)
在你完成代码生成，准备把代码展示给我之前，你必须在你的“脑海”里，严格按照以下清单进行逐一检查。这是你交付代码的最后一道关卡。

[ ] 执行路径覆盖检查: 我是否为被测方法中的每一个 if/else 分支、switch 的每一个 case、循环的进入和退出条件、以及所有 try-catch 块的异常抛出和捕获路径都编写了对应的测试用例？我是否能清晰地在脑海中描绘出每个测试用例所覆盖的特定代码路径？

[ ] 依赖完整性检查: 我是否 Mock 了被测方法内部调用的所有外部依赖？一个都不能少，否则就是 NullPointerException。特别注意： 不仅是被测方法直接调用的依赖，其内部逻辑中可能通过传入参数调用的深层依赖也需要 Mock 到位。

[ ] Mock 行为正确性与多样性检查: 我是否为所有被调用的依赖方法都设定了正确的 Mock 行为（when...thenReturn 或 do...when）？并且，对于能够影响被测方法逻辑走向的依赖调用，我是否为它们设置了至少两种不同的 Mock 行为（例如，成功返回和抛出异常），以确保覆盖不同的业务逻辑分支？

[ ] 断言有效性检查: 我的测试是否遵循了 Arrange-Act-Assert 结构？断言是否有意义？我是否断言了：

返回值: 如果方法有返回值，是否验证了返回值的正确性？
状态变化: 如果方法会改变对象状态或传入参数的状态，是否验证了这些状态变化？
异常抛出: 如果预期方法会抛出异常，是否捕获并断言了异常的类型和消息？
依赖调用: 我是否使用了 verify 来验证被测方法内部是否正确调用了其依赖方法，以及调用次数和传入参数是否正确？这是确保业务逻辑正确性的关键。
[ ] 编译通过性检查: 我生成的代码是否存在任何 import 遗漏或语法错误？

第三部分：最终检验标准 (The Final Command)
你生成的代码的测试方法必须能够完美运行通过。

你的使命就是：通过严格执行第二部分的自检清单，并深刻理解每个测试用例如何触发不同的代码路径，来确保你生成的代码能够经受住第三部分这条命令的考验，并最终达到高覆盖率。 你的代码质量差、跑不通的现象，根源就在于违反了这些清单和最终目标。现在，严格执行它们。
必须保证交付的单元测试用例，没有报错，可以完全的跑通。