package com.anytech.anytxn.parameter.card.service;

import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.base.card.domain.dto.AnnualFeeTableResDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.AnnualFeeruleReqDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.AnnualFeeruleResDTO;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmAnnualFeeTable;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmAnnualFeerule;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardProductInfo;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmAnnualFeeTableSelfMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmAnnualFeeruleMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardProductInfoSelfMapper;
import com.anytech.anytxn.parameter.common.mapper.broadcast.ParmCommonSelfMapper;
import com.github.pagehelper.PageHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ParmAnnualFeeRuleServiceImpl unit test class
 */
@ExtendWith(MockitoExtension.class)
class ParmAnnualFeeRuleServiceTest {

    @Mock
    private Number16IdGen numberIdGenerator;

    @Mock
    private ParmCommonSelfMapper parmCommonSelfMapper;

    @Mock
    private ParmAnnualFeeruleMapper parmAnnualFeeruleMapper;

    @Mock
    private ParmCardProductInfoSelfMapper parmCardProductInfoSelfMapper;

    @Mock
    private ParmAnnualFeeTableSelfMapper annualFeeTableMapper;

    @InjectMocks
    private ParmAnnualFeeRuleServiceImpl parmAnnualFeeRuleService;

    private MockedStatic<OrgNumberUtils> orgNumberUtilsMockedStatic;
    private MockedStatic<TenantUtils> tenantUtilsMockedStatic;
    private MockedStatic<BeanMapping> beanMappingMockedStatic;
    private MockedStatic<PageHelper> pageHelperMockedStatic;

    @BeforeEach
    void setUp() {
        orgNumberUtilsMockedStatic = mockStatic(OrgNumberUtils.class);
        tenantUtilsMockedStatic = mockStatic(TenantUtils.class);
        beanMappingMockedStatic = mockStatic(BeanMapping.class);
        pageHelperMockedStatic = mockStatic(PageHelper.class);

        Mockito.lenient().when(numberIdGenerator.generateId(String.valueOf(anyLong()))).thenReturn(123456789L);
    }

    /**
     * Test add method - success case
     */
    @Test
    void testAdd_Success() {
        AnnualFeeruleReqDTO req = createValidAnnualFeeruleReqDTO();
        
        Mockito.lenient().when(parmCommonSelfMapper.isExists(
            eq("PARM_ANNUAL_FEE_RULE"), 
            eq("001"), 
            eq("TABLE_ID"), 
            eq("AFR001")
        )).thenReturn(0);
        
        Mockito.lenient().when(parmAnnualFeeruleMapper.selectByOneIndex(
            eq("001"), 
            eq("AFR001"), 
            eq("AF001")
        )).thenReturn(null);

        ParameterCompare result = parmAnnualFeeRuleService.add(req);

        assertNotNull(result);
        assertEquals("AFR001", result.getMainParmId());
        verify(parmCommonSelfMapper).isExists("PARM_ANNUAL_FEE_RULE", "001", "TABLE_ID", "AFR001");
        verify(parmAnnualFeeruleMapper).selectByOneIndex("001", "AFR001", "AF001");
    }

    /**
     * Test add method - empty annual fee table ids
     */
    @Test
    void testAdd_EmptyAnnualFeeTableIds() {
        AnnualFeeruleReqDTO req = createValidAnnualFeeruleReqDTO();
        req.setAnnualFeeTableIds(null);

        AnyTxnParameterException exception = assertThrows(
            AnyTxnParameterException.class,
            () -> parmAnnualFeeRuleService.add(req)
        );
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * Test add method - table id exists
     */
    @Test
    void testAdd_TableIdExists() {
        AnnualFeeruleReqDTO req = createValidAnnualFeeruleReqDTO();
        
        Mockito.lenient().when(parmCommonSelfMapper.isExists(
            eq("PARM_ANNUAL_FEE_RULE"), 
            eq("001"), 
            eq("TABLE_ID"), 
            eq("AFR001")
        )).thenReturn(1);

        AnyTxnParameterException exception = assertThrows(
            AnyTxnParameterException.class,
            () -> parmAnnualFeeRuleService.add(req)
        );
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_TABLE_ID_EXIST.getCode(), exception.getErrCode());
    }

    /**
     * Test add method - annual fee table id exists
     */
    @Test
    void testAdd_AnnualFeeTableIdExists() {
        AnnualFeeruleReqDTO req = createValidAnnualFeeruleReqDTO();
        ParmAnnualFeerule existingRule = createParmAnnualFeerule();
        
        Mockito.lenient().when(parmCommonSelfMapper.isExists(
            eq("PARM_ANNUAL_FEE_RULE"), 
            eq("001"), 
            eq("TABLE_ID"), 
            eq("AFR001")
        )).thenReturn(0);
        
        Mockito.lenient().when(parmAnnualFeeruleMapper.selectByOneIndex(
            eq("001"), 
            eq("AFR001"), 
            eq("AF001")
        )).thenReturn(existingRule);

        AnyTxnParameterException exception = assertThrows(
            AnyTxnParameterException.class,
            () -> parmAnnualFeeRuleService.add(req)
        );
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_ANNUAL_TABLE_ID_EXIST.getCode(), exception.getErrCode());
    }

    /**
     * Test modify method - success case
     */
    @Test
    void testModify_Success() {
        AnnualFeeruleReqDTO req = createValidAnnualFeeruleReqDTO();
        AnnualFeeruleResDTO existingResDTO = createAnnualFeeruleResDTO();
        AnnualFeeruleReqDTO copiedReqDTO = createValidAnnualFeeruleReqDTO();
        
        Mockito.lenient().when(parmCommonSelfMapper.isExists(
            eq("PARM_ANNUAL_FEE_RULE"), 
            eq("001"), 
            eq("TABLE_ID"), 
            eq("AFR001")
        )).thenReturn(1);

        // Mock findByOrgAndTableId method call which is called inside modify method
        List<ParmAnnualFeerule> existingRules = Arrays.asList(createParmAnnualFeerule());
        Mockito.lenient().when(parmAnnualFeeruleMapper.selectByTableId(eq("001"), eq("AFR001")))
            .thenReturn(existingRules);

        beanMappingMockedStatic.when(() -> BeanMapping.copy(any(ParmAnnualFeerule.class), eq(AnnualFeeruleResDTO.class)))
            .thenReturn(existingResDTO);
        
        beanMappingMockedStatic.when(() -> BeanMapping.copy(any(AnnualFeeruleResDTO.class), eq(AnnualFeeruleReqDTO.class)))
            .thenReturn(copiedReqDTO);


        ParameterCompare result = parmAnnualFeeRuleService.modify(req);

        assertNotNull(result);
        assertEquals("AFR001", result.getMainParmId());
        verify(parmCommonSelfMapper).isExists("PARM_ANNUAL_FEE_RULE", "001", "TABLE_ID", "AFR001");
    }

    /**
     * Test modify method - table id not exists
     */
    @Test
    void testModify_TableIdNotExists() {
        AnnualFeeruleReqDTO req = createValidAnnualFeeruleReqDTO();
        
        Mockito.lenient().when(parmCommonSelfMapper.isExists(
            eq("PARM_ANNUAL_FEE_RULE"), 
            eq("001"), 
            eq("TABLE_ID"), 
            eq("AFR001")
        )).thenReturn(0);

        AnyTxnParameterException exception = assertThrows(
            AnyTxnParameterException.class,
            () -> parmAnnualFeeRuleService.modify(req)
        );
        assertEquals(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * Test modify method - organization number empty
     */
    @Test
    void testModify_OrganizationNumberEmpty() {
        AnnualFeeruleReqDTO req = createValidAnnualFeeruleReqDTO();
        req.setOrganizationNumber("");
        
        Mockito.lenient().when(parmCommonSelfMapper.isExists(
            eq("PARM_ANNUAL_FEE_RULE"), 
            eq(""), 
            eq("TABLE_ID"), 
            eq("AFR001")
        )).thenReturn(1);

        AnyTxnParameterException exception = assertThrows(
            AnyTxnParameterException.class,
            () -> parmAnnualFeeRuleService.modify(req)
        );
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

//    /**
//     * Test findPage method - success case
//     */
//    @Test
//    void testFindPage_Success() {
//        Integer pageNum = 1;
//        Integer pageSize = 10;
//        String tableId = "AFR";
//        String description = "annual fee rule";
//        String organizationNumber = "001"; // Pass explicit org number to avoid OrgNumberUtils.getOrg() call
//
//        Page<ParmAnnualFeerule> mockPage = new Page<>(pageNum, pageSize);
//        mockPage.setTotal(2L);
//        mockPage.setPages(1);
//
//        List<ParmAnnualFeerule> tableIdList = Arrays.asList(createParmAnnualFeerule());
//        List<ParmAnnualFeerule> detailList = Arrays.asList(createParmAnnualFeerule());
//        List<AnnualFeeruleResDTO> resultList = Arrays.asList(createAnnualFeeruleResDTO());
//
//        // Configure static mocks for this test
//        pageHelperMockedStatic.when(() -> PageHelper.startPage(pageNum, pageSize))
//            .thenReturn(mockPage);
//
//        beanMappingMockedStatic.when(() -> BeanMapping.copyList(tableIdList, AnnualFeeruleResDTO.class))
//            .thenReturn(resultList);
//
//        Mockito.lenient().when(parmAnnualFeeruleMapper.selectTableIdsByCondition(
//            eq("AFR"), eq("annual fee rule"), eq("001")
//        )).thenReturn(tableIdList);
//
//        Mockito.lenient().when(parmAnnualFeeruleMapper.findByTableId(eq("AFR001"), eq("001")))
//            .thenReturn(detailList);
//
//        PageResultDTO<AnnualFeeruleResDTO> result = parmAnnualFeeRuleService.findPage(
//            pageNum, pageSize, tableId, description, organizationNumber
//        );
//
//        assertNotNull(result);
//        assertEquals(1, result.getPage());
//        assertEquals(10, result.getRows());
//        assertEquals(2L, result.getTotal());
//        assertEquals(1L, result.getTotalPage());
//        assertNotNull(result.getData());
//        assertEquals(1, result.getData().size());
//    }

    /**
     * Test findById method - success case
     */
    @Test
    void testFindById_Success() {
        String id = "123456789";
        ParmAnnualFeerule mockEntity = createParmAnnualFeerule();
        
        Mockito.lenient().when(parmAnnualFeeruleMapper.selectByPrimaryKey(eq(id)))
            .thenReturn(mockEntity);
        
        // BeanMapping.copy is void method that copies properties from source to target
        beanMappingMockedStatic.when(() -> BeanMapping.copy(eq(mockEntity), any(AnnualFeeruleResDTO.class)))
            .thenAnswer(invocation -> {
                AnnualFeeruleResDTO target = invocation.getArgument(1);
                target.setId(mockEntity.getId());
                target.setOrganizationNumber(mockEntity.getOrganizationNumber());
                target.setTableId(mockEntity.getTableId());
                target.setDescription(mockEntity.getDescription());
                target.setStatus(mockEntity.getStatus());
                target.setUpdateBy(mockEntity.getUpdateBy());
                target.setVersionNumber(mockEntity.getVersionNumber());
                target.setCreateTime(mockEntity.getCreateTime());
                target.setUpdateTime(mockEntity.getUpdateTime());
                return null;
            });

        AnnualFeeruleResDTO result = parmAnnualFeeRuleService.findById(id);

        assertNotNull(result);
        assertEquals("AFR001", result.getTableId());
        assertEquals("123456789", result.getId());
        verify(parmAnnualFeeruleMapper).selectByPrimaryKey(id);
    }

    /**
     * Test findById method - null id
     */
    @Test
    void testFindById_NullId() {
        AnyTxnParameterException exception = assertThrows(
            AnyTxnParameterException.class,
            () -> parmAnnualFeeRuleService.findById(null)
        );
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * Test findById method - data not exist
     */
    @Test
    void testFindById_DataNotExist() {
        String id = "123456789";
        
        Mockito.lenient().when(parmAnnualFeeruleMapper.selectByPrimaryKey(eq(id)))
            .thenReturn(null);

        AnyTxnParameterException exception = assertThrows(
            AnyTxnParameterException.class,
            () -> parmAnnualFeeRuleService.findById(id)
        );
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST.getCode(), exception.getErrCode());
    }

    /**
     * Test remove method - success case
     */
    @Test
    void testRemove_Success() {
        String id = "123456789";
        ParmAnnualFeerule mockEntity = createParmAnnualFeerule();
        
        Mockito.lenient().when(parmAnnualFeeruleMapper.selectByPrimaryKey(eq(id)))
            .thenReturn(mockEntity);
        Mockito.lenient().when(parmAnnualFeeruleMapper.deleteByPrimaryKey(eq(id)))
            .thenReturn(1);

        Boolean result = parmAnnualFeeRuleService.remove(id);

        assertTrue(result);
        verify(parmAnnualFeeruleMapper).selectByPrimaryKey(id);
        verify(parmAnnualFeeruleMapper).deleteByPrimaryKey(id);
    }

    /**
     * Test remove method - null id
     */
    @Test
    void testRemove_NullId() {
        AnyTxnParameterException exception = assertThrows(
            AnyTxnParameterException.class,
            () -> parmAnnualFeeRuleService.remove(null)
        );
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * Test remove method - data not exist
     */
    @Test
    void testRemove_DataNotExist() {
        String id = "123456789";
        
        Mockito.lenient().when(parmAnnualFeeruleMapper.selectByPrimaryKey(eq(id)))
            .thenReturn(null);

        AnyTxnParameterException exception = assertThrows(
            AnyTxnParameterException.class,
            () -> parmAnnualFeeRuleService.remove(id)
        );
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST.getCode(), exception.getErrCode());
    }

    /**
     * Test findByOrgAndProductNum method - success case
     */
    @Test
    void testFindByOrgAndProductNum_Success() {
        String orgNumber = "001";
        String productNumber = "PROD001";
        
        ParmCardProductInfo productInfo = createParmCardProductInfo();
        List<ParmAnnualFeerule> ruleList = Arrays.asList(createParmAnnualFeerule());
        ParmAnnualFeeTable feeTable = createParmAnnualFeeTable();
        List<AnnualFeeTableResDTO> expectedResult = Arrays.asList(createAnnualFeeTableResDTO());
        
        Mockito.lenient().when(parmCardProductInfoSelfMapper.selectOrgNumberAndProNumber(eq(orgNumber), eq(productNumber)))
            .thenReturn(productInfo);
        Mockito.lenient().when(parmAnnualFeeruleMapper.selectByTableId(eq(orgNumber), eq("AFR001")))
            .thenReturn(ruleList);
        Mockito.lenient().when(annualFeeTableMapper.selectByOrgAndTid(eq("001"), eq("AF001")))
            .thenReturn(feeTable);
        
        beanMappingMockedStatic.when(() -> BeanMapping.copyList(anyList(), eq(AnnualFeeTableResDTO.class)))
            .thenReturn(expectedResult);

        List<AnnualFeeTableResDTO> result = parmAnnualFeeRuleService.findByOrgAndProductNum(orgNumber, productNumber);

        assertNotNull(result);
        assertEquals(1, result.size());
        verify(parmCardProductInfoSelfMapper).selectOrgNumberAndProNumber(orgNumber, productNumber);
    }

    /**
     * Test findByOrgAndProductNum method - empty product number
     */
    @Test
    void testFindByOrgAndProductNum_EmptyProductNumber() {
        AnyTxnParameterException exception = assertThrows(
            AnyTxnParameterException.class,
            () -> parmAnnualFeeRuleService.findByOrgAndProductNum("001", "")
        );
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * Test findFeeRuleByOrgAndProductNum method - success case
     */
    @Test
    void testFindFeeRuleByOrgAndProductNum_Success() {
        String orgNumber = "001";
        String productNumber = "PROD001";
        
        ParmCardProductInfo productInfo = createParmCardProductInfo();
        List<ParmAnnualFeerule> ruleList = Arrays.asList(createParmAnnualFeerule());
        List<AnnualFeeruleResDTO> expectedResult = Arrays.asList(createAnnualFeeruleResDTO());
        
        Mockito.lenient().when(parmCardProductInfoSelfMapper.selectOrgNumberAndProNumber(eq(orgNumber), eq(productNumber)))
            .thenReturn(productInfo);
        Mockito.lenient().when(parmAnnualFeeruleMapper.selectByTableId(eq(orgNumber), eq("AFR001")))
            .thenReturn(ruleList);
        
        beanMappingMockedStatic.when(() -> BeanMapping.copyList(eq(ruleList), eq(AnnualFeeruleResDTO.class)))
            .thenReturn(expectedResult);

        List<AnnualFeeruleResDTO> result = parmAnnualFeeRuleService.findFeeRuleByOrgAndProductNum(orgNumber, productNumber);

        assertNotNull(result);
        assertEquals(1, result.size());
        verify(parmCardProductInfoSelfMapper).selectOrgNumberAndProNumber(orgNumber, productNumber);
    }

    /**
     * Test findByOrgAndTableId method - success case
     */
    @Test
    void testFindByOrgAndTableId_Success() {
        String orgNumber = "001";
        String tableId = "AFR001";
        
        List<ParmAnnualFeerule> ruleList = Arrays.asList(createParmAnnualFeerule());
        AnnualFeeruleResDTO expectedResult = createAnnualFeeruleResDTO();
        
        Mockito.lenient().when(parmAnnualFeeruleMapper.selectByTableId(eq(orgNumber), eq(tableId)))
            .thenReturn(ruleList);
        
        beanMappingMockedStatic.when(() -> BeanMapping.copy(any(ParmAnnualFeerule.class), eq(AnnualFeeruleResDTO.class)))
            .thenReturn(expectedResult);

        AnnualFeeruleResDTO result = parmAnnualFeeRuleService.findByOrgAndTableId(orgNumber, tableId);

        assertNotNull(result);
        assertEquals("AFR001", result.getTableId());
        verify(parmAnnualFeeruleMapper).selectByTableId(orgNumber, tableId);
    }

    /**
     * Test findByOrgAndTableId method - empty table id
     */
    @Test
    void testFindByOrgAndTableId_EmptyTableId() {
        AnyTxnParameterException exception = assertThrows(
            AnyTxnParameterException.class,
            () -> parmAnnualFeeRuleService.findByOrgAndTableId("001", "")
        );
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * Test removeByOrgNumAndTableId method - success case
     */
    @Test
    void testRemoveByOrgNumAndTableId_Success() {
        String orgNumber = "001";
        String tableId = "AFR001";
        
        List<ParmAnnualFeerule> existingRules = Arrays.asList(createParmAnnualFeerule());
        
        Mockito.lenient().when(parmAnnualFeeruleMapper.selectByTableId(eq(orgNumber), eq(tableId)))
            .thenReturn(existingRules);

        ParameterCompare result = parmAnnualFeeRuleService.removeByOrgNumAndTableId(orgNumber, tableId);

        assertNotNull(result);
        assertEquals(tableId, result.getMainParmId());
        verify(parmAnnualFeeruleMapper).selectByTableId(orgNumber, tableId);
    }

    /**
     * Test removeByOrgNumAndTableId method - null parameters
     */
    @Test
    void testRemoveByOrgNumAndTableId_NullParameters() {
        AnyTxnParameterException exception = assertThrows(
            AnyTxnParameterException.class,
            () -> parmAnnualFeeRuleService.removeByOrgNumAndTableId(null, "AFR001")
        );
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * Test removeByOrgNumAndTableId method - data not exist
     */
    @Test
    void testRemoveByOrgNumAndTableId_DataNotExist() {
        String orgNumber = "001";
        String tableId = "AFR001";
        
        Mockito.lenient().when(parmAnnualFeeruleMapper.selectByTableId(eq(orgNumber), eq(tableId)))
            .thenReturn(Collections.emptyList());

        AnyTxnParameterException exception = assertThrows(
            AnyTxnParameterException.class,
            () -> parmAnnualFeeRuleService.removeByOrgNumAndTableId(orgNumber, tableId)
        );
        assertEquals(AnyTxnParameterRespCodeEnum.D_DATA_NOT_EXISTS_FAULT.getCode(), exception.getErrCode());
    }

    private AnnualFeeruleReqDTO createValidAnnualFeeruleReqDTO() {
        AnnualFeeruleReqDTO req = new AnnualFeeruleReqDTO();
        req.setOrganizationNumber("001");
        req.setTableId("AFR001");
        req.setDescription("test annual fee rule");
        req.setStatus("1");
        req.setAnnualFeeTableIds(Arrays.asList("AF001", "AF002"));
        req.setUpdateBy("testUser");
        req.setVersionNumber(1L);
        req.setCreateTime(LocalDateTime.now());
        req.setUpdateTime(LocalDateTime.now());
        return req;
    }

    private AnnualFeeruleResDTO createAnnualFeeruleResDTO() {
        AnnualFeeruleResDTO res = new AnnualFeeruleResDTO();
        res.setId("123456789");
        res.setOrganizationNumber("001");
        res.setTableId("AFR001");
        res.setDescription("test annual fee rule");
        res.setStatus("1");
        res.setAnnualFeeTableIds(Arrays.asList("AF001", "AF002"));
        res.setUpdateBy("testUser");
        res.setVersionNumber(1L);
        res.setCreateTime(LocalDateTime.now());
        res.setUpdateTime(LocalDateTime.now());
        return res;
    }

    private ParmAnnualFeerule createParmAnnualFeerule() {
        ParmAnnualFeerule entity = new ParmAnnualFeerule();
        entity.setId("123456789");
        entity.setOrganizationNumber("001");
        entity.setTableId("AFR001");
        entity.setDescription("test annual fee rule");
        entity.setStatus("1");
        entity.setAnnualFeeTableId("AF001");
        entity.setUpdateBy("testUser");
        entity.setVersionNumber(1L);
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());
        return entity;
    }

    private ParmCardProductInfo createParmCardProductInfo() {
        ParmCardProductInfo productInfo = new ParmCardProductInfo();
        productInfo.setOrganizationNumber("001");
        productInfo.setProductNumber("PROD001");
        productInfo.setAnnualFeeRuleTableId("AFR001");
        return productInfo;
    }

    private ParmAnnualFeeTable createParmAnnualFeeTable() {
        ParmAnnualFeeTable feeTable = new ParmAnnualFeeTable();
        feeTable.setId("123456789");
        feeTable.setOrganizationNumber("001");
        feeTable.setTableId("AF001");
        feeTable.setDescription("annual fee table");
        feeTable.setStatus("1");
        return feeTable;
    }

    private AnnualFeeTableResDTO createAnnualFeeTableResDTO() {
        AnnualFeeTableResDTO dto = new AnnualFeeTableResDTO();
        dto.setId("123456789");
        dto.setOrganizationNumber("001");
        dto.setTableId("AF001");
        dto.setDescription("annual fee table");
        dto.setStatus("1");
        return dto;
    }

    @org.junit.jupiter.api.AfterEach
    void tearDown() {
        if (orgNumberUtilsMockedStatic != null) {
            orgNumberUtilsMockedStatic.close();
        }
        if (tenantUtilsMockedStatic != null) {
            tenantUtilsMockedStatic.close();
        }
        if (beanMappingMockedStatic != null) {
            beanMappingMockedStatic.close();
        }
        if (pageHelperMockedStatic != null) {
            pageHelperMockedStatic.close();
        }
    }
}