package com.anytech.anytxn.parameter.card.service;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.core.utils.MessageSourceUtils;
import com.anytech.anytxn.common.core.enums.MessageLanguageEnum;
import com.anytech.anytxn.parameter.base.card.domain.dto.AnnualFeeTableReqDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.AnnualFeeTableResDTO;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmAnnualFeeTable;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmAnnualFeeTableMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmAnnualFeeTableSelfMapper;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.util.StringUtils;

import java.util.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;

@ExtendWith(MockitoExtension.class)
class AnnualFeeTableServiceTest {
    @InjectMocks
    private AnnualFeeTableServiceImpl service;
    @Mock
    private ParmAnnualFeeTableMapper parmAnnualFeeTableMapper;
    @Mock
    private ParmAnnualFeeTableSelfMapper parmAnnualFeeTableSelfMapper;
    @Mock
    private Number16IdGen numberIdGenerator;
    private MockedStatic<BeanMapping> beanMappingMockedStatic;
    private MockedStatic<OrgNumberUtils> orgNumberUtilsMockedStatic;
    private MockedStatic<TenantUtils> tenantUtilsMockedStatic;
    private MockedStatic<PageHelper> pageHelperMockedStatic;
    private MockedStatic<StringUtils> stringUtilsMockedStatic;
    private MockedStatic<MessageSourceUtils> messageSourceUtilsMockedStatic;

    @BeforeEach
    void setUp() {
        beanMappingMockedStatic = Mockito.mockStatic(BeanMapping.class);
        orgNumberUtilsMockedStatic = Mockito.mockStatic(OrgNumberUtils.class);
        tenantUtilsMockedStatic = Mockito.mockStatic(TenantUtils.class);
        pageHelperMockedStatic = Mockito.mockStatic(PageHelper.class);
        stringUtilsMockedStatic = Mockito.mockStatic(StringUtils.class);
        messageSourceUtilsMockedStatic = Mockito.mockStatic(MessageSourceUtils.class);
        // mock静态国际化和消息
        messageSourceUtilsMockedStatic.when(() -> MessageSourceUtils.requestLanguage()).thenReturn(MessageLanguageEnum.CN);
    }

    @AfterEach
    void tearDown() {
        beanMappingMockedStatic.close();
        orgNumberUtilsMockedStatic.close();
        tenantUtilsMockedStatic.close();
        pageHelperMockedStatic.close();
        stringUtilsMockedStatic.close();
        messageSourceUtilsMockedStatic.close();
    }

    // 1. findAll - happy path
    @Test
    void testFindAll_HappyPath() {
        stringUtilsMockedStatic.when(() -> StringUtils.isEmpty("100")).thenReturn(false);
        stringUtilsMockedStatic.when(() -> StringUtils.isEmpty("org")).thenReturn(false);
        pageHelperMockedStatic.when(() -> PageHelper.startPage(anyInt(), anyInt())).thenReturn(Mockito.mock(Page.class));
        List<ParmAnnualFeeTable> list = Collections.singletonList(new ParmAnnualFeeTable());
        Mockito.lenient().when(parmAnnualFeeTableSelfMapper.selectByCondition(any())).thenReturn(list);
        List<AnnualFeeTableResDTO> resList = Collections.singletonList(new AnnualFeeTableResDTO());
        beanMappingMockedStatic.when(() -> BeanMapping.copyList(Mockito.any(), Mockito.any())).thenReturn(resList);
        PageResultDTO<AnnualFeeTableResDTO> result = service.findAll(1, 10, "tid", "desc", "1", "100", "org");
        assertNotNull(result);
        assertEquals(resList, result.getData());
    }

    // 2. findAll - fixedAmnt非法
    @Test
    void testFindAll_FixedAmntError() {
        stringUtilsMockedStatic.when(() -> StringUtils.isEmpty("notANumber")).thenReturn(false);
        stringUtilsMockedStatic.when(() -> StringUtils.isEmpty("org")).thenReturn(false);
        assertThrows(AnyTxnParameterException.class, () -> service.findAll(1, 10, null, null, null, "notANumber", "org"));
    }

    // 3. findAll - organizationNumber为空
    @Test
    void testFindAll_OrgNumberNull() {
        stringUtilsMockedStatic.when(() -> StringUtils.isEmpty(null)).thenReturn(true);
        orgNumberUtilsMockedStatic.when(OrgNumberUtils::getOrg).thenReturn("ORG001");
        pageHelperMockedStatic.when(() -> PageHelper.startPage(anyInt(), anyInt())).thenReturn(Mockito.mock(Page.class));
        Mockito.lenient().when(parmAnnualFeeTableSelfMapper.selectByCondition(any())).thenReturn(Collections.emptyList());
        beanMappingMockedStatic.when(() -> BeanMapping.copyList(Mockito.any(), Mockito.any())).thenReturn(Collections.emptyList());
        PageResultDTO<AnnualFeeTableResDTO> result = service.findAll(1, 10, null, null, null, null, null);
        assertNotNull(result);
    }

    // 4. addParmFee - happy path
    @Test
    void testAddParmFee_HappyPath() {
        AnnualFeeTableReqDTO req = new AnnualFeeTableReqDTO();
        req.setOrganizationNumber("org");
        req.setTableId("tid");
        Mockito.lenient().when(parmAnnualFeeTableSelfMapper.selectByOrgAndTid(any(), any())).thenReturn(null);
        ParmAnnualFeeTable entity = new ParmAnnualFeeTable();
        beanMappingMockedStatic.when(() -> BeanMapping.copy(any(), eq(ParmAnnualFeeTable.class))).thenReturn(entity);
        tenantUtilsMockedStatic.when(TenantUtils::getTenantId).thenReturn("tenant");
        Mockito.lenient().when(numberIdGenerator.generateId(any())).thenReturn(123L);
        ParameterCompare compare = ParameterCompare.getBuilder().withAfter(entity).build(ParmAnnualFeeTable.class);
        // 这里直接调用真实方法即可
        ParameterCompare result = service.addParmFee(req);
        assertNotNull(result);
    }

    // 5. addParmFee - 已存在
    @Test
    void testAddParmFee_Exist() {
        AnnualFeeTableReqDTO req = new AnnualFeeTableReqDTO();
        req.setOrganizationNumber("org");
        req.setTableId("tid");
        Mockito.lenient().when(parmAnnualFeeTableSelfMapper.selectByOrgAndTid(any(), any())).thenReturn(new ParmAnnualFeeTable());
        assertThrows(AnyTxnParameterException.class, () -> service.addParmFee(req));
    }

    // 6. modifyParmFee - happy path
    @Test
    void testModifyParmFee_HappyPath() {
        AnnualFeeTableReqDTO req = new AnnualFeeTableReqDTO();
        req.setId("id");
        req.setOrganizationNumber("org");
        req.setTableId("tid");
        ParmAnnualFeeTable oldEntity = new ParmAnnualFeeTable();
        oldEntity.setOrganizationNumber("org");
        oldEntity.setTableId("tid");
        Mockito.lenient().when(parmAnnualFeeTableMapper.selectByPrimaryKey(any())).thenReturn(oldEntity);
        beanMappingMockedStatic.when(() -> BeanMapping.copy(any(), eq(ParmAnnualFeeTable.class))).thenReturn(new ParmAnnualFeeTable());
        ParameterCompare compare = ParameterCompare.getBuilder().withAfter(new ParmAnnualFeeTable()).withBefore(oldEntity).build(ParmAnnualFeeTable.class);
        ParameterCompare result = service.modifyParmFee(req);
        assertNotNull(result);
    }

    // 7. modifyParmFee - id查不到
    @Test
    void testModifyParmFee_NotFound() {
        AnnualFeeTableReqDTO req = new AnnualFeeTableReqDTO();
        req.setId("id");
        Mockito.lenient().when(parmAnnualFeeTableMapper.selectByPrimaryKey(any())).thenReturn(null);
        assertThrows(AnyTxnParameterException.class, () -> service.modifyParmFee(req));
    }

    // 8. modifyParmFee - 唯一性冲突
    @Test
    void testModifyParmFee_UniqueConflict() {
        AnnualFeeTableReqDTO req = new AnnualFeeTableReqDTO();
        req.setId("id");
        req.setOrganizationNumber("org2");
        req.setTableId("tid2");
        ParmAnnualFeeTable oldEntity = new ParmAnnualFeeTable();
        oldEntity.setOrganizationNumber("org1");
        oldEntity.setTableId("tid1");
        Mockito.lenient().when(parmAnnualFeeTableMapper.selectByPrimaryKey(any())).thenReturn(oldEntity);
        Mockito.lenient().when(parmAnnualFeeTableSelfMapper.selectByOrgAndTid(any(), any())).thenReturn(new ParmAnnualFeeTable());
        assertThrows(AnyTxnParameterException.class, () -> service.modifyParmFee(req));
    }

    // 9. removeParmFee - happy path
    @Test
    void testRemoveParmFee_HappyPath() {
        ParmAnnualFeeTable entity = new ParmAnnualFeeTable();
        Mockito.lenient().when(parmAnnualFeeTableMapper.selectByPrimaryKey(any())).thenReturn(entity);
        ParameterCompare compare = ParameterCompare.getBuilder().withBefore(entity).build(ParmAnnualFeeTable.class);
        ParameterCompare result = service.removeParmFee("id");
        assertNotNull(result);
    }

    // 10. removeParmFee - id查不到
    @Test
    void testRemoveParmFee_NotFound() {
        Mockito.lenient().when(parmAnnualFeeTableMapper.selectByPrimaryKey(any())).thenReturn(null);
        assertThrows(AnyTxnParameterException.class, () -> service.removeParmFee("id"));
    }

    // 11. findById - happy path
    @Test
    void testFindById_HappyPath() {
        ParmAnnualFeeTable entity = new ParmAnnualFeeTable();
        Mockito.lenient().when(parmAnnualFeeTableMapper.selectByPrimaryKey(any())).thenReturn(entity);
        AnnualFeeTableResDTO dto = new AnnualFeeTableResDTO();
        beanMappingMockedStatic.when(() -> BeanMapping.copy(entity, AnnualFeeTableResDTO.class)).thenReturn(dto);
        AnnualFeeTableResDTO result = service.findById("id");
        assertNotNull(result);
    }

    // 12. findById - id为空
    @Test
    void testFindById_IdNull() {
        assertThrows(AnyTxnParameterException.class, () -> service.findById(null));
    }

    // 13. findByOrgAndTableId - happy path
    @Test
    void testFindByOrgAndTableId_HappyPath() {
        ParmAnnualFeeTable entity = new ParmAnnualFeeTable();
        Mockito.lenient().when(parmAnnualFeeTableSelfMapper.selectByOrgAndTid(any(), any())).thenReturn(entity);
        AnnualFeeTableResDTO dto = new AnnualFeeTableResDTO();
        beanMappingMockedStatic.when(() -> BeanMapping.copy(entity, AnnualFeeTableResDTO.class)).thenReturn(dto);
        AnnualFeeTableResDTO result = service.findByOrgAndTableId("org", "tid");
        assertNotNull(result);
    }

    // 14. findByOrgAndTableId - 查不到
    @Test
    void testFindByOrgAndTableId_NotFound() {
        Mockito.lenient().when(parmAnnualFeeTableSelfMapper.selectByOrgAndTid(any(), any())).thenReturn(null);
        assertThrows(AnyTxnParameterException.class, () -> service.findByOrgAndTableId("org", "tid"));
    }
} 