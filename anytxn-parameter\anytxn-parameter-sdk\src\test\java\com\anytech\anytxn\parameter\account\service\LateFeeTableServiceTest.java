package com.anytech.anytxn.parameter.account.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.parameter.account.service.LateFeeTableServiceImpl;
import com.anytech.anytxn.parameter.account.mapper.ParmLateFeeTableMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmLateFeeTableSelfMapper;
import com.anytech.anytxn.parameter.base.account.domain.dto.LateFeeTableReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.LateFeeTableResDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.LateFeeTableSearchDTO;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmLateFeeTable;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.enums.ParameterRepDetailEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * LateFeeTableServiceImpl 单元测试类
 * 
 * 测试 LateFeeTableServiceImpl 的所有公共方法：
 * 1. findAll(Integer pageNum, Integer pageSize, LateFeeTableSearchDTO lateFeeTableSearchDTO) - 分页查询违约金参数
 * 2. addParmFee(LateFeeTableReqDTO lateFeeTableReq) - 添加违约金参数
 * 3. modifyParmFee(LateFeeTableReqDTO lateFeeTableReq) - 修改违约金参数
 * 4. removeParmFee(String id) - 删除违约金参数
 * 5. findById(String id) - 根据ID查询违约金参数
 * 6. findByOrgAndTableId(String orgNum, String tableId) - 根据机构号和表ID查询违约金参数
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class LateFeeTableServiceTest {

    @Mock
    private ParmLateFeeTableMapper parmLateFeeTableMapper;

    @Mock
    private ParmLateFeeTableSelfMapper parmLateFeeTableSelfMapper;

    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private LateFeeTableServiceImpl lateFeeTableService;

    private LateFeeTableReqDTO testReqDTO;
    private LateFeeTableResDTO testResDTO;
    private ParmLateFeeTable testEntity;
    private LateFeeTableSearchDTO testSearchDTO;

    @BeforeEach
    void setUp() {
        // 设置测试数据 - 注意在setUp中处理OrgNumberUtils静态初始化问题
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(() -> OrgNumberUtils.getOrg()).thenReturn("001");
            orgNumberUtilsMock.when(() -> OrgNumberUtils.getOrg("001")).thenReturn("001");
            
            testReqDTO = new LateFeeTableReqDTO();
            testReqDTO.setId("TEST_ID_001");
            testReqDTO.setOrganizationNumber("001");
            testReqDTO.setTableId("TABLE_001");
            testReqDTO.setDescription("测试违约金参数");
            testReqDTO.setChargeFlag("1");
            testReqDTO.setCalculateMethod("1");
            testReqDTO.setChargeOption("1");
            testReqDTO.setChargePercent(new BigDecimal("0.05"));
            testReqDTO.setFixedAmnt(new BigDecimal("100.00"));
            testReqDTO.setGenerateInterestOption("0");
            testReqDTO.setMaxAmnt(new BigDecimal("500.00"));
            testReqDTO.setMinAmnt(new BigDecimal("10.00"));
            testReqDTO.setTxnCode("61000");
            testReqDTO.setStatus("1");
            testReqDTO.setVersionNumber(1L);

            testResDTO = new LateFeeTableResDTO();
            testResDTO.setId("TEST_ID_001");
            testResDTO.setOrganizationNumber("001");
            testResDTO.setTableId("TABLE_001");
            testResDTO.setDescription("测试违约金参数");
            testResDTO.setChargeFlag("1");
            testResDTO.setCalculateMethod("1");
            testResDTO.setChargeOption("1");
            testResDTO.setChargePercent(new BigDecimal("0.05"));
            testResDTO.setFixedAmnt(new BigDecimal("100.00"));
            testResDTO.setGenerateInterestOption("0");
            testResDTO.setMaxAmnt(new BigDecimal("500.00"));
            testResDTO.setMinAmnt(new BigDecimal("10.00"));
            testResDTO.setTxnCode("61000");
            testResDTO.setStatus("1");
            testResDTO.setVersionNumber(1L);
            testResDTO.setLmtUnitCodeFollowIndicator("0");
            testResDTO.setCreateTime(LocalDateTime.now());
            testResDTO.setUpdateTime(LocalDateTime.now());

            testEntity = new ParmLateFeeTable();
            testEntity.setId("TEST_ID_001");
            testEntity.setOrganizationNumber("001");
            testEntity.setTableId("TABLE_001");
            testEntity.setDescription("测试违约金参数");
            testEntity.setChargeFlag("1");
            testEntity.setCalculateMethod("1");
            testEntity.setChargeOption("1");
            testEntity.setChargePercent(new BigDecimal("0.05"));
            testEntity.setFixedAmnt(new BigDecimal("100.00"));
            testEntity.setGenerateInterestOption("0");
            testEntity.setMaxAmnt(new BigDecimal("500.00"));
            testEntity.setMinAmnt(new BigDecimal("10.00"));
            testEntity.setTxnCode("61000");
            testEntity.setStatus("1");
            testEntity.setVersionNumber(1L);
            testEntity.setLmtUnitCodeFollowIndicator("0");
            testEntity.setCreateTime(LocalDateTime.now());
            testEntity.setUpdateTime(LocalDateTime.now());

            testSearchDTO = new LateFeeTableSearchDTO();
            testSearchDTO.setOrganizationNumber("001");
            testSearchDTO.setTableId("TABLE_001");
            testSearchDTO.setDescription("测试");
            testSearchDTO.setChargeFlag("1");
        }
    }

    @Test
    void testFindAll_Success() {
        // Arrange
        List<ParmLateFeeTable> entityList = Arrays.asList(testEntity);
        List<LateFeeTableResDTO> dtoList = Arrays.asList(testResDTO);
        
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            
            orgNumberUtilsMock.when(() -> OrgNumberUtils.getOrg()).thenReturn("001");
            beanMappingMock.when(() -> BeanMapping.copyList(entityList, LateFeeTableResDTO.class)).thenReturn(dtoList);
            
            when(parmLateFeeTableSelfMapper.selectByCondition(any(LateFeeTableSearchDTO.class))).thenReturn(entityList);

            // Act
            PageResultDTO<LateFeeTableResDTO> result = lateFeeTableService.findAll(1, 10, testSearchDTO);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getData()).hasSize(1);
            assertThat(result.getPage()).isEqualTo(1);
            assertThat(result.getRows()).isEqualTo(10);
            verify(parmLateFeeTableSelfMapper).selectByCondition(any(LateFeeTableSearchDTO.class));
        }
    }

    @Test
    void testFindAll_WithNullSearchDTO() {
        // Arrange
        List<ParmLateFeeTable> entityList = Collections.emptyList();
        List<LateFeeTableResDTO> dtoList = Collections.emptyList();
        
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            
            orgNumberUtilsMock.when(() -> OrgNumberUtils.getOrg()).thenReturn("001");
            beanMappingMock.when(() -> BeanMapping.copyList(entityList, LateFeeTableResDTO.class)).thenReturn(dtoList);
            
            when(parmLateFeeTableSelfMapper.selectByCondition(any(LateFeeTableSearchDTO.class))).thenReturn(entityList);

            // Act
            PageResultDTO<LateFeeTableResDTO> result = lateFeeTableService.findAll(1, 10, null);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getData()).isEmpty();
            verify(parmLateFeeTableSelfMapper).selectByCondition(any(LateFeeTableSearchDTO.class));
        }
    }

    @Test
    void testFindAll_InvalidChargePercent_ThrowsException() {
        // Arrange
        testSearchDTO.setChargePercent("invalid_number");

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            lateFeeTableService.findAll(1, 10, testSearchDTO);
        });
        
        assertThat(exception.getErrCode()).isEqualTo(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION.getCode());
    }

    @Test
    void testFindAll_InvalidFixedAmnt_ThrowsException() {
        // Arrange
        testSearchDTO.setFixedAmnt("invalid_number");

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            lateFeeTableService.findAll(1, 10, testSearchDTO);
        });
        
        assertThat(exception.getErrCode()).isEqualTo(AnyTxnParameterRespCodeEnum.PARAMETER_EXCEPTION.getCode());
    }

    @Test
    void testAddParmFee_Success() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<TenantUtils> tenantUtilsMock = mockStatic(TenantUtils.class)) {
            
            orgNumberUtilsMock.when(() -> OrgNumberUtils.getOrg("001")).thenReturn("001");
            tenantUtilsMock.when(() -> TenantUtils.getTenantId()).thenReturn("tenant_001");
            beanMappingMock.when(() -> BeanMapping.copy(testReqDTO, ParmLateFeeTable.class)).thenReturn(testEntity);
            
            when(parmLateFeeTableSelfMapper.selectByOrgAndTid("001", "TABLE_001")).thenReturn(null);
            when(numberIdGenerator.generateId("tenant_001")).thenReturn(123456L);

            // Act
            ParameterCompare result = lateFeeTableService.addParmFee(testReqDTO);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getAfter()).isNotNull();
            verify(parmLateFeeTableSelfMapper).selectByOrgAndTid("001", "TABLE_001");
            verify(numberIdGenerator).generateId("tenant_001");
        }
    }

    @Test
    void testAddParmFee_AlreadyExists_ThrowsException() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(() -> OrgNumberUtils.getOrg("001")).thenReturn("001");
            when(parmLateFeeTableSelfMapper.selectByOrgAndTid("001", "TABLE_001")).thenReturn(testEntity);

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
                lateFeeTableService.addParmFee(testReqDTO);
            });
            
            assertThat(exception.getErrCode()).isEqualTo(AnyTxnParameterRespCodeEnum.P_PARAMETER_TABLE_ID_EXIST.getCode());
        }
    }

    @Test
    void testAddParmFee_ChargeOption3_InvalidParams_ThrowsException() {
        // Arrange
        testReqDTO.setChargeOption("3"); // 按次收取
        testReqDTO.setLateFeeLimitAmount(null); // 门槛金额为空

        try (MockedStatic<OrgNumberUtils> orgNumberUtilsMock = mockStatic(OrgNumberUtils.class)) {
            orgNumberUtilsMock.when(() -> OrgNumberUtils.getOrg("001")).thenReturn("001");
            when(parmLateFeeTableSelfMapper.selectByOrgAndTid("001", "TABLE_001")).thenReturn(null);

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
                lateFeeTableService.addParmFee(testReqDTO);
            });
            
            assertThat(exception.getErrCode()).isEqualTo(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode());
        }
    }

    @Test
    void testModifyParmFee_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(testReqDTO, ParmLateFeeTable.class)).thenReturn(testEntity);
            when(parmLateFeeTableMapper.selectByPrimaryKey("TEST_ID_001")).thenReturn(testEntity);

            // Act
            ParameterCompare result = lateFeeTableService.modifyParmFee(testReqDTO);

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getBefore()).isNotNull();
            assertThat(result.getAfter()).isNotNull();
            verify(parmLateFeeTableMapper).selectByPrimaryKey("TEST_ID_001");
        }
    }

    @Test
    void testModifyParmFee_DataNotExist_ThrowsException() {
        // Arrange
        when(parmLateFeeTableMapper.selectByPrimaryKey("TEST_ID_001")).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            lateFeeTableService.modifyParmFee(testReqDTO);
        });
        
        assertThat(exception.getErrCode()).isEqualTo(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT.getCode());
    }

    @Test
    void testModifyParmFee_DifferentOrgOrTableId_ExistsConflict_ThrowsException() {
        // Arrange
        ParmLateFeeTable existingEntity = new ParmLateFeeTable();
        existingEntity.setOrganizationNumber("002"); // 不同机构号
        existingEntity.setTableId("TABLE_002"); // 不同表ID
        
        testReqDTO.setOrganizationNumber("001");
        testReqDTO.setTableId("TABLE_001");
        
        when(parmLateFeeTableMapper.selectByPrimaryKey("TEST_ID_001")).thenReturn(existingEntity);
        when(parmLateFeeTableSelfMapper.selectByOrgAndTid("001", "TABLE_001")).thenReturn(testEntity);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            lateFeeTableService.modifyParmFee(testReqDTO);
        });
        
        assertThat(exception.getErrCode()).isEqualTo(AnyTxnParameterRespCodeEnum.P_PARAMETER_TABLE_ID_EXIST.getCode());
    }

    @Test
    void testRemoveParmFee_Success() {
        // Arrange
        when(parmLateFeeTableMapper.selectByPrimaryKey("TEST_ID_001")).thenReturn(testEntity);

        // Act
        ParameterCompare result = lateFeeTableService.removeParmFee("TEST_ID_001");

        // Assert
        assertThat(result).isNotNull();
        assertThat(result.getBefore()).isNotNull();
        verify(parmLateFeeTableMapper).selectByPrimaryKey("TEST_ID_001");
    }

    @Test
    void testRemoveParmFee_DataNotExist_ThrowsException() {
        // Arrange
        when(parmLateFeeTableMapper.selectByPrimaryKey("TEST_ID_001")).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            lateFeeTableService.removeParmFee("TEST_ID_001");
        });
        
        assertThat(exception.getErrCode()).isEqualTo(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT.getCode());
    }

    @Test
    void testFindById_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(testEntity, LateFeeTableResDTO.class)).thenReturn(testResDTO);
            when(parmLateFeeTableMapper.selectByPrimaryKey("TEST_ID_001")).thenReturn(testEntity);

            // Act
            LateFeeTableResDTO result = lateFeeTableService.findById("TEST_ID_001");

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getId()).isEqualTo("TEST_ID_001");
            verify(parmLateFeeTableMapper).selectByPrimaryKey("TEST_ID_001");
        }
    }

    @Test
    void testFindById_NullId_ThrowsException() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            lateFeeTableService.findById(null);
        });
        
        assertThat(exception.getErrCode()).isEqualTo(AnyTxnParameterRespCodeEnum.P_PARAMETER_ID_EMPTY_FAULT.getCode());
    }

    @Test
    void testFindById_NotFound() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(null, LateFeeTableResDTO.class)).thenReturn(null);
            when(parmLateFeeTableMapper.selectByPrimaryKey("TEST_ID_001")).thenReturn(null);

            // Act
            LateFeeTableResDTO result = lateFeeTableService.findById("TEST_ID_001");

            // Assert
            assertThat(result).isNull();
            verify(parmLateFeeTableMapper).selectByPrimaryKey("TEST_ID_001");
        }
    }

    @Test
    void testFindByOrgAndTableId_Success() {
        // Arrange
        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            beanMappingMock.when(() -> BeanMapping.copy(testEntity, LateFeeTableResDTO.class)).thenReturn(testResDTO);
            when(parmLateFeeTableSelfMapper.selectByOrgAndTid("001", "TABLE_001")).thenReturn(testEntity);

            // Act
            LateFeeTableResDTO result = lateFeeTableService.findByOrgAndTableId("001", "TABLE_001");

            // Assert
            assertThat(result).isNotNull();
            assertThat(result.getId()).isEqualTo("TEST_ID_001");
            verify(parmLateFeeTableSelfMapper).selectByOrgAndTid("001", "TABLE_001");
        }
    }

    @Test
    void testFindByOrgAndTableId_NotFound_ThrowsException() {
        // Arrange
        when(parmLateFeeTableSelfMapper.selectByOrgAndTid("001", "TABLE_001")).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            lateFeeTableService.findByOrgAndTableId("001", "TABLE_001");
        });
        
        assertThat(exception.getErrCode()).isEqualTo(AnyTxnParameterRespCodeEnum.D_GET_IS_NULL_FAULT.getCode());
    }
} 
