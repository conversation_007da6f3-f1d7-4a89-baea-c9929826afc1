package com.anytech.anytxn.parameter.accounting.service;

import com.alibaba.fastjson.JSONObject;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.accounting.mapper.ParmAccountantDictMappingMapper;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.ParamAccountantDicMappingReqDTO;
import com.anytech.anytxn.parameter.base.accounting.domain.dto.ParamAccountantDictMappingDTO;
import com.anytech.anytxn.parameter.base.accounting.domain.model.ParamAccountantDictMapping;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;


import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ParamAccountantDictService 单元测试类
 * 
 * <AUTHOR>
 * @date 2022/12/5 20:48
 */
@ExtendWith(MockitoExtension.class)
class ParamAccountantDictServiceTest {

    @Mock
    private ParmAccountantDictMappingMapper parmAccountantDictMappingMapper;

    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private ParamAccountantDictServiceImpl paramAccountantDictService;

    @BeforeEach
    void setUp() throws Exception {
        // 设置通用的Mock
        Mockito.lenient().when(numberIdGenerator.generateId(anyString())).thenReturn(*********L);

        // 通过反射设置OrgNumberUtils静态实例
        setupOrgNumberUtils();
    }

    /**
     * 通过反射设置OrgNumberUtils静态实例
     */
    private void setupOrgNumberUtils() throws Exception {
        // 初始化OrgNumberUtils的静态实例以避免NullPointerException
        Field orgNumberUtilField = OrgNumberUtils.class.getDeclaredField("orgNumberUtil");
        orgNumberUtilField.setAccessible(true);
        OrgNumberUtils orgNumberUtilInstance = new OrgNumberUtils();
        orgNumberUtilField.set(null, orgNumberUtilInstance);
    }

    @Test
    void testAddParam_Success() {
        // Arrange
        ParamAccountantDicMappingReqDTO request = new ParamAccountantDicMappingReqDTO();
        request.setMappingType("TEST_TYPE");
        request.setCodeValue("TEST_CODE");
        request.setMappingValue("TEST_VALUE");
        request.setStatus("1");
        request.setOrganizationNumber("001");

        // Act
        ParameterCompare result = paramAccountantDictService.addParam(request);

        // Assert
        assertNotNull(result);
        assertEquals(1L, request.getVersionNumber());
        assertNotNull(result.getAfter());
        assertNull(result.getBefore());
    }

    @Test
    void testGetByPage_WithNullRequest() {
        // Arrange
        Integer pageNum = 1;
        Integer pageSize = 10;
        ParamAccountantDicMappingReqDTO request = null;

        List<ParamAccountantDictMapping> mappingList = createMockMappingList();
        List<ParamAccountantDicMappingReqDTO> reqDTOList = createMockReqDTOList();

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class);
             MockedStatic<OrgNumberUtils> orgMock = mockStatic(OrgNumberUtils.class)) {

            // Mock静态方法
            ParamAccountantDictMapping conditionMapping = new ParamAccountantDictMapping();
            conditionMapping.setOrganizationNumber("001");

            orgMock.when(OrgNumberUtils::getOrg).thenReturn("001");
            beanMappingMock.when(() -> BeanMapping.copy(any(ParamAccountantDicMappingReqDTO.class), eq(ParamAccountantDictMapping.class)))
                    .thenReturn(conditionMapping);
            beanMappingMock.when(() -> BeanMapping.copyList(mappingList, ParamAccountantDicMappingReqDTO.class))
                    .thenReturn(reqDTOList);

            // Mock Mapper方法
            Mockito.lenient().when(parmAccountantDictMappingMapper.selectByCondition(any(ParamAccountantDictMapping.class)))
                    .thenReturn(mappingList);

            // Act
            PageResultDTO<ParamAccountantDicMappingReqDTO> result = paramAccountantDictService.getByPage(pageNum, pageSize, request);

            // Assert
            assertNotNull(result);
            assertNotNull(result.getData());
            assertEquals(reqDTOList, result.getData());

            verify(parmAccountantDictMappingMapper).selectByCondition(any(ParamAccountantDictMapping.class));
        }
    }

    @Test
    void testGetByPage_WithCodeValue() {
        // Arrange
        Integer pageNum = 1;
        Integer pageSize = 10;
        ParamAccountantDicMappingReqDTO request = new ParamAccountantDicMappingReqDTO();
        request.setCodeValue("TEST_CODE");
        request.setOrganizationNumber("001");

        ParamAccountantDictMapping foundMapping = new ParamAccountantDictMapping();
        foundMapping.setId("mapping123");
        foundMapping.setParentId(null);
        foundMapping.setCodeValue("TEST_CODE");

        List<ParamAccountantDictMapping> mappingList = createMockMappingList();
        List<ParamAccountantDicMappingReqDTO> reqDTOList = createMockReqDTOList();

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {

            // Mock静态方法
            ParamAccountantDictMapping conditionMapping = new ParamAccountantDictMapping();
            conditionMapping.setCodeValue("TEST_CODE");
            conditionMapping.setOrganizationNumber("001");
            conditionMapping.setId("mapping123");

            beanMappingMock.when(() -> BeanMapping.copy(request, ParamAccountantDictMapping.class))
                    .thenReturn(conditionMapping);
            beanMappingMock.when(() -> BeanMapping.copyList(mappingList, ParamAccountantDicMappingReqDTO.class))
                    .thenReturn(reqDTOList);

            // Mock Mapper方法
            Mockito.lenient().when(parmAccountantDictMappingMapper.selectByCodeValue("TEST_CODE"))
                    .thenReturn(foundMapping);
            Mockito.lenient().when(parmAccountantDictMappingMapper.selectByCondition(any(ParamAccountantDictMapping.class)))
                    .thenReturn(mappingList);

            // Act
            PageResultDTO<ParamAccountantDicMappingReqDTO> result = paramAccountantDictService.getByPage(pageNum, pageSize, request);

            // Assert
            assertNotNull(result);
            assertNotNull(result.getData());
            assertEquals(reqDTOList, result.getData());

            verify(parmAccountantDictMappingMapper).selectByCodeValue("TEST_CODE");
            verify(parmAccountantDictMappingMapper).selectByCondition(any(ParamAccountantDictMapping.class));
        }
    }

    @Test
    void testGetByPage_WithCodeValueNotFound() {
        // Arrange
        Integer pageNum = 1;
        Integer pageSize = 10;
        ParamAccountantDicMappingReqDTO request = new ParamAccountantDicMappingReqDTO();
        request.setCodeValue("NOT_FOUND_CODE");
        request.setOrganizationNumber("001");

        List<ParamAccountantDictMapping> mappingList = new ArrayList<>();
        List<ParamAccountantDicMappingReqDTO> reqDTOList = new ArrayList<>();

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {

            // Mock静态方法
            ParamAccountantDictMapping conditionMapping = new ParamAccountantDictMapping();
            conditionMapping.setCodeValue("NOT_FOUND_CODE");
            conditionMapping.setOrganizationNumber("001");
            conditionMapping.setId("-1");

            beanMappingMock.when(() -> BeanMapping.copy(request, ParamAccountantDictMapping.class))
                    .thenReturn(conditionMapping);
            beanMappingMock.when(() -> BeanMapping.copyList(mappingList, ParamAccountantDicMappingReqDTO.class))
                    .thenReturn(reqDTOList);

            // Mock Mapper方法
            Mockito.lenient().when(parmAccountantDictMappingMapper.selectByCodeValue("NOT_FOUND_CODE"))
                    .thenReturn(null);
            Mockito.lenient().when(parmAccountantDictMappingMapper.selectByCondition(any(ParamAccountantDictMapping.class)))
                    .thenReturn(mappingList);

            // Act
            PageResultDTO<ParamAccountantDicMappingReqDTO> result = paramAccountantDictService.getByPage(pageNum, pageSize, request);

            // Assert
            assertNotNull(result);
            assertNotNull(result.getData());
            assertTrue(result.getData().isEmpty());

            verify(parmAccountantDictMappingMapper).selectByCodeValue("NOT_FOUND_CODE");
            verify(parmAccountantDictMappingMapper).selectByCondition(any(ParamAccountantDictMapping.class));
        }
    }

    private List<ParamAccountantDictMapping> createMockMappingList() {
        List<ParamAccountantDictMapping> list = new ArrayList<>();
        
        ParamAccountantDictMapping mapping1 = new ParamAccountantDictMapping();
        mapping1.setId("1");
        mapping1.setMappingType("TYPE1");
        mapping1.setCodeValue("CODE1");
        mapping1.setMappingValue("VALUE1");
        mapping1.setStatus("1");
        mapping1.setOrganizationNumber("001");
        mapping1.setCreateTime(LocalDateTime.now());
        mapping1.setUpdateTime(LocalDateTime.now());
        mapping1.setVersionNumber(1L);
        list.add(mapping1);

        return list;
    }

    private List<ParamAccountantDicMappingReqDTO> createMockReqDTOList() {
        List<ParamAccountantDicMappingReqDTO> list = new ArrayList<>();
        
        ParamAccountantDicMappingReqDTO dto1 = new ParamAccountantDicMappingReqDTO();
        dto1.setId("1");
        dto1.setMappingType("TYPE1");
        dto1.setCodeValue("CODE1");
        dto1.setMappingValue("VALUE1");
        dto1.setStatus("1");
        dto1.setOrganizationNumber("001");
        dto1.setCreateTime(LocalDateTime.now());
        dto1.setUpdateTime(LocalDateTime.now());
        dto1.setVersionNumber(1L);
        list.add(dto1);

        return list;
    }

    @Test
    void testGetByPage_WithCodeValueHasParentId() {
        // Arrange
        Integer pageNum = 1;
        Integer pageSize = 10;
        ParamAccountantDicMappingReqDTO request = new ParamAccountantDicMappingReqDTO();
        request.setCodeValue("CHILD_CODE");
        request.setOrganizationNumber("001");

        ParamAccountantDictMapping foundMapping = new ParamAccountantDictMapping();
        foundMapping.setId("child123");
        foundMapping.setParentId("parent123");
        foundMapping.setCodeValue("CHILD_CODE");

        List<ParamAccountantDictMapping> mappingList = createMockMappingList();
        List<ParamAccountantDicMappingReqDTO> reqDTOList = createMockReqDTOList();

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {

            // Mock静态方法
            ParamAccountantDictMapping conditionMapping = new ParamAccountantDictMapping();
            conditionMapping.setCodeValue("CHILD_CODE");
            conditionMapping.setOrganizationNumber("001");
            conditionMapping.setId("parent123");

            beanMappingMock.when(() -> BeanMapping.copy(request, ParamAccountantDictMapping.class))
                    .thenReturn(conditionMapping);
            beanMappingMock.when(() -> BeanMapping.copyList(mappingList, ParamAccountantDicMappingReqDTO.class))
                    .thenReturn(reqDTOList);

            // Mock Mapper方法
            Mockito.lenient().when(parmAccountantDictMappingMapper.selectByCodeValue("CHILD_CODE"))
                    .thenReturn(foundMapping);
            Mockito.lenient().when(parmAccountantDictMappingMapper.selectByCondition(any(ParamAccountantDictMapping.class)))
                    .thenReturn(mappingList);

            // Act
            PageResultDTO<ParamAccountantDicMappingReqDTO> result = paramAccountantDictService.getByPage(pageNum, pageSize, request);

            // Assert
            assertNotNull(result);
            assertNotNull(result.getData());
            assertEquals(reqDTOList, result.getData());

            verify(parmAccountantDictMappingMapper).selectByCodeValue("CHILD_CODE");
            verify(parmAccountantDictMappingMapper).selectByCondition(any(ParamAccountantDictMapping.class));
        }
    }

    @Test
    void testGetDetails_Success() {
        // Arrange
        String id = "test123";

        ParamAccountantDictMapping mainMapping = new ParamAccountantDictMapping();
        mainMapping.setId(id);
        mainMapping.setMappingType("MAIN_TYPE");
        mainMapping.setCodeValue("MAIN_CODE");
        mainMapping.setMappingValue("MAIN_VALUE");
        mainMapping.setStatus("1");
        mainMapping.setOrganizationNumber("001");
        mainMapping.setCreateTime(LocalDateTime.now());
        mainMapping.setUpdateTime(LocalDateTime.now());
        mainMapping.setVersionNumber(1L);

        List<ParamAccountantDictMapping> childMappings = new ArrayList<>();
        ParamAccountantDictMapping child1 = new ParamAccountantDictMapping();
        child1.setId("child1");
        child1.setParentId(id);
        child1.setCodeValue("CHILD_CODE_1");
        child1.setMappingValue("CHILD_VALUE_1");
        child1.setOrganizationNumber("001");
        childMappings.add(child1);

        ParamAccountantDicMappingReqDTO expectedResult = new ParamAccountantDicMappingReqDTO();
        expectedResult.setId(id);
        expectedResult.setMappingType("MAIN_TYPE");
        expectedResult.setCodeValue("MAIN_CODE");
        expectedResult.setMappingValue("MAIN_VALUE");
        expectedResult.setStatus("1");
        expectedResult.setOrganizationNumber("001");

        List<ParamAccountantDictMappingDTO> childDTOs = new ArrayList<>();
        ParamAccountantDictMappingDTO childDTO1 = new ParamAccountantDictMappingDTO();
        childDTO1.setCodeValue("CHILD_CODE_1");
        childDTO1.setMappingValue("CHILD_VALUE_1");
        childDTO1.setOrganizationNumber("001");
        childDTOs.add(childDTO1);

        expectedResult.setAccountantDictMappingDtoS(childDTOs);

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            // Mock Mapper方法
            Mockito.lenient().when(parmAccountantDictMappingMapper.selectById(id))
                    .thenReturn(mainMapping);
            Mockito.lenient().when(parmAccountantDictMappingMapper.selectByParentId(id))
                    .thenReturn(childMappings);

            // Mock静态方法
            beanMappingMock.when(() -> BeanMapping.copy(mainMapping, ParamAccountantDicMappingReqDTO.class))
                    .thenReturn(expectedResult);
            beanMappingMock.when(() -> BeanMapping.copyList(childMappings, ParamAccountantDictMappingDTO.class))
                    .thenReturn(childDTOs);

            // Act
            ParamAccountantDicMappingReqDTO result = paramAccountantDictService.getDetails(id);

            // Assert
            assertNotNull(result);
            assertEquals(id, result.getId());
            assertEquals("MAIN_TYPE", result.getMappingType());
            assertEquals("MAIN_CODE", result.getCodeValue());
            assertEquals("MAIN_VALUE", result.getMappingValue());
            assertEquals("1", result.getStatus());
            assertEquals("001", result.getOrganizationNumber());
            assertNotNull(result.getAccountantDictMappingDtoS());
            assertEquals(1, result.getAccountantDictMappingDtoS().size());

            verify(parmAccountantDictMappingMapper).selectById(id);
            verify(parmAccountantDictMappingMapper).selectByParentId(id);
        }
    }

    @Test
    void testUpdateById_Success() {
        // Arrange
        String id = "test123";
        ParamAccountantDicMappingReqDTO request = new ParamAccountantDicMappingReqDTO();
        request.setId(id);
        request.setMappingType("UPDATED_TYPE");
        request.setCodeValue("UPDATED_CODE");
        request.setMappingValue("UPDATED_VALUE");
        request.setStatus("1");
        request.setOrganizationNumber("001");

        ParamAccountantDicMappingReqDTO existingData = new ParamAccountantDicMappingReqDTO();
        existingData.setId(id);
        existingData.setMappingType("OLD_TYPE");
        existingData.setCodeValue("OLD_CODE");
        existingData.setMappingValue("OLD_VALUE");
        existingData.setStatus("1");
        existingData.setOrganizationNumber("001");

        ParamAccountantDictMapping mainMapping = new ParamAccountantDictMapping();
        mainMapping.setId(id);
        mainMapping.setMappingType("OLD_TYPE");
        mainMapping.setCodeValue("OLD_CODE");
        mainMapping.setMappingValue("OLD_VALUE");
        mainMapping.setStatus("1");
        mainMapping.setOrganizationNumber("001");

        List<ParamAccountantDictMapping> childMappings = new ArrayList<>();
        List<ParamAccountantDictMappingDTO> childDTOs = new ArrayList<>();

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            // Mock Mapper方法
            Mockito.lenient().when(parmAccountantDictMappingMapper.selectById(id))
                    .thenReturn(mainMapping);
            Mockito.lenient().when(parmAccountantDictMappingMapper.selectByParentId(id))
                    .thenReturn(childMappings);

            // Mock静态方法
            beanMappingMock.when(() -> BeanMapping.copy(mainMapping, ParamAccountantDicMappingReqDTO.class))
                    .thenReturn(existingData);
            beanMappingMock.when(() -> BeanMapping.copyList(childMappings, ParamAccountantDictMappingDTO.class))
                    .thenReturn(childDTOs);

            // Act
            ParameterCompare result = paramAccountantDictService.updateById(request);

            // Assert
            assertNotNull(result);
            assertNotNull(result.getBefore());
            assertNotNull(result.getAfter());
            assertEquals(existingData, result.getBefore());
            assertEquals(request, result.getAfter());
        }
    }

    @Test
    void testDeleteById_Success() {
        // Arrange
        String id = "test123";

        ParamAccountantDictMapping existingMapping = new ParamAccountantDictMapping();
        existingMapping.setId(id);
        existingMapping.setMappingType("TEST_TYPE");
        existingMapping.setCodeValue("TEST_CODE");
        existingMapping.setMappingValue("TEST_VALUE");
        existingMapping.setStatus("1");
        existingMapping.setOrganizationNumber("001");

        ParamAccountantDicMappingReqDTO existingData = new ParamAccountantDicMappingReqDTO();
        existingData.setId(id);
        existingData.setMappingType("TEST_TYPE");
        existingData.setCodeValue("TEST_CODE");
        existingData.setMappingValue("TEST_VALUE");
        existingData.setStatus("1");
        existingData.setOrganizationNumber("001");

        List<ParamAccountantDictMapping> childMappings = new ArrayList<>();
        List<ParamAccountantDictMappingDTO> childDTOs = new ArrayList<>();

        try (MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {
            // Mock Mapper方法
            Mockito.lenient().when(parmAccountantDictMappingMapper.selectById(id))
                    .thenReturn(existingMapping);
            Mockito.lenient().when(parmAccountantDictMappingMapper.selectByParentId(id))
                    .thenReturn(childMappings);

            // Mock静态方法
            beanMappingMock.when(() -> BeanMapping.copy(existingMapping, ParamAccountantDicMappingReqDTO.class))
                    .thenReturn(existingData);
            beanMappingMock.when(() -> BeanMapping.copyList(childMappings, ParamAccountantDictMappingDTO.class))
                    .thenReturn(childDTOs);

            // Act
            Object result = paramAccountantDictService.deleteById(id);

            // Assert
            assertNotNull(result);
            assertTrue(result instanceof ParameterCompare);
            ParameterCompare parameterCompare = (ParameterCompare) result;
            assertNotNull(parameterCompare.getBefore());
            assertNull(parameterCompare.getAfter());

            verify(parmAccountantDictMappingMapper, times(2)).selectById(id);
        }
    }

    @Test
    void testDeleteById_DataNotExist() {
        // Arrange
        String id = "nonexistent123";

        // Mock Mapper方法
        Mockito.lenient().when(parmAccountantDictMappingMapper.selectById(id))
                .thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, () -> {
            paramAccountantDictService.deleteById(id);
        });

        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_DATA_NOT_EXIST.getCode(), exception.getErrCode());
        verify(parmAccountantDictMappingMapper).selectById(id);
    }

    @Test
    void testUpdateDb_Success() throws Exception {
        // Arrange
        ParmModificationRecord record = new ParmModificationRecord();
        record.setApplicationBy("testUser");

        ParamAccountantDicMappingReqDTO requestDTO = new ParamAccountantDicMappingReqDTO();
        requestDTO.setId("test123");
        requestDTO.setMappingType("ACCOUT_PRODUCT_TYPE");
        requestDTO.setCodeValue("TEST_CODE");
        requestDTO.setMappingValue("TEST_VALUE");
        requestDTO.setStatus("1");
        requestDTO.setOrganizationNumber("001");

        List<ParamAccountantDictMappingDTO> childDTOs = new ArrayList<>();
        ParamAccountantDictMappingDTO childDTO = new ParamAccountantDictMappingDTO();
        childDTO.setCodeValue("CHILD_CODE");
        childDTO.setMappingValue("CHILD_VALUE");
        childDTO.setOrganizationNumber("001");
        childDTOs.add(childDTO);
        requestDTO.setAccountantDictMappingDtoS(childDTOs);

        String jsonBody = "{\"id\":\"test123\",\"mappingType\":\"ACCOUT_PRODUCT_TYPE\",\"codeValue\":\"TEST_CODE\",\"mappingValue\":\"TEST_VALUE\",\"status\":\"1\",\"organizationNumber\":\"001\",\"accountantDictMappingDtoS\":[{\"codeValue\":\"CHILD_CODE\",\"mappingValue\":\"CHILD_VALUE\",\"organizationNumber\":\"001\"}]}";
        record.setParmBody(jsonBody);

        ParamAccountantDictMapping mainMapping = new ParamAccountantDictMapping();
        mainMapping.setId("test123");
        mainMapping.setMappingType("ACCOUT_PRODUCT_TYPE");
        mainMapping.setCodeValue("TEST_CODE");
        mainMapping.setMappingValue("TEST_VALUE");
        mainMapping.setStatus("1");
        mainMapping.setOrganizationNumber("001");

        ParamAccountantDictMapping childMapping = new ParamAccountantDictMapping();
        childMapping.setId("*********");
        childMapping.setParentId("test123");
        childMapping.setCodeValue("CHILD_CODE");
        childMapping.setMappingValue("TEST_VALUE");
        childMapping.setMappingType("ACCOUT_PRODUCT_TYPE");
        childMapping.setOrganizationNumber("001");

        try (MockedStatic<JSONObject> jsonMock = mockStatic(JSONObject.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {

            // Mock静态方法
            jsonMock.when(() -> JSONObject.parseObject(jsonBody, ParamAccountantDicMappingReqDTO.class))
                    .thenReturn(requestDTO);
            beanMappingMock.when(() -> BeanMapping.copy(requestDTO, ParamAccountantDictMapping.class))
                    .thenReturn(mainMapping);
            beanMappingMock.when(() -> BeanMapping.copy(childDTO, ParamAccountantDictMapping.class))
                    .thenReturn(childMapping);

            // Mock Mapper方法
            Mockito.lenient().when(parmAccountantDictMappingMapper.updateSelective(any(ParamAccountantDictMapping.class)))
                    .thenReturn(1);
            Mockito.lenient().doNothing().when(parmAccountantDictMappingMapper).deleteAllByParentId("test123");
            Mockito.lenient().doNothing().when(parmAccountantDictMappingMapper).batchInsert(anyList());

            // 使用反射调用私有方法
            Method updateDbMethod = ParamAccountantDictServiceImpl.class.getDeclaredMethod("updateDb", ParmModificationRecord.class);
            updateDbMethod.setAccessible(true);

            // Act
            Boolean result = (Boolean) updateDbMethod.invoke(paramAccountantDictService, record);

            // Assert
            assertTrue(result);
            verify(parmAccountantDictMappingMapper).updateSelective(any(ParamAccountantDictMapping.class));
            verify(parmAccountantDictMappingMapper).deleteAllByParentId("test123");
            verify(parmAccountantDictMappingMapper).batchInsert(anyList());
        }
    }

    @Test
    void testUpdateDb_UpdateFailed() throws Exception {
        // Arrange
        ParmModificationRecord record = new ParmModificationRecord();
        record.setApplicationBy("testUser");

        ParamAccountantDicMappingReqDTO requestDTO = new ParamAccountantDicMappingReqDTO();
        requestDTO.setId("test123");
        requestDTO.setMappingType("TEST_TYPE");
        requestDTO.setCodeValue("TEST_CODE");
        requestDTO.setMappingValue("TEST_VALUE");
        requestDTO.setStatus("1");
        requestDTO.setOrganizationNumber("001");
        requestDTO.setAccountantDictMappingDtoS(new ArrayList<>());

        String jsonBody = "{\"id\":\"test123\",\"mappingType\":\"TEST_TYPE\",\"codeValue\":\"TEST_CODE\",\"mappingValue\":\"TEST_VALUE\",\"status\":\"1\",\"organizationNumber\":\"001\",\"accountantDictMappingDtoS\":[]}";
        record.setParmBody(jsonBody);

        ParamAccountantDictMapping mainMapping = new ParamAccountantDictMapping();
        mainMapping.setId("test123");
        mainMapping.setMappingType("TEST_TYPE");
        mainMapping.setCodeValue("TEST_CODE");
        mainMapping.setMappingValue("TEST_VALUE");
        mainMapping.setStatus("1");
        mainMapping.setOrganizationNumber("001");

        try (MockedStatic<JSONObject> jsonMock = mockStatic(JSONObject.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {

            // Mock静态方法
            jsonMock.when(() -> JSONObject.parseObject(jsonBody, ParamAccountantDicMappingReqDTO.class))
                    .thenReturn(requestDTO);
            beanMappingMock.when(() -> BeanMapping.copy(requestDTO, ParamAccountantDictMapping.class))
                    .thenReturn(mainMapping);

            // Mock Mapper方法 - 更新失败
            Mockito.lenient().when(parmAccountantDictMappingMapper.updateSelective(any(ParamAccountantDictMapping.class)))
                    .thenReturn(0);

            // 使用反射调用私有方法
            Method updateDbMethod = ParamAccountantDictServiceImpl.class.getDeclaredMethod("updateDb", ParmModificationRecord.class);
            updateDbMethod.setAccessible(true);

            // Act
            Boolean result = (Boolean) updateDbMethod.invoke(paramAccountantDictService, record);

            // Assert
            assertFalse(result);
            verify(parmAccountantDictMappingMapper).updateSelective(any(ParamAccountantDictMapping.class));
            verify(parmAccountantDictMappingMapper, never()).deleteAllByParentId(anyString());
            verify(parmAccountantDictMappingMapper, never()).batchInsert(anyList());
        }
    }

    @Test
    void testInsertDb_Success() throws Exception {
        // Arrange
        ParmModificationRecord record = new ParmModificationRecord();
        record.setApplicationBy("testUser");

        ParamAccountantDicMappingReqDTO requestDTO = new ParamAccountantDicMappingReqDTO();
        requestDTO.setId("test123");
        requestDTO.setMappingType("ACCOUT_PRODUCT_TYPE");
        requestDTO.setCodeValue("TEST_CODE");
        requestDTO.setMappingValue("TEST_VALUE");
        requestDTO.setStatus("1");
        requestDTO.setOrganizationNumber("001");

        List<ParamAccountantDictMappingDTO> childDTOs = new ArrayList<>();
        ParamAccountantDictMappingDTO childDTO = new ParamAccountantDictMappingDTO();
        childDTO.setCodeValue("CHILD_CODE");
        childDTO.setMappingValue("CHILD_VALUE");
        childDTO.setOrganizationNumber("001");
        childDTOs.add(childDTO);
        requestDTO.setAccountantDictMappingDtoS(childDTOs);

        String jsonBody = "{\"id\":\"test123\",\"mappingType\":\"ACCOUT_PRODUCT_TYPE\",\"codeValue\":\"TEST_CODE\",\"mappingValue\":\"TEST_VALUE\",\"status\":\"1\",\"organizationNumber\":\"001\",\"accountantDictMappingDtoS\":[{\"codeValue\":\"CHILD_CODE\",\"mappingValue\":\"CHILD_VALUE\",\"organizationNumber\":\"001\"}]}";
        record.setParmBody(jsonBody);

        ParamAccountantDictMapping mainMapping = new ParamAccountantDictMapping();
        mainMapping.setId("test123");
        mainMapping.setMappingType("ACCOUT_PRODUCT_TYPE");
        mainMapping.setCodeValue("TEST_CODE");
        mainMapping.setMappingValue("TEST_VALUE");
        mainMapping.setStatus("1");
        mainMapping.setOrganizationNumber("001");

        ParamAccountantDictMapping childMapping = new ParamAccountantDictMapping();
        childMapping.setId("*********");
        childMapping.setParentId("test123");
        childMapping.setCodeValue("CHILD_CODE");
        childMapping.setMappingValue("TEST_VALUE");
        childMapping.setMappingType("ACCOUT_PRODUCT_TYPE");
        childMapping.setOrganizationNumber("001");

        try (MockedStatic<JSONObject> jsonMock = mockStatic(JSONObject.class);
             MockedStatic<BeanMapping> beanMappingMock = mockStatic(BeanMapping.class)) {

            // Mock static methods
            jsonMock.when(() -> JSONObject.parseObject(jsonBody, ParamAccountantDicMappingReqDTO.class))
                    .thenReturn(requestDTO);
            beanMappingMock.when(() -> BeanMapping.copy(requestDTO, ParamAccountantDictMapping.class))
                    .thenReturn(mainMapping);
            beanMappingMock.when(() -> BeanMapping.copy(childDTO, ParamAccountantDictMapping.class))
                    .thenReturn(childMapping);

            // Mock Mapper方法
            Mockito.lenient().when(parmAccountantDictMappingMapper.insertSelective(any(ParamAccountantDictMapping.class)))
                    .thenReturn(1);
            Mockito.lenient().doNothing().when(parmAccountantDictMappingMapper).batchInsert(anyList());

            // 使用反射调用私有方法
            Method insertDbMethod = ParamAccountantDictServiceImpl.class.getDeclaredMethod("insertDb", ParmModificationRecord.class);
            insertDbMethod.setAccessible(true);

            // Act
            Boolean result = (Boolean) insertDbMethod.invoke(paramAccountantDictService, record);

            // Assert
            assertTrue(result);
            verify(parmAccountantDictMappingMapper).insertSelective(any(ParamAccountantDictMapping.class));
            verify(parmAccountantDictMappingMapper).batchInsert(anyList());
        }
    }

    @Test
    void testDeleteDb_Success() throws Exception {
        // Arrange
        ParmModificationRecord record = new ParmModificationRecord();
        record.setApplicationBy("testUser");

        ParamAccountantDicMappingReqDTO requestDTO = new ParamAccountantDicMappingReqDTO();
        requestDTO.setId("test123");
        requestDTO.setMappingType("TEST_TYPE");
        requestDTO.setCodeValue("TEST_CODE");
        requestDTO.setMappingValue("TEST_VALUE");
        requestDTO.setStatus("1");
        requestDTO.setOrganizationNumber("001");

        String jsonBody = "{\"id\":\"test123\",\"mappingType\":\"TEST_TYPE\",\"codeValue\":\"TEST_CODE\",\"mappingValue\":\"TEST_VALUE\",\"status\":\"1\",\"organizationNumber\":\"001\"}";
        record.setParmBody(jsonBody);

        try (MockedStatic<JSONObject> jsonMock = mockStatic(JSONObject.class)) {
            // Mock static methods
            jsonMock.when(() -> JSONObject.parseObject(jsonBody, ParamAccountantDicMappingReqDTO.class))
                    .thenReturn(requestDTO);

            // Mock Mapper方法
            Mockito.lenient().doNothing().when(parmAccountantDictMappingMapper).deleteBytId("test123");
            Mockito.lenient().doNothing().when(parmAccountantDictMappingMapper).deleteAllByParentId("test123");

            // 使用反射调用私有方法
            Method deleteDbMethod = ParamAccountantDictServiceImpl.class.getDeclaredMethod("deleteDb", ParmModificationRecord.class);
            deleteDbMethod.setAccessible(true);

            // Act
            Boolean result = (Boolean) deleteDbMethod.invoke(paramAccountantDictService, record);

            // Assert
            assertTrue(result);
            verify(parmAccountantDictMappingMapper).deleteBytId("test123");
            verify(parmAccountantDictMappingMapper).deleteAllByParentId("test123");
        }
    }
}
