package com.anytech.anytxn.parameter.account.service;

import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.account.mapper.ParmBlockCodeAccountMapper;
import com.anytech.anytxn.parameter.account.mapper.ParmBlockCodeAccountSelfMapper;
import com.anytech.anytxn.parameter.account.service.BlockCodeAccountServiceImpl;
import com.anytech.anytxn.parameter.base.account.domain.dto.BlockCodeAccountReqDTO;
import com.anytech.anytxn.parameter.base.account.domain.dto.BlockCodeAccountResDTO;
import com.anytech.anytxn.parameter.base.account.domain.model.ParmBlockCodeAccount;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * BlockCodeAccountService 测试类
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class BlockCodeAccountServiceTest {

    @Mock
    private ParmBlockCodeAccountMapper parmBlockCodeAccountMapper;

    @Mock
    private ParmBlockCodeAccountSelfMapper parmBlockCodeAccountSelfMapper;

    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private BlockCodeAccountServiceImpl blockCodeAccountService;

    @BeforeEach
    void setUp() {
        // 不在这里创建任何继承自BaseParam的对象
    }

    @Test
    void testAdd_Success() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMock = mockStatic(BeanMapping.class);
             MockedStatic<TenantUtils> tenantMock = mockStatic(TenantUtils.class)) {
            
            orgMock.when(OrgNumberUtils::getOrg).thenReturn("0001");
            tenantMock.when(TenantUtils::getTenantId).thenReturn("tenant1");
            
            // 创建测试数据
            BlockCodeAccountReqDTO reqDTO = new BlockCodeAccountReqDTO();
            reqDTO.setTableId("TABLE1");
            reqDTO.setBlockCode("01");
            
            ParmBlockCodeAccount entity = new ParmBlockCodeAccount();
            entity.setId(1L);
            entity.setTableId("TABLE1");
            entity.setBlockCode("01");
            
            BlockCodeAccountResDTO resDTO = new BlockCodeAccountResDTO();
            resDTO.setId(1L);
            resDTO.setTableId("TABLE1");
            resDTO.setBlockCode("01");
            
            when(parmBlockCodeAccountSelfMapper.isExists(anyString(), anyString(), anyString())).thenReturn(0);
            when(numberIdGenerator.generateId(anyString())).thenReturn(1L);
            beanMock.when(() -> BeanMapping.copy(any(BlockCodeAccountReqDTO.class), eq(ParmBlockCodeAccount.class)))
                    .thenReturn(entity);
            beanMock.when(() -> BeanMapping.copy(any(ParmBlockCodeAccount.class), eq(BlockCodeAccountResDTO.class)))
                    .thenReturn(resDTO);
            when(parmBlockCodeAccountMapper.insertSelective(any(ParmBlockCodeAccount.class))).thenReturn(1);

            // Act
            BlockCodeAccountResDTO result = blockCodeAccountService.add(reqDTO);

            // Assert
            assertNotNull(result);
            assertEquals(1L, result.getId());
            verify(parmBlockCodeAccountMapper).insertSelective(any(ParmBlockCodeAccount.class));
        }
    }

    @Test
    void testAdd_AlreadyExists_ThrowsException() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgMock = mockStatic(OrgNumberUtils.class)) {
            orgMock.when(OrgNumberUtils::getOrg).thenReturn("0001");
            
            BlockCodeAccountReqDTO reqDTO = new BlockCodeAccountReqDTO();
            reqDTO.setTableId("TABLE1");
            reqDTO.setBlockCode("01");
            
            when(parmBlockCodeAccountSelfMapper.isExists(anyString(), anyString(), anyString())).thenReturn(1);

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
                () -> blockCodeAccountService.add(reqDTO));
            assertEquals(AnyTxnParameterRespCodeEnum.P_EXIST_BLOCK_CODE_AND_ID_FAULT.getCode(), exception.getErrCode());
        }
    }

    @Test
    void testModify_Success() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMock = mockStatic(BeanMapping.class)) {
            
            orgMock.when(OrgNumberUtils::getOrg).thenReturn("0001");
            
            BlockCodeAccountReqDTO reqDTO = new BlockCodeAccountReqDTO();
            reqDTO.setId(1L);
            reqDTO.setTableId("TABLE1");
            reqDTO.setBlockCode("01");
            
            ParmBlockCodeAccount entity = new ParmBlockCodeAccount();
            entity.setId(1L);
            entity.setTableId("TABLE1");
            entity.setBlockCode("01");
            
            BlockCodeAccountResDTO resDTO = new BlockCodeAccountResDTO();
            resDTO.setId(1L);
            resDTO.setTableId("TABLE1");
            resDTO.setBlockCode("01");
            
            when(parmBlockCodeAccountMapper.selectByPrimaryKey(1L)).thenReturn(entity);
            when(parmBlockCodeAccountSelfMapper.isExists(anyString(), anyString(), anyString())).thenReturn(0);
            beanMock.when(() -> BeanMapping.copy(any(BlockCodeAccountReqDTO.class), any(ParmBlockCodeAccount.class)))
                    .thenAnswer(invocation -> null);
            beanMock.when(() -> BeanMapping.copy(any(ParmBlockCodeAccount.class), eq(BlockCodeAccountResDTO.class)))
                    .thenReturn(resDTO);
            when(parmBlockCodeAccountMapper.updateByPrimaryKeySelective(any(ParmBlockCodeAccount.class))).thenReturn(1);

            // Act
            BlockCodeAccountResDTO result = blockCodeAccountService.modify(reqDTO);

            // Assert
            assertNotNull(result);
            assertEquals(1L, result.getId());
            verify(parmBlockCodeAccountMapper).updateByPrimaryKeySelective(any(ParmBlockCodeAccount.class));
        }
    }

    @Test
    void testModify_IdIsNull_ThrowsException() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgMock = mockStatic(OrgNumberUtils.class)) {
            orgMock.when(OrgNumberUtils::getOrg).thenReturn("0001");
            
            BlockCodeAccountReqDTO reqDTO = new BlockCodeAccountReqDTO();
            reqDTO.setId(null);

            // Act & Assert
            AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
                () -> blockCodeAccountService.modify(reqDTO));
            assertEquals(AnyTxnParameterRespCodeEnum.P_BLOCK_CODE_ACCOUNT_REQ_ID_FAULT.getCode(), exception.getErrCode());
        }
    }

    @Test
    void testRemove_Success() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgMock = mockStatic(OrgNumberUtils.class)) {
            orgMock.when(OrgNumberUtils::getOrg).thenReturn("0001");
            
            ParmBlockCodeAccount entity = new ParmBlockCodeAccount();
            entity.setId(1L);
            entity.setTableId("TABLE1");
            entity.setBlockCode("01");
            
            when(parmBlockCodeAccountMapper.selectByPrimaryKey(1L)).thenReturn(entity);
            when(parmBlockCodeAccountMapper.deleteByPrimaryKey(1L)).thenReturn(1);

            // Act
            Boolean result = blockCodeAccountService.remove(1L);

            // Assert
            assertTrue(result);
            verify(parmBlockCodeAccountMapper).deleteByPrimaryKey(1L);
        }
    }

    @Test
    void testFind_Success() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMock = mockStatic(BeanMapping.class)) {
            
            orgMock.when(OrgNumberUtils::getOrg).thenReturn("0001");
            
            ParmBlockCodeAccount entity = new ParmBlockCodeAccount();
            entity.setId(1L);
            entity.setTableId("TABLE1");
            entity.setBlockCode("01");
            
            BlockCodeAccountResDTO resDTO = new BlockCodeAccountResDTO();
            resDTO.setId(1L);
            resDTO.setTableId("TABLE1");
            resDTO.setBlockCode("01");
            
            when(parmBlockCodeAccountMapper.selectByPrimaryKey(1L)).thenReturn(entity);
            beanMock.when(() -> BeanMapping.copy(any(ParmBlockCodeAccount.class), eq(BlockCodeAccountResDTO.class)))
                    .thenReturn(resDTO);

            // Act
            BlockCodeAccountResDTO result = blockCodeAccountService.find(1L);

            // Assert
            assertNotNull(result);
            assertEquals(1L, result.getId());
            verify(parmBlockCodeAccountMapper).selectByPrimaryKey(1L);
        }
    }

    @Test
    void testFindListByOrgNumber_Success() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> orgMock = mockStatic(OrgNumberUtils.class);
             MockedStatic<BeanMapping> beanMock = mockStatic(BeanMapping.class)) {
            
            orgMock.when(OrgNumberUtils::getOrg).thenReturn("0001");
            
            ParmBlockCodeAccount entity = new ParmBlockCodeAccount();
            entity.setId(1L);
            entity.setTableId("TABLE1");
            entity.setBlockCode("01");
            
            BlockCodeAccountResDTO resDTO = new BlockCodeAccountResDTO();
            resDTO.setId(1L);
            resDTO.setTableId("TABLE1");
            resDTO.setBlockCode("01");
            
            List<ParmBlockCodeAccount> entityList = Arrays.asList(entity);
            List<BlockCodeAccountResDTO> resDTOList = Arrays.asList(resDTO);
            
            when(parmBlockCodeAccountSelfMapper.selectListByOrgNumber("0001", "1", true))
                    .thenReturn(entityList);
            beanMock.when(() -> BeanMapping.copyList(entityList, BlockCodeAccountResDTO.class))
                    .thenReturn(resDTOList);

            // Act
            List<BlockCodeAccountResDTO> result = blockCodeAccountService.findListByOrgNumber("0001", "1");

            // Assert
            assertNotNull(result);
            assertEquals(1, result.size());
            assertEquals(1L, result.get(0).getId());
            verify(parmBlockCodeAccountSelfMapper).selectListByOrgNumber("0001", "1", true);
        }
    }
} 
