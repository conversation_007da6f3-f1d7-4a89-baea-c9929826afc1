package com.anytech.anytxn.parameter.card.service;

import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardProductCurrencyRelationReqDTO;
import com.anytech.anytxn.parameter.base.card.domain.dto.CardProductCurrencyRelationResDTO;
import com.anytech.anytxn.parameter.base.card.domain.model.ParmCardCurrencyInfo;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.parameter.base.common.domain.dto.system.OrganizationInfoResDTO;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardCurrencyInfoMapper;
import com.anytech.anytxn.parameter.card.mapper.broadcast.ParmCardCurrencyInfoSelfMapper;

import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * CardProductCurrencyRelationServiceImpl单元测试类
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
@ExtendWith(MockitoExtension.class)
class CardProductCurrencyRelationServiceTest {

    @Mock
    private ParmCardCurrencyInfoMapper parmCardCurrencyInfoMapper;

    @Mock
    private ParmCardCurrencyInfoSelfMapper parmCardCurrencyInfoSelfMapper;

    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private CardProductCurrencyRelationServiceImpl cardProductCurrencyRelationService;

    private MockedStatic<BeanMapping> beanMappingMockedStatic;
    private MockedStatic<TenantUtils> tenantUtilsMockedStatic;
    private MockedStatic<OrgNumberUtils> orgNumberUtilsMockedStatic;
    private MockedStatic<StringUtils> stringUtilsMockedStatic;

    private CardProductCurrencyRelationReqDTO reqDTO;
    private CardProductCurrencyRelationResDTO resDTO;
    private ParmCardCurrencyInfo parmCardCurrencyInfo;
    private OrganizationInfoResDTO organizationInfoResDTO;
    private List<ParmCardCurrencyInfo> cardCurrencyInfoList;

    @BeforeEach
    void setUp() {
        // 初始化静态Mock
        beanMappingMockedStatic = mockStatic(BeanMapping.class);
        tenantUtilsMockedStatic = mockStatic(TenantUtils.class);
        orgNumberUtilsMockedStatic = mockStatic(OrgNumberUtils.class);
        stringUtilsMockedStatic = mockStatic(StringUtils.class);

        // 设置测试数据
        setupTestData();

        // 设置静态方法Mock
        setupStaticMocks();
    }

    private void setupTestData() {
        // 创建CardProductCurrencyRelationReqDTO测试对象
        reqDTO = new CardProductCurrencyRelationReqDTO();
        reqDTO.setOrganizationNumber("1001");
        reqDTO.setProductNumber("P001");
        reqDTO.setStatus("1");
        reqDTO.setCurrencyList(Arrays.asList("USD", "CNY"));

        // 创建CardProductCurrencyRelationResDTO测试对象
        resDTO = new CardProductCurrencyRelationResDTO();
        resDTO.setId(1L);
        resDTO.setOrganizationNumber("1001");
        resDTO.setProductNumber("P001");
        resDTO.setStatus("1");
        resDTO.setCurrencyList(Arrays.asList("USD", "CNY"));

        // 创建ParmCardCurrencyInfo测试对象
        parmCardCurrencyInfo = new ParmCardCurrencyInfo();
        parmCardCurrencyInfo.setId(1L);
        parmCardCurrencyInfo.setOrganizationNumber("1001");
        parmCardCurrencyInfo.setProductNumber("P001");
        parmCardCurrencyInfo.setCurrencyCode("USD");
        parmCardCurrencyInfo.setStatus("1");
        parmCardCurrencyInfo.setCreateTime(LocalDateTime.now());
        parmCardCurrencyInfo.setUpdateTime(LocalDateTime.now());
        parmCardCurrencyInfo.setUpdateBy(Constants.DEFAULT_USER);
        parmCardCurrencyInfo.setVersionNumber(1L);

        cardCurrencyInfoList = Arrays.asList(parmCardCurrencyInfo);

        // 创建OrganizationInfoResDTO测试对象
        organizationInfoResDTO = new OrganizationInfoResDTO();
        organizationInfoResDTO.setOrganizationNumber("1001");
        organizationInfoResDTO.setOrganizationCurrency("CNY");
    }

    private void setupStaticMocks() {
        // Mock静态方法
        orgNumberUtilsMockedStatic.when(() -> OrgNumberUtils.getOrg()).thenReturn("1001");
        tenantUtilsMockedStatic.when(() -> TenantUtils.getTenantId()).thenReturn("tenant001");
        beanMappingMockedStatic.when(() -> BeanMapping.copy(any(CardProductCurrencyRelationReqDTO.class), eq(ParmCardCurrencyInfo.class)))
                .thenReturn(parmCardCurrencyInfo);
        beanMappingMockedStatic.when(() -> BeanMapping.copyList(anyList(), eq(CardProductCurrencyRelationResDTO.class)))
                .thenReturn(Arrays.asList(resDTO));
        beanMappingMockedStatic.when(() -> BeanMapping.copy(any(ParmCardCurrencyInfo.class), eq(CardProductCurrencyRelationResDTO.class)))
                .thenReturn(resDTO);
        stringUtilsMockedStatic.when(() -> StringUtils.isEmpty(anyString())).thenCallRealMethod();

        // Mock依赖方法
        Mockito.lenient().when(numberIdGenerator.generateId(anyString())).thenReturn(1L);
        Mockito.lenient().when(parmCardCurrencyInfoMapper.insert(any(ParmCardCurrencyInfo.class))).thenReturn(1);
        Mockito.lenient().when(parmCardCurrencyInfoMapper.deleteByPrimaryKey(anyLong())).thenReturn(1);
        Mockito.lenient().when(parmCardCurrencyInfoSelfMapper.selectByOrgAndProductNum(anyString(), anyString()))
                .thenReturn(cardCurrencyInfoList);
    }

    @Test
    void testAdd_Success() {
        // Arrange - 成功路径测试
        
        // Act
        List<CardProductCurrencyRelationResDTO> result = cardProductCurrencyRelationService.add(reqDTO);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(parmCardCurrencyInfoMapper, times(2)).insert(any(ParmCardCurrencyInfo.class)); // USD和CNY两个币种
        verify(numberIdGenerator, times(2)).generateId("tenant001");
    }

    @Test
    void testAdd_EmptyOrganizationNumber() {
        // Arrange - 机构号为空测试
        reqDTO.setOrganizationNumber("");

        // Act & Assert
        assertDoesNotThrow(() -> cardProductCurrencyRelationService.add(reqDTO));
        // 由于代码中只是记录错误日志，不抛异常，所以测试不抛异常
    }

    @Test
    void testAdd_EmptyProductNumber() {
        // Arrange - 产品编号为空测试
        reqDTO.setProductNumber("");

        // Act & Assert
        assertDoesNotThrow(() -> cardProductCurrencyRelationService.add(reqDTO));
        // 由于代码中只是记录错误日志，不抛异常，所以测试不抛异常
    }

    @Test
    void testAdd_EmptyCurrencyList() {
        // Arrange - 币种列表为空测试
        reqDTO.setCurrencyList(Collections.emptyList());

        // Act & Assert
        assertDoesNotThrow(() -> cardProductCurrencyRelationService.add(reqDTO));
        // 由于代码中只是记录错误日志，不抛异常，所以测试不抛异常
    }

    @Test
    void testRemove_Success() {
        // Arrange
        String organizationNumber = "1001";
        String productNumber = "P001";

        // Act
        Boolean result = cardProductCurrencyRelationService.remove(organizationNumber, productNumber);

        // Assert
        assertTrue(result);
        verify(parmCardCurrencyInfoSelfMapper, times(1)).selectByOrgAndProductNum(organizationNumber, productNumber);
        verify(parmCardCurrencyInfoMapper, times(1)).deleteByPrimaryKey(1L);
    }

    @Test
    void testRemove_NotFound() {
        // Arrange - 未找到数据测试
        String organizationNumber = "1001";
        String productNumber = "P001";
        
        Mockito.lenient().when(parmCardCurrencyInfoSelfMapper.selectByOrgAndProductNum(organizationNumber, productNumber))
                .thenReturn(Collections.emptyList());

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> cardProductCurrencyRelationService.remove(organizationNumber, productNumber));
        
        assertEquals(AnyTxnParameterRespCodeEnum.S_QUERY_PARM_CARD_CURRENCY_INFO_BY_ORGNUM_PRODUCTNUM_FAULT.getCode(), exception.getErrCode());
    }

    @Test
    void testModify_Success() {
        // Arrange
        reqDTO.setCurrencyList(Arrays.asList("EUR", "GBP"));

        // Act
        CardProductCurrencyRelationResDTO result = cardProductCurrencyRelationService.modify(reqDTO);

        // Assert
        assertNotNull(result);
        verify(parmCardCurrencyInfoSelfMapper, times(1)).selectByOrgAndProductNum(reqDTO.getOrganizationNumber(), reqDTO.getProductNumber());
        verify(parmCardCurrencyInfoMapper, times(1)).deleteByPrimaryKey(1L);
        verify(parmCardCurrencyInfoMapper, times(2)).insert(any(ParmCardCurrencyInfo.class)); // EUR和GBP两个币种
    }

    @Test
    void testModify_NotFound() {
        // Arrange - 未找到数据测试
        Mockito.lenient().when(parmCardCurrencyInfoSelfMapper.selectByOrgAndProductNum(reqDTO.getOrganizationNumber(), reqDTO.getProductNumber()))
                .thenReturn(Collections.emptyList());

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> cardProductCurrencyRelationService.modify(reqDTO));
        
        assertEquals(AnyTxnParameterRespCodeEnum.S_QUERY_PARM_CARD_CURRENCY_INFO_BY_ORGNUM_PRODUCTNUM_FAULT.getCode(), exception.getErrCode());
    }

    @Test
    void testFind_Success() {
        // Arrange
        String organizationNumber = "1001";
        String productNumber = "P001";

        // Act
        CardProductCurrencyRelationResDTO result = cardProductCurrencyRelationService.find(organizationNumber, productNumber);

        // Assert
        assertNotNull(result);
        verify(parmCardCurrencyInfoSelfMapper, times(1)).selectByOrgAndProductNum(organizationNumber, productNumber);
    }

    @Test
    void testFind_EmptyParameters() {
        // Arrange - 参数为空测试
        String organizationNumber = "";
        String productNumber = "P001";

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> cardProductCurrencyRelationService.find(organizationNumber, productNumber));
        
        assertEquals(AnyTxnParameterRespCodeEnum.P_PARAMETER_EMPTY_FAULT.getCode(), exception.getErrCode());
    }

    @Test
    void testFind_NotFound() {
        // Arrange - 未找到数据测试
        String organizationNumber = "1001";
        String productNumber = "P001";
        
        Mockito.lenient().when(parmCardCurrencyInfoSelfMapper.selectByOrgAndProductNum(organizationNumber, productNumber))
                .thenReturn(Collections.emptyList());

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> cardProductCurrencyRelationService.find(organizationNumber, productNumber));
        
        assertEquals(AnyTxnParameterRespCodeEnum.S_QUERY_PARM_CARD_CURRENCY_INFO_BY_ORGNUM_PRODUCTNUM_FAULT.getCode(), exception.getErrCode());
    }

    @Test
    void testFindAll_Success() {
        // Arrange
        Integer pageNum = 1;
        Integer pageSize = 10;

        Mockito.lenient().when(parmCardCurrencyInfoSelfMapper.selectAllWithDesc(false, "1001"))
                .thenReturn(cardCurrencyInfoList);

        // Act
        PageResultDTO<CardProductCurrencyRelationResDTO> result = cardProductCurrencyRelationService.findAll(pageNum, pageSize);

        // Assert
        assertNotNull(result);
        assertEquals(pageNum, result.getPage());
        verify(parmCardCurrencyInfoSelfMapper, times(1)).selectAllWithDesc(false, "1001");
    }

    @Test
    void testFindAll_EmptyResult() {
        // Arrange - 空结果测试
        Integer pageNum = 1;
        Integer pageSize = 10;

        Mockito.lenient().when(parmCardCurrencyInfoSelfMapper.selectAllWithDesc(false, "1001"))
                .thenReturn(Collections.emptyList());

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> cardProductCurrencyRelationService.findAll(pageNum, pageSize));

        assertEquals(AnyTxnParameterRespCodeEnum.S_PAGE_QUERY_PARM_CARD_CURRENCY_INFO_FAULT.getCode(), exception.getErrCode());
    }

    @Test
    void testFindDefaultCurrency_SingleRecord() {
        // Arrange - 单条记录测试
        String productNumber = "P001";
        List<ParmCardCurrencyInfo> singleRecord = Arrays.asList(parmCardCurrencyInfo);
        
        Mockito.lenient().when(parmCardCurrencyInfoSelfMapper.selectByOrgAndProductNum("1001", productNumber))
                .thenReturn(singleRecord);

        // Act
        String result = cardProductCurrencyRelationService.findDefaultCurrency(organizationInfoResDTO, productNumber);

        // Assert
        assertEquals("USD", result);
        verify(parmCardCurrencyInfoSelfMapper, times(1)).selectByOrgAndProductNum("1001", productNumber);
    }

    @Test
    void testFindDefaultCurrency_MultipleRecords() {
        // Arrange - 多条记录测试，返回机构主币种
        String productNumber = "P001";
        ParmCardCurrencyInfo info2 = new ParmCardCurrencyInfo();
        info2.setCurrencyCode("EUR");
        List<ParmCardCurrencyInfo> multipleRecords = Arrays.asList(parmCardCurrencyInfo, info2);
        
        Mockito.lenient().when(parmCardCurrencyInfoSelfMapper.selectByOrgAndProductNum("1001", productNumber))
                .thenReturn(multipleRecords);

        // Act
        String result = cardProductCurrencyRelationService.findDefaultCurrency(organizationInfoResDTO, productNumber);

        // Assert
        assertEquals("CNY", result); // 返回机构主币种
        verify(parmCardCurrencyInfoSelfMapper, times(1)).selectByOrgAndProductNum("1001", productNumber);
    }

    @Test
    void testFindDefaultCurrency_NoRecords() {
        // Arrange - 无记录测试，返回机构主币种
        String productNumber = "P001";
        
        Mockito.lenient().when(parmCardCurrencyInfoSelfMapper.selectByOrgAndProductNum("1001", productNumber))
                .thenReturn(Collections.emptyList());

        // Act
        String result = cardProductCurrencyRelationService.findDefaultCurrency(organizationInfoResDTO, productNumber);

        // Assert
        assertEquals("CNY", result); // 返回机构主币种
        verify(parmCardCurrencyInfoSelfMapper, times(1)).selectByOrgAndProductNum("1001", productNumber);
    }

    @AfterEach
    void tearDown() {
        // 关闭静态Mock
        if (beanMappingMockedStatic != null) {
            beanMappingMockedStatic.close();
        }
        if (tenantUtilsMockedStatic != null) {
            tenantUtilsMockedStatic.close();
        }
        if (orgNumberUtilsMockedStatic != null) {
            orgNumberUtilsMockedStatic.close();
        }
        if (stringUtilsMockedStatic != null) {
            stringUtilsMockedStatic.close();
        }
    }
}
