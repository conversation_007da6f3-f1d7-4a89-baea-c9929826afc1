package com.anytech.anytxn.parameter.authorization.service;

import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.parameter.authorization.service.ParmTypeDetailCodeServiceImpl;
import com.anytech.anytxn.parameter.authorization.mapper.ParmTypeDetailCodeSelfMapper;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.ParmTypeDetailCodeDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.dto.ParmTypeDetailCodeReqDTO;
import com.anytech.anytxn.parameter.base.authorization.domain.model.ParmTypeDetailCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ParmTypeDetailCodeService单元测试类
 * 
 * 测试ParmTypeDetailCodeServiceImpl的核心方法：
 * 1. findByCondition - 根据条件查询交易细分码
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class ParmTypeDetailCodeServiceTest {

    @Mock
    private ParmTypeDetailCodeSelfMapper selfMapper;

    @InjectMocks
    private ParmTypeDetailCodeServiceImpl parmTypeDetailCodeService;

    private ParmTypeDetailCodeReqDTO reqDTO;
    private ParmTypeDetailCode parmTypeDetailCode;
    private ParmTypeDetailCodeDTO expectedDTO;

    @BeforeEach
    void setUp() {
        // Mock OrgNumberUtils 避免构造函数中的空指针异常
        try (MockedStatic<OrgNumberUtils> mockOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockOrgNumberUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("1001");
            
            // 创建测试请求DTO
            reqDTO = new ParmTypeDetailCodeReqDTO();
            reqDTO.setOrganizationNumber("1001");
            reqDTO.setTransactionTypeDetailCode("001");
            reqDTO.setTransactionTypeTopCode("TOP001");
            reqDTO.setCountryCode("CN");
            reqDTO.setDebitCreditIndicator("D");
            reqDTO.setInterfaceNumber("IF001");
            reqDTO.setVersionNumber(1L);
            reqDTO.setUpdateBy("TEST_USER");
            reqDTO.setParmJson("{\"test\":\"data\"}");

            // 创建测试实体对象
            parmTypeDetailCode = new ParmTypeDetailCode();
            parmTypeDetailCode.setId("1");
            parmTypeDetailCode.setOrganizationNumber("1001");
            parmTypeDetailCode.setTransactionTypeDetailCode("001");
            parmTypeDetailCode.setTransactionTypeTopCode("TOP001");
            parmTypeDetailCode.setCountryCode("CN");
            parmTypeDetailCode.setDebitCreditIndicator("D");
            parmTypeDetailCode.setPostingTransactionCode("POST001");
            parmTypeDetailCode.setInterfaceNumber("IF001");
            parmTypeDetailCode.setCreateTime(LocalDateTime.now());
            parmTypeDetailCode.setUpdateTime(LocalDateTime.now());
            parmTypeDetailCode.setVersionNumber(1L);
            parmTypeDetailCode.setUpdateBy("TEST_USER");
            parmTypeDetailCode.setParmJson("{\"test\":\"data\"}");

            // 创建期望的DTO对象
            expectedDTO = new ParmTypeDetailCodeDTO();
            expectedDTO.setId(BigDecimal.valueOf(1L));
            expectedDTO.setOrganizationNumber("1001");
            expectedDTO.setTransactionTypeDetailCode("001");
            expectedDTO.setTransactionTypeTopCode("TOP001");
            expectedDTO.setCountryCode("CN");
            expectedDTO.setDebitCreditIndicator("D");
            expectedDTO.setPostingTransactionCode("POST001");
            expectedDTO.setInterfaceNumber("IF001");
            expectedDTO.setCreateTime(LocalDateTime.now());
            expectedDTO.setUpdateTime(LocalDateTime.now());
            expectedDTO.setVersionNumber(1L);
            expectedDTO.setUpdateBy("TEST_USER");
            expectedDTO.setParmJson("{\"test\":\"data\"}");
        }
    }

    // ==================== findByCondition 测试 ====================

    @Test
    void testFindByCondition_Success() {
        // Arrange
        when(selfMapper.selectByCondition(reqDTO)).thenReturn(parmTypeDetailCode);

        try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {
            mockBeanMapping.when(() -> BeanMapping.copy(parmTypeDetailCode, ParmTypeDetailCodeDTO.class))
                    .thenReturn(expectedDTO);

            // Act
            ParmTypeDetailCodeDTO result = parmTypeDetailCodeService.findByCondition(reqDTO);

            // Assert
            assertNotNull(result);
            assertEquals(BigDecimal.valueOf(1L), result.getId());
            assertEquals("1001", result.getOrganizationNumber());
            assertEquals("001", result.getTransactionTypeDetailCode());
            assertEquals("TOP001", result.getTransactionTypeTopCode());
            assertEquals("CN", result.getCountryCode());
            assertEquals("D", result.getDebitCreditIndicator());
            assertEquals("POST001", result.getPostingTransactionCode());
            assertEquals("IF001", result.getInterfaceNumber());
            assertEquals(1L, result.getVersionNumber());
            assertEquals("TEST_USER", result.getUpdateBy());
            assertEquals("{\"test\":\"data\"}", result.getParmJson());
            
            verify(selfMapper).selectByCondition(reqDTO);
            mockBeanMapping.verify(() -> BeanMapping.copy(parmTypeDetailCode, ParmTypeDetailCodeDTO.class));
        }
    }

    @Test
    void testFindByCondition_NotFound() {
        // Arrange
        when(selfMapper.selectByCondition(reqDTO)).thenReturn(null);

        // Act
        ParmTypeDetailCodeDTO result = parmTypeDetailCodeService.findByCondition(reqDTO);

        // Assert
        assertNull(result);
        verify(selfMapper).selectByCondition(reqDTO);
    }

        @Test
    void testFindByCondition_WithMinimalParameters() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> mockOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockOrgNumberUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("1001");
            
            ParmTypeDetailCodeReqDTO minimalReqDTO = new ParmTypeDetailCodeReqDTO();
            minimalReqDTO.setOrganizationNumber("1001");
            minimalReqDTO.setTransactionTypeDetailCode("001");

            ParmTypeDetailCode minimalEntity = new ParmTypeDetailCode();
            minimalEntity.setId("2");
            minimalEntity.setOrganizationNumber("1001");
            minimalEntity.setTransactionTypeDetailCode("001");

            ParmTypeDetailCodeDTO minimalDTO = new ParmTypeDetailCodeDTO();
            minimalDTO.setId(BigDecimal.valueOf(2L));
            minimalDTO.setOrganizationNumber("1001");
            minimalDTO.setTransactionTypeDetailCode("001");

            when(selfMapper.selectByCondition(minimalReqDTO)).thenReturn(minimalEntity);

            try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {
                mockBeanMapping.when(() -> BeanMapping.copy(minimalEntity, ParmTypeDetailCodeDTO.class))
                        .thenReturn(minimalDTO);

                // Act
                ParmTypeDetailCodeDTO result = parmTypeDetailCodeService.findByCondition(minimalReqDTO);

                // Assert
                assertNotNull(result);
                assertEquals(BigDecimal.valueOf(2L), result.getId());
                assertEquals("1001", result.getOrganizationNumber());
                assertEquals("001", result.getTransactionTypeDetailCode());
                
                verify(selfMapper).selectByCondition(minimalReqDTO);
                mockBeanMapping.verify(() -> BeanMapping.copy(minimalEntity, ParmTypeDetailCodeDTO.class));
            }
        }
    }

        @Test
    void testFindByCondition_WithAllParameters() {
        // Arrange
        try (MockedStatic<OrgNumberUtils> mockOrgNumberUtils = mockStatic(OrgNumberUtils.class)) {
            mockOrgNumberUtils.when(() -> OrgNumberUtils.getOrg()).thenReturn("1001");
            
            ParmTypeDetailCodeReqDTO fullReqDTO = new ParmTypeDetailCodeReqDTO();
            fullReqDTO.setOrganizationNumber("1001");
            fullReqDTO.setTransactionTypeDetailCode("002");
            fullReqDTO.setTransactionTypeTopCode("TOP002");
            fullReqDTO.setCountryCode("US");
            fullReqDTO.setDebitCreditIndicator("C");
            fullReqDTO.setInterfaceNumber("IF002");

            ParmTypeDetailCode fullEntity = new ParmTypeDetailCode();
            fullEntity.setId("3");
            fullEntity.setOrganizationNumber("1001");
            fullEntity.setTransactionTypeDetailCode("002");
            fullEntity.setTransactionTypeTopCode("TOP002");
            fullEntity.setCountryCode("US");
            fullEntity.setDebitCreditIndicator("C");
            fullEntity.setPostingTransactionCode("POST002");
            fullEntity.setInterfaceNumber("IF002");

            ParmTypeDetailCodeDTO fullDTO = new ParmTypeDetailCodeDTO();
            fullDTO.setId(BigDecimal.valueOf(3L));
            fullDTO.setOrganizationNumber("1001");
            fullDTO.setTransactionTypeDetailCode("002");
            fullDTO.setTransactionTypeTopCode("TOP002");
            fullDTO.setCountryCode("US");
            fullDTO.setDebitCreditIndicator("C");
            fullDTO.setPostingTransactionCode("POST002");
            fullDTO.setInterfaceNumber("IF002");

            when(selfMapper.selectByCondition(fullReqDTO)).thenReturn(fullEntity);

            try (MockedStatic<BeanMapping> mockBeanMapping = mockStatic(BeanMapping.class)) {
                mockBeanMapping.when(() -> BeanMapping.copy(fullEntity, ParmTypeDetailCodeDTO.class))
                        .thenReturn(fullDTO);

                // Act
                ParmTypeDetailCodeDTO result = parmTypeDetailCodeService.findByCondition(fullReqDTO);

                // Assert
                assertNotNull(result);
                assertEquals(BigDecimal.valueOf(3L), result.getId());
                assertEquals("1001", result.getOrganizationNumber());
                assertEquals("002", result.getTransactionTypeDetailCode());
                assertEquals("TOP002", result.getTransactionTypeTopCode());
                assertEquals("US", result.getCountryCode());
                assertEquals("C", result.getDebitCreditIndicator());
                assertEquals("POST002", result.getPostingTransactionCode());
                assertEquals("IF002", result.getInterfaceNumber());
                
                verify(selfMapper).selectByCondition(fullReqDTO);
                mockBeanMapping.verify(() -> BeanMapping.copy(fullEntity, ParmTypeDetailCodeDTO.class));
            }
        }
    }

    @Test
    void testFindByCondition_WithNullRequest() {
        // Arrange
        ParmTypeDetailCodeReqDTO nullReqDTO = null;

        // Act
        ParmTypeDetailCodeDTO result = parmTypeDetailCodeService.findByCondition(nullReqDTO);

        // Assert
        assertNull(result);
        verify(selfMapper).selectByCondition(nullReqDTO);
    }

    @Test
    void testFindByCondition_MapperThrowsException() {
        // Arrange
        when(selfMapper.selectByCondition(reqDTO)).thenThrow(new RuntimeException("Database error"));

        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class,
                () -> parmTypeDetailCodeService.findByCondition(reqDTO));
        assertEquals("Database error", exception.getMessage());
        
        verify(selfMapper).selectByCondition(reqDTO);
    }
} 
