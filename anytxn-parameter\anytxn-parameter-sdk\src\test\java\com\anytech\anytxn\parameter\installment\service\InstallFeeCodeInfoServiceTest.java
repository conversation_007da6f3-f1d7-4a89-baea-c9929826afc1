package com.anytech.anytxn.parameter.installment.service;

import com.alibaba.fastjson.JSON;
import com.anytech.anytxn.business.base.audit.ParmModificationRecord;
import com.anytech.anytxn.common.core.base.PageResultDTO;
import com.anytech.anytxn.common.core.base.ParameterCompare;
import com.anytech.anytxn.common.core.utils.BeanMapping;
import com.anytech.anytxn.common.core.utils.OrgNumberUtils;
import com.anytech.anytxn.common.core.utils.TenantUtils;
import com.anytech.anytxn.common.sequence.utils.Number16IdGen;
import com.anytech.anytxn.parameter.base.common.constants.Constants;
import com.anytech.anytxn.parameter.base.common.enums.AnyTxnParameterRespCodeEnum;
import com.anytech.anytxn.parameter.base.common.exception.AnyTxnParameterException;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallFeeCodeInfoReqDTO;
import com.anytech.anytxn.parameter.base.installment.domain.dto.InstallFeeCodeInfoResDTO;
import com.anytech.anytxn.parameter.base.installment.domain.model.InstallFeeCodeInfo;
import com.anytech.anytxn.parameter.installment.mapper.InstallFeeCodeInfoMapper;
import com.anytech.anytxn.parameter.installment.mapper.InstallFeeCodeInfoSelfMapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * InstallFeeCodeInfoService 单元测试类
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
class InstallFeeCodeInfoServiceTest {

    @Mock
    private InstallFeeCodeInfoMapper installFeeCodeInfoMapper;
    
    @Mock
    private InstallFeeCodeInfoSelfMapper installFeeCodeInfoSelfMapper;
    
    @Mock
    private Number16IdGen numberIdGenerator;

    @InjectMocks
    private InstallFeeCodeInfoServiceImpl installFeeCodeInfoService;

    private MockedStatic<OrgNumberUtils> orgNumberUtilsMockedStatic;
    private MockedStatic<TenantUtils> tenantUtilsMockedStatic;
    private MockedStatic<BeanMapping> beanMappingMockedStatic;
    private MockedStatic<PageHelper> pageHelperMockedStatic;
    private MockedStatic<StringUtils> stringUtilsMockedStatic;

    @BeforeEach
    void setUp() {
        orgNumberUtilsMockedStatic = mockStatic(OrgNumberUtils.class);
        tenantUtilsMockedStatic = mockStatic(TenantUtils.class);
        beanMappingMockedStatic = mockStatic(BeanMapping.class);
        pageHelperMockedStatic = mockStatic(PageHelper.class);
        stringUtilsMockedStatic = mockStatic(StringUtils.class);

        // 设置静态方法的默认返回值
        Mockito.lenient().when(OrgNumberUtils.getOrg()).thenReturn("0001");
        Mockito.lenient().when(TenantUtils.getTenantId()).thenReturn("6001");
        Mockito.lenient().when(StringUtils.isEmpty(anyString())).thenReturn(false);
    }

    @AfterEach
    void tearDown() {
        if (orgNumberUtilsMockedStatic != null) {
            orgNumberUtilsMockedStatic.close();
        }
        if (tenantUtilsMockedStatic != null) {
            tenantUtilsMockedStatic.close();
        }
        if (beanMappingMockedStatic != null) {
            beanMappingMockedStatic.close();
        }
        if (pageHelperMockedStatic != null) {
            pageHelperMockedStatic.close();
        }
        if (stringUtilsMockedStatic != null) {
            stringUtilsMockedStatic.close();
        }
    }

    /**
     * 测试添加分期费用代码 - 成功场景
     */
    @Test
    void testAdd_Success() {
        // Arrange
        InstallFeeCodeInfoReqDTO reqDTO = createValidInstallFeeCodeInfoReqDTO();
        InstallFeeCodeInfo feeCodeInfo = createValidInstallFeeCodeInfo();
        
        Mockito.lenient().when(installFeeCodeInfoSelfMapper.isExists(reqDTO.getOrganizationNumber(), reqDTO.getFeeCodeId())).thenReturn(0);
        Mockito.lenient().when(numberIdGenerator.generateId(anyString())).thenReturn(*********L);
        beanMappingMockedStatic.when(() -> BeanMapping.copy(reqDTO, InstallFeeCodeInfo.class)).thenReturn(feeCodeInfo);

        // Act
        ParameterCompare result = installFeeCodeInfoService.add(reqDTO);

        // Assert
        assertNotNull(result);
        verify(installFeeCodeInfoSelfMapper).isExists(reqDTO.getOrganizationNumber(), reqDTO.getFeeCodeId());
    }

    /**
     * 测试添加分期费用代码 - 必填参数为空
     */
    @Test
    void testAdd_RequiredParametersEmpty() {
        // Arrange
        InstallFeeCodeInfoReqDTO reqDTO = new InstallFeeCodeInfoReqDTO();
        reqDTO.setOrganizationNumber("");

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> installFeeCodeInfoService.add(reqDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_ORG_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试添加分期费用代码 - 数据已存在
     */
    @Test
    void testAdd_DataExists() {
        // Arrange
        InstallFeeCodeInfoReqDTO reqDTO = createValidInstallFeeCodeInfoReqDTO();
        
        Mockito.lenient().when(installFeeCodeInfoSelfMapper.isExists(reqDTO.getOrganizationNumber(), reqDTO.getFeeCodeId())).thenReturn(1);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> installFeeCodeInfoService.add(reqDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.D_INSTALL_FEE_CODE_REFUSE_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试删除分期费用代码 - 成功场景
     */
    @Test
    void testRemove_Success() {
        // Arrange
        String id = "*********";
        InstallFeeCodeInfo feeCodeInfo = createValidInstallFeeCodeInfo();
        
        Mockito.lenient().when(installFeeCodeInfoMapper.selectByPrimaryKey(id)).thenReturn(feeCodeInfo);

        // Act
        ParameterCompare result = installFeeCodeInfoService.remove(id);

        // Assert
        assertNotNull(result);
        verify(installFeeCodeInfoMapper).selectByPrimaryKey(id);
    }

    /**
     * 测试删除分期费用代码 - ID为空
     */
    @Test
    void testRemove_IdNull() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> installFeeCodeInfoService.remove(null));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试删除分期费用代码 - 数据不存在
     */
    @Test
    void testRemove_DataNotExists() {
        // Arrange
        String id = "*********";
        
        Mockito.lenient().when(installFeeCodeInfoMapper.selectByPrimaryKey(id)).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> installFeeCodeInfoService.remove(id));
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_FEE_CODE_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试修改分期费用代码 - 成功场景
     */
    @Test
    void testModify_Success() {
        // Arrange
        InstallFeeCodeInfoReqDTO reqDTO = createValidInstallFeeCodeInfoReqDTO();
        InstallFeeCodeInfo existingFeeCodeInfo = createValidInstallFeeCodeInfo();
        InstallFeeCodeInfo updatedFeeCodeInfo = createValidInstallFeeCodeInfo();
        
        Mockito.lenient().when(installFeeCodeInfoMapper.selectByPrimaryKey(reqDTO.getId())).thenReturn(existingFeeCodeInfo);
        Mockito.lenient().when(installFeeCodeInfoSelfMapper.selectByIndex(existingFeeCodeInfo.getOrganizationNumber(), existingFeeCodeInfo.getFeeCodeId())).thenReturn(null);
        beanMappingMockedStatic.when(() -> BeanMapping.copy(reqDTO, InstallFeeCodeInfo.class)).thenReturn(updatedFeeCodeInfo);

        // Act
        ParameterCompare result = installFeeCodeInfoService.modify(reqDTO);

        // Assert
        assertNotNull(result);
        verify(installFeeCodeInfoMapper).selectByPrimaryKey(reqDTO.getId());
    }

    /**
     * 测试修改分期费用代码 - ID为空
     */
    @Test
    void testModify_IdNull() {
        // Arrange
        InstallFeeCodeInfoReqDTO reqDTO = createValidInstallFeeCodeInfoReqDTO();
        reqDTO.setId(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class, 
            () -> installFeeCodeInfoService.modify(reqDTO));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 创建有效的InstallFeeCodeInfoReqDTO对象
     */
    private InstallFeeCodeInfoReqDTO createValidInstallFeeCodeInfoReqDTO() {
        InstallFeeCodeInfoReqDTO reqDTO = new InstallFeeCodeInfoReqDTO();
        reqDTO.setId("*********");
        reqDTO.setOrganizationNumber("0001");
        reqDTO.setFeeCodeId("F00001");
        reqDTO.setFeeComputeFlag("Y");
        reqDTO.setFeeType("A");
        reqDTO.setChargeOption("1");
        reqDTO.setBaseAmountFlag("1");
        reqDTO.setThresholdAmount1(new BigDecimal("100.00"));
        reqDTO.setFeeRate1(new BigDecimal("0.05"));
        reqDTO.setThresholdAmount2(new BigDecimal("500.00"));
        reqDTO.setFeeRate2(new BigDecimal("0.03"));
        reqDTO.setThresholdAmount3(new BigDecimal("1000.00"));
        reqDTO.setFeeRate3(new BigDecimal("0.02"));
        reqDTO.setMinFeeAmount(new BigDecimal("10.00"));
        reqDTO.setMaxFeeAmount(new BigDecimal("100.00"));
        reqDTO.setDerateMethod("1");
        reqDTO.setDerateValue(new BigDecimal("5.00"));
        reqDTO.setPrincipalRelief("1");
        reqDTO.setPrincipalAmountValue(new BigDecimal("50.00"));
        reqDTO.setPrincipalTermValue(new BigDecimal("3.00"));
        reqDTO.setFeeDesc("Test Fee Code");
        reqDTO.setStatus(Constants.ENABLED);
        reqDTO.setUnReceiveFeeTerm(6);
        reqDTO.setAnnualInterestRate(new BigDecimal("0.12"));
        reqDTO.setGstInterestRate(new BigDecimal("0.10"));
        reqDTO.setOneTimeChargeFlag("Y");
        reqDTO.setUpdateBy("TEST_USER");
        reqDTO.setVersionNumber(1L);
        return reqDTO;
    }

    /**
     * 创建有效的InstallFeeCodeInfo对象
     */
    private InstallFeeCodeInfo createValidInstallFeeCodeInfo() {
        InstallFeeCodeInfo feeCodeInfo = new InstallFeeCodeInfo();
        feeCodeInfo.setId("*********");
        feeCodeInfo.setOrganizationNumber("0001");
        feeCodeInfo.setFeeCodeId("F00001");
        feeCodeInfo.setFeeComputeFlag("Y");
        feeCodeInfo.setFeeType("A");
        feeCodeInfo.setChargeOption("1");
        feeCodeInfo.setBaseAmountFlag("1");
        feeCodeInfo.setThresholdAmount1(new BigDecimal("100.00"));
        feeCodeInfo.setFeeRate1(new BigDecimal("0.05"));
        feeCodeInfo.setThresholdAmount2(new BigDecimal("500.00"));
        feeCodeInfo.setFeeRate2(new BigDecimal("0.03"));
        feeCodeInfo.setThresholdAmount3(new BigDecimal("1000.00"));
        feeCodeInfo.setFeeRate3(new BigDecimal("0.02"));
        feeCodeInfo.setMinFeeAmount(new BigDecimal("10.00"));
        feeCodeInfo.setMaxFeeAmount(new BigDecimal("100.00"));
        feeCodeInfo.setDerateMethod("1");
        feeCodeInfo.setDerateValue(new BigDecimal("5.00"));
        feeCodeInfo.setPrincipalRelief("1");
        feeCodeInfo.setPrincipalAmountValue(new BigDecimal("50.00"));
        feeCodeInfo.setPrincipalTermValue(new BigDecimal("3.00"));
        feeCodeInfo.setFeeDesc("Test Fee Code");
        feeCodeInfo.setStatus(Constants.ENABLED);
        feeCodeInfo.setUnReceiveFeeTerm(6);
        feeCodeInfo.setAnnualInterestRate(new BigDecimal("0.12"));
        feeCodeInfo.setGstInterestRate(new BigDecimal("0.10"));
        feeCodeInfo.setOneTimeChargeFlag("Y");
        feeCodeInfo.setVersionNumber(1L);
        feeCodeInfo.setCreateTime(LocalDateTime.now());
        feeCodeInfo.setUpdateTime(LocalDateTime.now());
        feeCodeInfo.setUpdateBy("TEST_USER");
        return feeCodeInfo;
    }

    /**
     * 测试分页查询分期费用代码 - 成功场景
     */
    @Test
    void testFindPage_Success() {
        // Arrange
        Integer pageNum = 1;
        Integer pageSize = 10;
        InstallFeeCodeInfoReqDTO reqDTO = new InstallFeeCodeInfoReqDTO();
        reqDTO.setOrganizationNumber("0001");

        List<InstallFeeCodeInfo> feeCodeInfoList = Collections.singletonList(createValidInstallFeeCodeInfo());
        List<InstallFeeCodeInfoResDTO> expectedResults = Collections.singletonList(createValidInstallFeeCodeInfoResDTO());
        Page<InstallFeeCodeInfo> page = new Page<>(pageNum, pageSize);
        page.setTotal(1);
        page.setPages(1);

        pageHelperMockedStatic.when(() -> PageHelper.startPage(pageNum, pageSize)).thenReturn(page);
        Mockito.lenient().when(installFeeCodeInfoSelfMapper.selectByCondition(any(InstallFeeCodeInfoReqDTO.class))).thenReturn(feeCodeInfoList);
        beanMappingMockedStatic.when(() -> BeanMapping.copyList(feeCodeInfoList, InstallFeeCodeInfoResDTO.class)).thenReturn(expectedResults);

        // Act
        PageResultDTO<InstallFeeCodeInfoResDTO> result = installFeeCodeInfoService.findPage(pageNum, pageSize, reqDTO);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getData().size());
        verify(installFeeCodeInfoSelfMapper).selectByCondition(any(InstallFeeCodeInfoReqDTO.class));
    }

    /**
     * 测试根据ID查询分期费用代码 - 成功场景
     */
    @Test
    void testGetById_Success() {
        // Arrange
        String id = "*********";
        InstallFeeCodeInfo feeCodeInfo = createValidInstallFeeCodeInfo();
        InstallFeeCodeInfoResDTO expectedResult = createValidInstallFeeCodeInfoResDTO();

        Mockito.lenient().when(installFeeCodeInfoMapper.selectByPrimaryKey(id)).thenReturn(feeCodeInfo);
        beanMappingMockedStatic.when(() -> BeanMapping.copy(feeCodeInfo, InstallFeeCodeInfoResDTO.class)).thenReturn(expectedResult);

        // Act
        InstallFeeCodeInfoResDTO result = installFeeCodeInfoService.getById(id);

        // Assert
        assertNotNull(result);
        assertEquals(expectedResult.getId(), result.getId());
        verify(installFeeCodeInfoMapper).selectByPrimaryKey(id);
    }

    /**
     * 测试根据ID查询分期费用代码 - ID为空
     */
    @Test
    void testGetById_IdNull() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installFeeCodeInfoService.getById(null));
        assertEquals(AnyTxnParameterRespCodeEnum.P_PRIMARY_KEY_NULL_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试根据ID查询分期费用代码 - 数据不存在
     */
    @Test
    void testGetById_DataNotExists() {
        // Arrange
        String id = "*********";

        Mockito.lenient().when(installFeeCodeInfoMapper.selectByPrimaryKey(id)).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installFeeCodeInfoService.getById(id));
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_FEE_CODE_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试根据机构号和费用代码查询 - 成功场景
     */
    @Test
    void testGetByIndex_Success() {
        // Arrange
        String organizationNumber = "0001";
        String feeCode = "F00001";
        InstallFeeCodeInfo feeCodeInfo = createValidInstallFeeCodeInfo();
        InstallFeeCodeInfoResDTO expectedResult = createValidInstallFeeCodeInfoResDTO();

        Mockito.lenient().when(installFeeCodeInfoSelfMapper.selectByIndex(organizationNumber, feeCode)).thenReturn(feeCodeInfo);
        beanMappingMockedStatic.when(() -> BeanMapping.copy(feeCodeInfo, InstallFeeCodeInfoResDTO.class)).thenReturn(expectedResult);

        // Act
        InstallFeeCodeInfoResDTO result = installFeeCodeInfoService.getByIndex(organizationNumber, feeCode);

        // Assert
        assertNotNull(result);
        assertEquals(expectedResult.getId(), result.getId());
        verify(installFeeCodeInfoSelfMapper).selectByIndex(organizationNumber, feeCode);
    }

    /**
     * 测试根据机构号和费用代码查询 - 机构号为空
     */
    @Test
    void testGetByIndex_OrganizationNumberEmpty() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installFeeCodeInfoService.getByIndex("", "F00001"));
        assertEquals(AnyTxnParameterRespCodeEnum.D_INSTALL_ACCOUNTING_TRANS_PARM_ORG_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试根据机构号和费用代码查询 - 费用代码为空
     */
    @Test
    void testGetByIndex_FeeCodeEmpty() {
        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installFeeCodeInfoService.getByIndex("0001", ""));
        assertEquals(AnyTxnParameterRespCodeEnum.D_INSTALL_FEE_CODE_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 测试根据机构号和费用代码查询 - 数据不存在
     */
    @Test
    void testGetByIndex_DataNotExists() {
        // Arrange
        String organizationNumber = "0001";
        String feeCode = "F00001";

        Mockito.lenient().when(installFeeCodeInfoSelfMapper.selectByIndex(organizationNumber, feeCode)).thenReturn(null);

        // Act & Assert
        AnyTxnParameterException exception = assertThrows(AnyTxnParameterException.class,
            () -> installFeeCodeInfoService.getByIndex(organizationNumber, feeCode));
        assertEquals(AnyTxnParameterRespCodeEnum.D_QUERY_INSTALL_FEE_CODE_BY_ORG_FAULT.getCode(), exception.getErrCode());
    }

    /**
     * 创建有效的InstallFeeCodeInfoResDTO对象
     */
    private InstallFeeCodeInfoResDTO createValidInstallFeeCodeInfoResDTO() {
        InstallFeeCodeInfoResDTO resDTO = new InstallFeeCodeInfoResDTO();
        resDTO.setId("*********");
        resDTO.setOrganizationNumber("0001");
        resDTO.setFeeCodeId("F00001");
        resDTO.setFeeComputeFlag("Y");
        resDTO.setFeeType("A");
        resDTO.setFeeReceiveFlag("F");
        resDTO.setChargeOption("1");
        resDTO.setBaseAmountFlag("1");
        resDTO.setThresholdAmount1(new BigDecimal("100.00"));
        resDTO.setFeeRate1(new BigDecimal("0.05"));
        resDTO.setThresholdAmount2(new BigDecimal("500.00"));
        resDTO.setFeeRate2(new BigDecimal("0.03"));
        resDTO.setThresholdAmount3(new BigDecimal("1000.00"));
        resDTO.setFeeRate3(new BigDecimal("0.02"));
        resDTO.setMinFeeAmount(new BigDecimal("10.00"));
        resDTO.setMaxFeeAmount(new BigDecimal("100.00"));
        resDTO.setDerateMethod("1");
        resDTO.setDerateValue(new BigDecimal("5.00"));
        resDTO.setPrincipalRelief("1");
        resDTO.setPrincipalAmountValue(new BigDecimal("50.00"));
        resDTO.setPrincipalTermValue(new BigDecimal("3.00"));
        resDTO.setFeeDesc("Test Fee Code");
        resDTO.setStatus(Constants.ENABLED);
        resDTO.setUnReceiveFeeTerm(6);
        resDTO.setAnnualInterestRate(new BigDecimal("0.12"));
        resDTO.setGstInterestRate(new BigDecimal("0.10"));
        resDTO.setOneTimeChargeFlag("Y");
        resDTO.setVersionNumber(1L);
        resDTO.setCreateTime(LocalDateTime.now());
        resDTO.setUpdateTime(LocalDateTime.now());
        resDTO.setUpdateBy("TEST_USER");
        return resDTO;
    }
}
